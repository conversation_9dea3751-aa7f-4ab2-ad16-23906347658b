# BakedBot API Endpoints Specification

## Overview

This document outlines the API endpoints required for the BakedBot frontend application. The API uses two authentication methods:

- **API Key Authentication** for public/product endpoints (from `window.BakedBotConfig.apiKey`)
- **Bearer Token Authentication** for user-specific endpoints (Firebase auth tokens)

## Base URLs

- **BakedBot API**: `https://beta.bakedbot.ai/api/`
- **Chat API**: `https://your-chat-api-domain.com/` (from `VITE_BASE_URL`)

---

## 🟢 BakedBot API Endpoints (API Key Auth)

### Products

#### `GET /public/products`

**Purpose**: Fetch paginated list of products  
**Authentication**: Bearer token with API key  
**Query Parameters**:

```typescript
{
  page?: number // Default: 1
}
```

**Response**:

```typescript
{
  products: Array<{
    meta_sku: string;
    retailer_id: string;
    products: Product[];
  }>;
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  }
}
```

#### `GET /public/products/search`

**Purpose**: Search products by query  
**Authentication**: Bearer token with API key  
**Query Parameters**:

```typescript
{
  q: string;      // Search query
  page?: number;  // Default: 1
}
```

**Response**: Same as `/public/products`

### Orders

#### `POST /orders/checkout`

**Purpose**: Process order checkout  
**Authentication**: Bearer token with API key  
**Request Body**:

```typescript
{
  name: string;
  contact_info: {
    email: string;
    phone: string;
  };
  cart: Record<string, {
    sku: string;
    product_name: string;
    quantity: number;
    price: number;
    weight?: string;
  }>;
  total_price: number;
  coupon_code?: string;
}
```

**Response**:

```typescript
{
  success: boolean;
  message: string;
}
```

### Chats

#### `PUT /public/products/chats/{id}` ✅ **AVAILABLE**

**Purpose**: Update/rename chat for authenticated users  
**Authentication**: Bearer token with API key  
**Path Parameters**: `id` (string) - Chat ID  
**Request Body**:

```typescript
{
  name: string; // New chat name
}
```

**Response**:

```typescript
{
  success: boolean;
  message?: string;
}
```

#### `GET /public/products/chats` ✅ **AVAILABLE**

**Purpose**: Get user's chat history  
**Authentication**: Bearer token with API key  
**Response**:

```typescript
{
  data: Array<{
    id: string;
    chat_id: string;
    name: string;
    created_at: string;
    updated_at: string;
    location_id: string;
    agent_ids: string[];
    status: string;
    metadata: object;
    agents: Array<{
      id: string;
      name: string;
      role: string;
      description: string;
      icon: string;
      capabilities: string[];
      disabled?: boolean;
      metadata?: object;
    }>;
  }>;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  }
}
```

#### `GET /public/products/chats/{id}/messages` ✅ **AVAILABLE**

**Purpose**: Get messages for a specific chat  
**Authentication**: Bearer token with API key  
**Path Parameters**: `id` (string) - Chat ID  
**Response**:

```typescript
{
  data: Array<{
    id: string;
    chat_id: string;
    role: "user" | "assistant" | "system";
    content: string;
    created_at: string;
    timestamp: string;
    metadata: object;
  }>;
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  }
}
```

#### `POST /public/products/chats` ✅ **AVAILABLE**

**Purpose**: Send a new message or create new chat  
**Authentication**: Bearer token with API key  
**Request Body**:

```typescript
{
  message: string;
  agent_id: string; // Required - ID of the agent to send message to
  chat_id?: string | null; // Optional - null for new chat
}
```

**Response**:

```typescript
{
  type: "ai";
  content: string;
  message_id: string;
  chat_id: string;
  data?: {
    suggested_next_questions?: string[];
    images?: any[];
    products?: any[];
  };
}
```

#### `DELETE /public/products/chats/{id}` ✅ **AVAILABLE**

**Purpose**: Delete a chat  
**Authentication**: Bearer token with API key  
**Path Parameters**: `id` (string) - Chat ID  
**Response**:

```typescript
{
  success: boolean;
  message?: string;
}
```

#### `POST /public/products/chats/feedback` ✅ **AVAILABLE**

**Purpose**: Record user feedback on messages  
**Authentication**: Bearer token with API key  
**Request Body**:

```typescript
{
  message_id: string;
  feedback_type: "like" | "dislike";
}
```

#### `POST /public/products/chats/retry` ✅ **AVAILABLE**

**Purpose**: Retry a failed message  
**Authentication**: Bearer token with API key  
**Request Body**:

```typescript
{
  message_id: string;
}
```

#### `WS /public/products/chats/ws` ✅ **AVAILABLE**

**Purpose**: Real-time chat messaging  
**Authentication**: API key via query parameter `?apikey=<api_key>&chat_id=<chat_id>`  
**Message Format**:

```typescript
// Send:
{
  message: string;
  voice_type: string;
  location?: string;
}

// Receive:
{
  type: "status" | "ai" | "chat_response" | "final" | "complete";
  content?: string;
  message?: string | object;
  chat_id?: string;
  message_id?: string;
  data?: {
    suggested_next_questions?: string[];
    images?: any[];
    products?: any[];
  };
}
```

#### `GET /public/products/users/theme` ⚠️ **MISSING - NEEDS IMPLEMENTATION**

**Purpose**: Get user's theme settings  
**Authentication**: Bearer token with API key  
**Response**:

```typescript
{
  primary_color?: string;
  secondary_color?: string;
  // ... other theme properties
}
```

#### `POST /public/products/users/theme` ⚠️ **MISSING - NEEDS IMPLEMENTATION**

**Purpose**: Save user's theme settings  
**Authentication**: Bearer token with API key  
**Request Body**:

```typescript
{
  primary_color?: string;
  secondary_color?: string;
  // ... other theme properties
}
```

---

## 🟡 ~~Chat API Endpoints (User Token Auth)~~ **DEPRECATED - USE BAKEDBOT API INSTEAD**

### User Chats

#### `GET /user/chats`

**Purpose**: Get user's chat history  
**Authentication**: Bearer token (Firebase auth)  
**Response**:

```typescript
{
  chats: Array<{
    chat_id: string;
    name: string;
    created_at: string;
    updated_at: string;
  }>;
}
```

### Chat Messages

#### `GET /chat/messages`

**Purpose**: Get messages for a specific chat  
**Authentication**: Bearer token (Firebase auth)  
**Query Parameters**:

```typescript
{
  chat_id: string;
}
```

**Response**:

```typescript
{
  messages: Array<{
    message_id: string;
    type: "human" | "ai";
    content: string;
    created_at: string;
    data?: {
      suggested_next_questions?: string[];
      images?: any[];
      products?: any[];
    };
  }>;
}
```

#### `POST /chat`

**Purpose**: Send a new message  
**Authentication**: Bearer token (Firebase auth)  
**Request Body**:

```typescript
{
  message: string;
  voice_type: string;
  chat_id?: string | null; // null for new chat
  location?: string;
}
```

**Response**:

```typescript
{
  type: "ai";
  content: string;
  message_id: string;
  chat_id: string;
  data?: {
    suggested_next_questions?: string[];
    images?: any[];
    products?: any[];
  };
}
```

#### `DELETE /chat/{chatId}`

**Purpose**: Delete a chat  
**Authentication**: Bearer token (Firebase auth)  
**Path Parameters**: `chatId` (string)  
**Response**:

```typescript
{
  success: boolean;
  message?: string;
}
```

### WebSocket Chat

#### `WS /ws/chat`

**Purpose**: Real-time chat messaging  
**Authentication**: Token via query parameter `?token=<auth_token>&chat_id=<chat_id>`  
**Message Format**:

```typescript
// Send:
{
  message: string;
  voice_type: string;
  location?: string;
}

// Receive:
{
  type: "status" | "ai" | "chat_response" | "final" | "complete";
  content?: string;
  message?: string | object;
  chat_id?: string;
  message_id?: string;
  data?: {
    suggested_next_questions?: string[];
    images?: any[];
    products?: any[];
  };
}
```

### Feedback & Retry

#### `POST /feedback`

**Purpose**: Record user feedback on messages  
**Authentication**: Bearer token (Firebase auth)  
**Request Body**:

```typescript
{
  message_id: string;
  feedback_type: "like" | "dislike";
}
```

#### `POST /retry`

**Purpose**: Retry a failed message  
**Authentication**: Bearer token (Firebase auth)  
**Request Body**:

```typescript
{
  message_id: string;
}
```

### User Settings

#### `GET /users/theme`

**Purpose**: Get user's theme settings  
**Authentication**: Bearer token (Firebase auth)  
**Response**:

```typescript
{
  primary_color?: string;
  secondary_color?: string;
  // ... other theme properties
}
```

#### `POST /users/theme`

**Purpose**: Save user's theme settings  
**Authentication**: Bearer token (Firebase auth)  
**Request Body**:

```typescript
{
  primary_color?: string;
  secondary_color?: string;
  // ... other theme properties
}
```

---

## 📋 Product Type Definition

```typescript
interface Product {
  cann_sku_id: string;
  brand_name: string | null;
  brand_id: number | null;
  url: string;
  image_url: string;
  raw_product_name: string;
  product_name: string;
  raw_weight_string: string | null;
  display_weight: string | null;
  raw_product_category: string | null;
  category: string;
  raw_subcategory: string | null;
  subcategory: string | null;
  product_tags: string[] | null;
  percentage_thc: number | null;
  percentage_cbd: number | null;
  mg_thc: number | null;
  mg_cbd: number | null;
  quantity_per_package: number | null;
  medical: boolean;
  recreational: boolean;
  latest_price: number;
  menu_provider: string;
  retailer_id: string;
  meta_sku: string;
  updated_at: string;
  id: string;
  price: number | null;
  description: string;
  product_id: string;
  discount?: boolean;
  original_price?: number;
  discount_percentage?: number;
  featured?: boolean;
  best_value?: boolean;
}
```

---

## 🔐 Authentication Details

### API Key Authentication (BakedBot API)

```
Authorization: Bearer <API_KEY>
Content-Type: application/json
```

- API key is retrieved from `window.BakedBotConfig.apiKey`
- Used for product and order endpoints

### User Token Authentication (Chat API)

```
Authorization: Bearer <FIREBASE_TOKEN>
Content-Type: application/json
```

- Firebase auth token from user login
- Used for user-specific chat and settings endpoints

---

## ⚠️ Missing Endpoints Summary

### ✅ Most Endpoints Are Available!

**CHAT ENDPOINTS AVAILABLE - Widget configured for API key authentication:**

1. ✅ **`PUT /public/products/chats/{id}`** - Chat renaming functionality
2. ✅ **`GET /public/products/chats`** - Get user's chat history
3. ✅ **`GET /public/products/chats/{id}/messages`** - Get chat messages
4. ✅ **`POST /public/products/chats`** - Send new message/create chat
5. ✅ **`DELETE /public/products/chats/{id}`** - Delete chat
6. ✅ **`POST /public/products/chats/feedback`** - Record feedback
7. ✅ **`POST /public/products/chats/retry`** - Retry messages
8. ✅ **`WS /public/products/chats/ws`** - WebSocket chat

### ⚠️ Potentially Missing (Need to Verify):

9. **`GET /public/products/users/theme`** - Get theme settings
10. **`POST /public/products/users/theme`** - Save theme settings

**All endpoints use API key authentication from `BakedBotConfig`**

### Medium Priority - May Need in Future:

1. Enhanced product search with filters
2. User profile management endpoints
3. Chat export functionality

---

## 🧪 Frontend Implementation Notes

The frontend is configured to:

- Use `VITE_BAKED_BOT_API=https://beta.bakedbot.ai/api/` for BakedBot endpoints
- Use `VITE_BASE_URL` for chat API endpoints
- Automatically handle API key injection from `BakedBotConfig`
- Provide fallback error handling and retry logic
- Support both HTTP and WebSocket chat implementations

---

## 📚 Reference

For the complete frontend implementation, see:

- `src/utils/api.ts` - All API function implementations
- `src/views/ChatWidget/CartContext.tsx` - Checkout implementation
- `src/views/ChatWidget/index.tsx` - Chat and product usage

**API Documentation**: [https://beta.bakedbot.ai/api/api-docs](https://beta.bakedbot.ai/api/api-docs)
