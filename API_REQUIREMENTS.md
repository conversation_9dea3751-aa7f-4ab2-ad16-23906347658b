# BakedBot Headless Ecommerce Menu - API Requirements & Documentation

## Quick Start

### WordPress Shortcode Usage

#### Basic Usage

```html
[bakedbot_menu]
```

#### Advanced Configuration

```html
[bakedbot_menu title="Cannabis Products" enable_filters="true"
enable_search="true" enable_cart="true" layout="grid" products_per_page="12"
theme="light" primary_color="#065f46" secondary_color="#10b981"
category="flower" brand="premium" show_title="true" height="auto" ]
```

### Shortcode Attributes

| Attribute           | Default             | Description               |
| ------------------- | ------------------- | ------------------------- |
| `title`             | "Cannabis Products" | Page/section title        |
| `enable_filters`    | "true"              | Show filter sidebar       |
| `enable_search`     | "true"              | Show search functionality |
| `enable_cart`       | "true"              | Enable cart features      |
| `layout`            | "grid"              | Layout type (grid/list)   |
| `products_per_page` | "12"                | Products per page         |
| `theme`             | "light"             | Theme (light/dark)        |
| `primary_color`     | "#065f46"           | Primary brand color       |
| `secondary_color`   | "#10b981"           | Secondary brand color     |
| `category`          | ""                  | Filter by category        |
| `brand`             | ""                  | Filter by brand           |
| `show_title`        | "true"              | Show/hide title           |
| `height`            | "auto"              | Container height          |

## SEO Features

### Progressive Enhancement

The headless menu is now **SEO-friendly** with progressive enhancement:

1. **Server-Side Rendering**: Initial product grid is rendered server-side for immediate SEO benefits
2. **Structured Data**: Automatic JSON-LD schema markup for products and listings
3. **Meta Tags**: Dynamic Open Graph and meta descriptions
4. **Clean URLs**: SEO-friendly product URLs (`/cannabis-products/product-name/`)
5. **XML Sitemap**: Automatic product sitemap generation
6. **Accessibility**: ARIA labels, semantic HTML, keyboard navigation

### SEO URLs

Products automatically get SEO-friendly URLs:

- Pattern: `/cannabis-products/{product-slug}/?product_id={id}`
- Example: `/cannabis-products/blue-dream-flower/?product_id=123`

### Sitemap Generation

Automatic XML sitemap available at:

- URL: `/bakedbot-sitemap.xml`
- Contains all products with proper lastmod, changefreq, and priority

### Schema Markup

Automatic structured data includes:

- Product schema with name, brand, price, availability
- ItemList schema for product listings
- THC/CBD percentages as additional properties
- Proper brand and offer information

### Accessibility Features

- Screen reader support with proper ARIA labels
- Keyboard navigation for all interactive elements
- Semantic HTML structure (header, main, aside, nav)
- Focus management and skip links
- High contrast support

### Performance Optimizations

- **Lazy Loading**: Images loaded only when in viewport
- **Virtual Scrolling**: For large product lists (1000+ items)
- **Code Splitting**: React components loaded on demand
- **Caching**: Server-side caching of API responses
- **CDN Support**: Static assets served from CDN
- **Compression**: Gzip/Brotli compression for all assets

### Progressive Enhancement Flow

1. **Server renders** initial HTML with products
2. **CSS applies** immediately for styling
3. **JavaScript enhances** with React interactivity
4. **Fallback works** if JavaScript fails to load

### SEO Testing

Test your SEO implementation:

```bash
# Check server-side rendering
curl -H "User-Agent: Googlebot" https://yoursite.com/products/

# Validate structured data
https://search.google.com/test/rich-results

# Test sitemap
https://yoursite.com/bakedbot-sitemap.xml

# Performance testing
https://pagespeed.web.dev/
```

## Overview

This document outlines the API endpoints and enhancements needed to support a full-featured headless ecommerce menu experience. The menu provides product browsing, filtering, search, cart management, and streamlined checkout functionality.

## Base URL

All endpoints should be available under: `https://beta.bakedbot.ai/api/`

## Authentication

- **API Key**: Required for all requests via `Authorization: Bearer {api_key}` header
- **User Token**: Optional for enhanced features via `X-User-Token` header

---

## 1. Enhanced Products API

### GET /public/products

**Purpose**: Fetch paginated products with advanced filtering and sorting

**Current Implementation**: ✅ Available
**Enhancements Needed**: 🔄 Extend existing endpoint

#### Query Parameters

```typescript
interface ProductsQuery {
  // Pagination
  page?: number; // Default: 1
  limit?: number; // Default: 20, Max: 100

  // Filtering
  category?: string[]; // Filter by categories
  brand?: string[]; // Filter by brands
  price_min?: number; // Minimum price
  price_max?: number; // Maximum price
  thc_min?: number; // Minimum THC percentage
  thc_max?: number; // Maximum THC percentage
  cbd_min?: number; // Minimum CBD percentage
  cbd_max?: number; // Maximum CBD percentage
  in_stock?: boolean; // Only show in-stock items
  featured?: boolean; // Only show featured products
  on_sale?: boolean; // Only show discounted products

  // Sorting
  sort?: "name" | "price" | "thc" | "cbd" | "popularity" | "newest";
  direction?: "asc" | "desc";

  // Search
  search?: string; // Search in name, description, brand
}
```

#### Enhanced Response Format

```typescript
interface ProductsResponse {
  success: boolean;
  data: {
    items: ProductGroup[];
    pagination: {
      current_page: number;
      total_pages: number;
      total_items: number;
      per_page: number;
      has_next: boolean;
      has_prev: boolean;
    };
    filters: {
      categories: FilterOption[];
      brands: FilterOption[];
      price_range: [number, number];
      thc_range: [number, number];
      cbd_range: [number, number];
    };
    sorting: {
      current_sort: string;
      current_direction: string;
      available_sorts: SortOption[];
    };
  };
}

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface SortOption {
  value: string;
  label: string;
}
```

---

## 2. Product Search API

### GET /public/products/search

**Purpose**: Advanced product search with autocomplete and suggestions

**Status**: 🆕 New endpoint needed

#### Query Parameters

```typescript
interface SearchQuery {
  q: string; // Search query (required)
  page?: number;
  limit?: number;
  filters?: ProductsQuery; // Same filters as products endpoint
  autocomplete?: boolean; // Return autocomplete suggestions
}
```

#### Response Format

```typescript
interface SearchResponse {
  success: boolean;
  data: {
    items: ProductGroup[];
    pagination: PaginationInfo;
    suggestions?: string[]; // Autocomplete suggestions
    related_searches?: string[]; // Related search terms
    search_metadata: {
      query: string;
      results_count: number;
      search_time_ms: number;
      corrected_query?: string; // If query was auto-corrected
    };
  };
}
```

---

## 3. Cart Management API

### POST /public/cart

**Purpose**: Create or update cart session

**Status**: 🆕 New endpoint needed

#### Request Body

```typescript
interface CartRequest {
  session_id?: string; // Optional existing session
  items: CartItem[];
}

interface CartItem {
  product_id: string;
  variant_id?: string; // For products with variants
  quantity: number;
  custom_options?: Record<string, any>; // Custom product options
}
```

#### Response Format

```typescript
interface CartResponse {
  success: boolean;
  data: {
    session_id: string;
    items: CartItemDetailed[];
    totals: CartTotals;
    expires_at: string; // ISO timestamp
  };
}

interface CartItemDetailed extends CartItem {
  product: Product;
  variant?: ProductVariant;
  line_total: number;
  unit_price: number;
}

interface CartTotals {
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
}
```

### GET /public/cart/{session_id}

**Purpose**: Retrieve existing cart

### PUT /public/cart/{session_id}/items/{item_id}

**Purpose**: Update cart item quantity

### DELETE /public/cart/{session_id}/items/{item_id}

**Purpose**: Remove item from cart

---

## 4. Checkout API

### POST /public/checkout/validate

**Purpose**: Validate checkout data before processing

**Status**: 🆕 New endpoint needed

#### Request Body

```typescript
interface CheckoutValidation {
  cart_session_id: string;
  customer: CustomerInfo;
  shipping_address: Address;
  billing_address?: Address; // Optional, defaults to shipping
  payment_method: PaymentMethod;
  coupon_code?: string;
}

interface CustomerInfo {
  email: string;
  phone?: string;
  first_name: string;
  last_name: string;
}

interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
}

interface PaymentMethod {
  type: "card" | "paypal" | "apple_pay" | "google_pay";
  token?: string; // Payment processor token
}
```

### POST /public/checkout/process

**Purpose**: Process the complete checkout

#### Request Body

```typescript
interface CheckoutRequest extends CheckoutValidation {
  terms_accepted: boolean;
  marketing_consent?: boolean;
  special_instructions?: string;
}
```

#### Response Format

```typescript
interface CheckoutResponse {
  success: boolean;
  data: {
    order_id: string;
    order_number: string;
    status: "pending" | "confirmed" | "processing";
    payment_status: "pending" | "paid" | "failed";
    total: number;
    currency: string;
    estimated_delivery?: string;
    tracking_url?: string;
    receipt_url: string;
  };
}
```

---

## 5. Coupons & Discounts API

### POST /public/coupons/validate

**Purpose**: Validate and apply coupon codes

**Status**: 🆕 New endpoint needed

#### Request Body

```typescript
interface CouponValidation {
  code: string;
  cart_session_id: string;
}
```

#### Response Format

```typescript
interface CouponResponse {
  success: boolean;
  data: {
    valid: boolean;
    coupon?: {
      code: string;
      type: "percentage" | "fixed_amount" | "free_shipping";
      value: number;
      description: string;
      minimum_order?: number;
      expires_at?: string;
    };
    discount_amount?: number;
    error_message?: string;
  };
}
```

---

## 6. Product Recommendations API

### GET /public/products/{product_id}/recommendations

**Purpose**: Get related/recommended products

**Status**: 🆕 New endpoint needed

#### Query Parameters

```typescript
interface RecommendationsQuery {
  type?: "related" | "frequently_bought" | "similar" | "upsell";
  limit?: number; // Default: 6
}
```

---

## 7. Inventory & Availability API

### POST /public/products/availability

**Purpose**: Check real-time availability for multiple products

**Status**: 🆕 New endpoint needed

#### Request Body

```typescript
interface AvailabilityRequest {
  items: {
    product_id: string;
    variant_id?: string;
    quantity: number;
  }[];
  location?: {
    latitude: number;
    longitude: number;
  };
}
```

#### Response Format

```typescript
interface AvailabilityResponse {
  success: boolean;
  data: {
    items: {
      product_id: string;
      variant_id?: string;
      available: boolean;
      quantity_available?: number;
      estimated_restock?: string;
      location_specific?: boolean;
    }[];
  };
}
```

---

## 8. User Preferences API

### GET /public/user/preferences

**Purpose**: Get user preferences for personalization

**Status**: 🆕 New endpoint needed
**Authentication**: User token required

### PUT /public/user/preferences

**Purpose**: Update user preferences

#### Request Body

```typescript
interface UserPreferences {
  favorite_categories?: string[];
  preferred_brands?: string[];
  thc_preference?: "low" | "medium" | "high";
  cbd_preference?: "low" | "medium" | "high";
  price_range?: [number, number];
  delivery_preferences?: {
    preferred_time?: string;
    special_instructions?: string;
  };
}
```

---

## 9. Analytics & Tracking API

### POST /public/analytics/events

**Purpose**: Track user interactions for analytics

**Status**: 🆕 New endpoint needed

#### Request Body

```typescript
interface AnalyticsEvent {
  event_type:
    | "product_view"
    | "add_to_cart"
    | "remove_from_cart"
    | "search"
    | "filter_applied"
    | "checkout_started"
    | "checkout_completed"
    | "page_view";
  product_id?: string;
  search_query?: string;
  filter_data?: Record<string, any>;
  cart_value?: number;
  session_id?: string;
  user_agent?: string;
  timestamp: string;
}
```

---

## 10. Store Locations API

### GET /public/locations

**Purpose**: Get store locations for pickup/delivery

**Status**: 🆕 New endpoint needed

#### Query Parameters

```typescript
interface LocationsQuery {
  latitude?: number;
  longitude?: number;
  radius?: number; // Miles/KM radius
  services?: ("pickup" | "delivery")[];
}
```

---

## Implementation Priority

### Phase 1 (Critical - Week 1)

1. ✅ Enhanced Products API with filtering
2. 🆕 Cart Management API (full CRUD)
3. 🆕 Basic Checkout API

### Phase 2 (Important - Week 2)

4. 🆕 Product Search API with autocomplete
5. 🆕 Coupons & Discounts API
6. 🆕 Inventory & Availability API

### Phase 3 (Nice to Have - Week 3)

7. 🆕 Product Recommendations API
8. 🆕 User Preferences API
9. 🆕 Analytics & Tracking API
10. 🆕 Store Locations API

---

## Error Handling

All endpoints should follow consistent error response format:

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  timestamp: string;
}
```

### Common Error Codes

- `INVALID_API_KEY`: Invalid or missing API key
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `PRODUCT_NOT_FOUND`: Product doesn't exist
- `CART_EXPIRED`: Cart session expired
- `INSUFFICIENT_INVENTORY`: Not enough stock
- `INVALID_COUPON`: Coupon code invalid or expired
- `CHECKOUT_FAILED`: Checkout processing failed

---

## Rate Limiting

- **Products API**: 100 requests/minute per API key
- **Search API**: 60 requests/minute per API key
- **Cart API**: 200 requests/minute per session
- **Checkout API**: 10 requests/minute per session
- **Analytics API**: 1000 requests/minute per API key

---

## Caching Strategy

- **Products**: Cache for 5 minutes
- **Search Results**: Cache for 2 minutes
- **Cart Data**: No caching (real-time)
- **Availability**: Cache for 30 seconds
- **Recommendations**: Cache for 1 hour

---

## Testing Requirements

Each endpoint should include:

1. Unit tests for business logic
2. Integration tests with database
3. API contract tests
4. Performance tests for high-load scenarios
5. Security tests for authentication/authorization

---

## Documentation

- OpenAPI/Swagger documentation for all endpoints
- Postman collection for testing
- SDK examples in JavaScript/TypeScript
- WordPress plugin integration examples
