# Events Search API Specification

## Overview

This document specifies the implementation requirements for the events search API endpoint that enables location-based event discovery in the BakedBot frontend application.

## Endpoint

**POST** `/misc/events/search`

### Authentication

- **API Key**: Required via `Authorization: Bearer {api_key}` header
- **User Token**: Optional via `X-User-Token` header for personalized results

### Request Body

```typescript
interface EventsSearchRequest {
  query?: string; // Optional: Search query for event name
  city?: string; // Optional: Filter by city
  state?: string; // Optional: Filter by state (preferably 2-letter abbreviation)
  limit?: number; // Optional: Number of results per page (default: 20, max: 100)
  page?: number; // Optional: Page number for pagination (default: 1)
}
```

### Request Example

```json
{
  "query": "music festival",
  "city": "Chicago",
  "state": "IL",
  "limit": 20,
  "page": 1
}
```

### Response Format

```typescript
interface EventsSearchResponse {
  events: Event[];
  total_count: number;
  page: number;
  limit: number;
  total_pages: number;
}

interface Event {
  id: string; // Unique identifier
  event_id: string; // External event ID if applicable
  event_name: string; // Event title/name
  category: string[]; // Event categories (e.g., ["music", "festival"])
  start_time: string; // ISO 8601 datetime string
  timezone: string; // Timezone (e.g., "America/Chicago")
  host: string; // Event organizer/host name
  starting_price: string; // Price display string (e.g., "$25", "Free")
  address: string; // Street address
  city: string; // City name
  state: string; // State abbreviation (e.g., "IL")
  postal_code: string; // ZIP/postal code
  image: string; // Event image URL
  url: string; // Event details/tickets URL
  source: string; // Data source identifier
}
```

### Response Example

```json
{
  "events": [
    {
      "id": "evt_12345",
      "event_id": "ext_67890",
      "event_name": "Summer Music Festival",
      "category": ["music", "festival", "outdoor"],
      "start_time": "2024-07-15T19:00:00Z",
      "timezone": "America/Chicago",
      "host": "Music Events Co.",
      "starting_price": "$45",
      "address": "123 Festival Way",
      "city": "Chicago",
      "state": "IL",
      "postal_code": "60601",
      "image": "https://example.com/images/festival.jpg",
      "url": "https://example.com/tickets/festival",
      "source": "eventbrite"
    }
  ],
  "total_count": 156,
  "page": 1,
  "limit": 20,
  "total_pages": 8
}
```

## Implementation Requirements

### Search Logic

1. **Text Search**: When `query` is provided, search in:

   - `event_name` (primary match)
   - `category` array
   - `host` name
   - Event description (if available)

2. **Location Filtering**:

   - When `city` is provided, filter events in that city
   - When `state` is provided, filter events in that state
   - Support both full state names and abbreviations
   - Case-insensitive matching

3. **Sorting**: Default sort by:
   - Relevance score (for text queries)
   - Start date (upcoming events first)
   - Distance (if user location is known)

### Data Sources

The endpoint should aggregate events from multiple sources:

- **Eventbrite API**
- **Ticketmaster API**
- **Facebook Events**
- **Local event databases**
- **Manual event entries**

### Caching Strategy

- Cache search results for 15 minutes
- Cache popular location queries for 1 hour
- Invalidate cache when new events are added

### Error Handling

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

Common error codes:

- `INVALID_API_KEY`: Missing or invalid API key
- `INVALID_PARAMETERS`: Invalid request parameters
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `SERVICE_UNAVAILABLE`: External API failures

### Rate Limiting

- 60 requests per minute per API key
- 300 requests per hour per API key

### Performance Requirements

- Response time: < 500ms for cached results
- Response time: < 2s for fresh queries
- Support concurrent requests: 100+ per second

## Frontend Integration

The frontend expects:

1. **Loading States**: API calls should complete within 2 seconds
2. **Error Handling**: Graceful fallback when API is unavailable
3. **Image Fallbacks**: Default image when event image fails to load
4. **Date Formatting**: ISO 8601 datetime strings for proper parsing

## Testing Requirements

### Unit Tests

- Search query parsing
- Location filtering logic
- Pagination calculations
- Error handling scenarios

### Integration Tests

- External API connectivity
- Database query performance
- Cache invalidation
- Rate limiting enforcement

### API Contract Tests

- Request/response schema validation
- Authentication flows
- Error response formats

## Security Considerations

1. **Input Validation**: Sanitize all search parameters
2. **Rate Limiting**: Prevent abuse with proper limits
3. **API Key Validation**: Verify API keys against allowed domains
4. **SQL Injection**: Use parameterized queries
5. **XSS Prevention**: Sanitize event data from external sources

## Monitoring & Analytics

Track the following metrics:

- Search query frequency
- Popular locations
- API response times
- Error rates by source
- Cache hit rates

## Deployment Checklist

- [ ] API endpoint implemented with proper authentication
- [ ] Database schema for events created
- [ ] External API integrations configured
- [ ] Caching layer implemented (Redis recommended)
- [ ] Rate limiting configured
- [ ] Error logging and monitoring setup
- [ ] API documentation published
- [ ] Frontend integration tested
- [ ] Load testing completed
- [ ] Production deployment verified

## Future Enhancements

1. **Personalized Results**: Use user preferences and history
2. **Geolocation Search**: Radius-based search with lat/lng
3. **Event Recommendations**: ML-based suggestions
4. **Real-time Updates**: WebSocket notifications for event changes
5. **Multi-language Support**: Localized event data
6. **Advanced Filters**: Price range, date range, category filters
