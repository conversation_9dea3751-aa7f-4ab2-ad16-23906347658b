# BakedBot WordPress Plugin Development

This document outlines the development and deployment process for the BakedBot WordPress plugin.

## Overview

The BakedBot WordPress plugin allows you to integrate the BakedBot AI chatbot into any WordPress site. The plugin now includes federated authentication, allowing users to maintain a single identity across multiple dispensary sites. Additionally, it features a **headless ecommerce menu** for creating SEO-friendly product listings and shopping experiences.

## Features

### 1. AI Chatbot

- Intelligent conversational AI for customer support
- Federated authentication across multiple sites
- Customizable appearance and behavior

### 2. Headless Ecommerce Menu

- **SEO-optimized** product listings with server-side rendering
- **Mobile-responsive** design with touch-friendly interactions
- **Progressive enhancement** for fast loading and accessibility
- Advanced filtering and search capabilities
- Shopping cart functionality
- Multiple layout options (grid/list view)

## Development Workflow

### Environment Setup

1. Make sure Node.js (v20.x+) and npm (v10.x+) are installed
2. Clone the repository: `git clone https://github.com/your-org/bakedbot.git`
3. Install dependencies: `npm install`

### Development Process

1. Make changes to the React application as needed
2. Implement the changes outlined in the [TODO.md](./TODO.md) file
3. Test your changes using `npm run dev`
4. Build for development: `npm run build`

### WordPress Plugin Generation

The project includes scripts to automatically generate the WordPress plugin:

```bash
# Build React app and prepare WordPress plugin
npm run wp:dev

# Build for production and prepare WordPress plugin
npm run wp:prod

# Only prepare WordPress plugin (uses existing build)
npm run wp:prepare

# Package the plugin as a ZIP file
npm run wp:package
```

## Plugin Architecture

The WordPress plugin consists of:

1. **PHP Files**: Define the WordPress integration, settings pages, and AJAX endpoints
2. **React Build**: The compiled React application in the `dist` folder
3. **Configuration**: WordPress settings for appearance and behavior

### Key WordPress Files

- `bakedbot-chatbot.php`: Main plugin file with WordPress hooks and integration
- `index.php`: Empty file for security
- `README.txt`: WordPress plugin readme in their required format

## Federated Authentication System

The plugin implements a federated authentication system that:

1. Recognizes users across different WordPress sites
2. Syncs user data between WordPress and BakedBot central services
3. Maintains consistent user experience across dispensary sites

### Authentication Modes

The plugin supports three authentication modes:

1. **Federated** (default): Users can log in with one account across multiple sites
2. **WordPress Only**: Uses only WordPress authentication (site-specific)
3. **BakedBot Only**: Uses only BakedBot authentication (ignores WordPress)

## Deployment Instructions

### 1. Build and Package

```bash
# Build production version and generate plugin ZIP
npm run wp:prod
```

### 2. Install on WordPress

1. Log into the WordPress admin dashboard
2. Go to **Plugins > Add New > Upload Plugin**
3. Select the generated `bakedbot-chatbot.zip` file
4. Click **Install Now** and then **Activate**

**Note:** Upon activation, the plugin automatically creates essential pages for your site:

- **BakedBot Products** (`/bakedbot-products/`) - Product grid with search and filters
- **Cannabis Menu** (`/cannabis-menu/`) - Complete product menu in list format
- **Product Finder** (`/product-finder/`) - AI-powered product discovery
- **BakedBot Assistant** (`/bakedbot-chat/`) - Direct access to the chatbot

These pages are also automatically added to your site's main navigation menu if one exists.

### 3. Configure the Plugin

1. Go to **BakedBot** in the WordPress admin menu
2. Configure appearance settings (colors, position)
3. Enter your API key and site identifier
4. Select the desired authentication mode
5. Save your settings

### 4. Using the Plugin

#### AI Chatbot

The plugin automatically adds the chatbot to all pages. You can also embed it directly in content using the shortcode:

```
[bakedbot_chatbot]
```

With attributes:

```
[bakedbot_chatbot position="left" width="500px" height="700px"]
```

#### Headless Ecommerce Menu

The headless menu can be embedded anywhere on your site using the shortcode:

```
[bakedbot_headless_menu]
```

##### Basic Usage Examples

**Simple product grid:**

```
[bakedbot_headless_menu]
```

**Customized appearance:**

```
[bakedbot_headless_menu
  layout="grid"
  products_per_page="12"
  primary_color="#065f46"
  secondary_color="#10b981"
]
```

**Search and filters enabled:**

```
[bakedbot_headless_menu
  enable_search="true"
  enable_filters="true"
  enable_cart="true"
  layout="list"
]
```

**Custom theme:**

```
[bakedbot_headless_menu
  theme="dark"
  products_per_page="24"
  layout="grid"
]
```

##### Available Shortcode Parameters

| Parameter           | Type    | Default     | Description                               |
| ------------------- | ------- | ----------- | ----------------------------------------- |
| `layout`            | string  | `"grid"`    | Layout type: `"grid"` or `"list"`         |
| `products_per_page` | number  | `12`        | Number of products to display per page    |
| `enable_search`     | boolean | `true`      | Enable/disable search functionality       |
| `enable_filters`    | boolean | `true`      | Enable/disable filtering options          |
| `enable_cart`       | boolean | `true`      | Enable/disable shopping cart              |
| `theme`             | string  | `"light"`   | Theme: `"light"`, `"dark"`, or `"custom"` |
| `primary_color`     | string  | `"#065f46"` | Primary brand color (hex format)          |
| `secondary_color`   | string  | `"#10b981"` | Secondary accent color (hex format)       |

##### SEO Benefits

The headless menu provides significant SEO advantages:

1. **Server-Side Rendering (SSR)**: Products are rendered on the server for search engines
2. **Structured Data**: Automatic JSON-LD markup for rich snippets
3. **SEO-Friendly URLs**: Clean product URLs like `/products/product-name/`
4. **Fast Loading**: Progressive enhancement ensures instant page loads
5. **Mobile Optimization**: Fully responsive design improves mobile search rankings
6. **Sitemap Integration**: Automatic product sitemap generation

##### Advanced Usage

**E-commerce Landing Page:**

```html
<div class="product-showcase">
  <h1>Our Premium Cannabis Products</h1>
  <p>Discover our curated selection of high-quality products.</p>

  [bakedbot_headless_menu layout="grid" products_per_page="16"
  enable_search="true" enable_filters="true" theme="custom"
  primary_color="#2d5a27" secondary_color="#4ade80" ]
</div>
```

**Category-Specific Product Page:**

```html
<div class="flower-products">
  <h2>Premium Flower Selection</h2>

  [bakedbot_headless_menu layout="list" products_per_page="20"
  enable_cart="true" category_filter="flower" ]
</div>
```

##### Mobile Responsiveness

The headless menu is fully optimized for mobile devices:

- **Touch-friendly** interface with appropriate button sizes
- **Off-canvas navigation** for filters and cart on mobile
- **Responsive grid** that adapts to screen size
- **Swipe gestures** for product image galleries
- **Fast loading** on slower mobile connections

##### Performance Features

- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Lazy Loading**: Images load as needed
- **Caching**: Built-in caching for better performance
- **CDN Ready**: Optimized for content delivery networks
- **Minimal Bundle Size**: Only loads necessary code

## Development Guidelines

1. **WordPress Standards**: Follow WordPress coding standards for PHP files
2. **React Best Practices**: Use functional components and hooks
3. **Authentication Security**: Ensure token handling follows security best practices
4. **API Integration**: Use consistent error handling and retry mechanisms
5. **SEO Optimization**: Maintain server-side rendering for headless components
6. **Responsive Design**: Ensure all components work across device sizes

## Uninstallation

When you deactivate the BakedBot plugin, it will automatically:

1. **Remove auto-created pages** - All pages created during installation are moved to trash
2. **Clean navigation menus** - Remove BakedBot pages from all menus where they were added
3. **Preserve your settings** - Plugin settings are kept in case you reactivate later
4. **Clean up rewrite rules** - Remove SEO-friendly URL patterns

**Important:** Only pages that were automatically created by the plugin are removed. Any pages you created manually with BakedBot shortcodes will remain untouched.

## Troubleshooting

### Plugin Not Appearing

1. Check that your theme supports `wp_footer()` hooks
2. Verify JavaScript console for errors
3. Confirm the plugin is activated in WordPress admin

### Authentication Issues

1. Verify API key and site identifier
2. Check cross-domain cookies settings
3. Ensure SSL is properly configured

### Headless Menu Issues

1. **Products not loading**: Verify API key and product sync status
2. **SEO not working**: Check if server-side rendering is enabled
3. **Mobile layout issues**: Clear cache and test on actual devices
4. **Search not working**: Ensure search endpoints are accessible

### Performance Optimization

1. **Enable caching** in your WordPress hosting
2. **Use a CDN** for static assets
3. **Optimize images** before uploading to product catalog
4. **Enable GZIP compression** on your server

## Resources

- [WordPress Plugin Developer Handbook](https://developer.wordpress.org/plugins/)
- [BakedBot API Documentation](https://api.bakedbot.ai/docs)
- [React Documentation](https://reactjs.org/docs/getting-started.html)
- [SEO Best Practices for E-commerce](https://developers.google.com/search/docs/advanced/ecommerce)
- [Web Performance Guidelines](https://web.dev/performance/)
