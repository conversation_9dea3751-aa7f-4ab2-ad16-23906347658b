# BakedBot Federated Authentication System

This repository contains the implementation of the BakedBot Federated Authentication System, which allows users to authenticate across multiple dispensary websites using a single account.

## Core Components

The federated authentication system consists of the following core components:

1. **ConfigProvider**: Reads configuration from `window.BakedBotConfig` (provided by WordPress) or uses fallback values.
2. **FederatedAuthProvider**: Manages authentication across different modes (federated, WordPress, BakedBot).
3. **LoginForm**: Provides different login options based on the current authentication mode.
4. **TokenStorage**: Secure utility for storing authentication tokens with encryption and expiration handling.
5. **ThemeProvider**: Reads theme settings from WordPress or uses fallback values.

## Authentication Modes

The system supports three authentication modes:

1. **Federated Authentication**: Users can log in with the same BakedBot account across different dispensary websites.
2. **WordPress Authentication**: Uses only WordPress users without cross-site functionality.
3. **BakedBot Authentication**: Uses only BakedBot's Firebase authentication without WordPress integration.

## Implementation Status

### Completed

- ✅ Core architecture for federated authentication
- ✅ ConfigProvider with support for reading from WordPress settings
- ✅ FederatedAuthProvider with multi-source authentication
- ✅ Updated LoginForm with contextual authentication options
- ✅ User synchronization between WordPress and BakedBot
- ✅ Token verification across sites
- ✅ Logout flow for different authentication modes
- ✅ Integration with WordPress AJAX endpoints
- ✅ Secure token storage with encryption and expiration
- ✅ Token refresh mechanism for longer sessions
- ✅ "Remember me across dispensaries" option for federated mode
- ✅ Theme management with WordPress integration
- ✅ Conditional UI based on disableThemeSettings configuration

### In Progress

- 🔄 Profile management UI

### Not Started Yet

- ❌ Comprehensive testing for all authentication flows
- ❌ Documentation for users and developers
- ❌ Cross-site preferences synchronization

## How It Works

1. The `ConfigProvider` reads the authentication mode and other settings from `window.BakedBotConfig`.
2. The `ThemeProvider` reads theme colors and settings from `window.BakedBotTheme`.
3. The `FederatedAuthProvider` initializes authentication based on the configured mode:
   - In WordPress mode: Fetches user data from WordPress via AJAX
   - In Federated mode: Checks both WordPress and Firebase
   - In BakedBot mode: Uses Firebase authentication
4. The `LoginForm` displays different login options based on the current mode.
5. When a user logs in, the system:
   - Securely stores authentication tokens with encryption
   - Respects the "Remember me across dispensaries" setting for federated mode
   - Synchronizes user accounts if configured to do so
   - Automatically refreshes tokens before they expire

## Token Storage Security

The system implements secure token storage with:

1. **Encryption**: Tokens are encrypted before being stored in localStorage
2. **Automatic Expiration**: Tokens have an expiration time and are automatically removed when expired
3. **Refresh Mechanism**: Tokens are refreshed automatically when they are about to expire
4. **Cross-Site Preference**: The system respects the user's choice about being remembered across different dispensaries

## Theme Management

The theme system provides:

1. **WordPress Integration**: Reads theme colors directly from `window.BakedBotTheme`
2. **CSS Variables**: Applies theme colors as CSS variables for consistent styling
3. **Fallback Values**: Uses default colors when WordPress theme settings are not available
4. **Configurable UI**: Hides theme settings when `disableThemeSettings` is set to true

## Setup and Configuration

For a full implementation, the WordPress plugin must be configured with the appropriate authentication mode and API credentials. The React app reads these settings from `window.BakedBotConfig` provided by the WordPress plugin.

### Configuration Options

- `authMode`: 'federated', 'wordpress', or 'bakedbot'
- `syncUsers`: true/false to enable user synchronization
- `siteIdentifier`: unique identifier for the current dispensary
- `apiUrl`: URL for the BakedBot API
- `wordPressAjaxUrl`: URL for WordPress AJAX endpoints
- `disableThemeSettings`: true/false to hide theme settings UI

## Next Steps

1. Create a Profile component to replace theme settings when appropriate
2. Write comprehensive tests for all authentication flows
3. Complete documentation for users and developers
4. Implement cross-site preferences synchronization

## Development Guidelines

When working on this authentication system, keep the following in mind:

1. Security is paramount - all tokens must be stored securely
2. User experience should be seamless across authentication modes
3. Code should be well-documented for future maintenance
4. Error handling should be robust and user-friendly

npm install
npm run dev

browse to
http://localhost:5173/widget/34
