=== BakedBot Chatbot ===
Contributors: bakedbot
Tags: chatbot, budtender, ai, cannabis, bud finder, customer service, live chat
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.5.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Add the BakedBot AI budtender chatbot to your WordPress site with cross-dispensary user authentication.

== Description ==

BakedBot is an AI-powered budtender chatbot for cannabis websites. It helps your customers find the perfect products based on their needs, preferences, and desired effects.

= Key Features =

* **AI-Powered Recommendations**: Intelligent product recommendations based on customer preferences
* **Customizable Appearance**: Match your brand's colors and style
* **Cross-Dispensary Authentication**: Users can authenticate once and use their profile across multiple dispensaries
* **Mobile-Friendly**: Fully responsive design works on all devices
* **Easy Integration**: Simple setup with shortcodes for specific pages or floating widget for site-wide use

= Who is BakedBot For? =

* **Dispensaries**: Provide 24/7 budtender assistance even when your staff is busy
* **Cannabis E-commerce**: Increase conversions with personalized product recommendations
* **Cannabis Brands**: Help customers find your products at nearby dispensaries

= Privacy & Compliance =

BakedBot is built with privacy in mind. User data is handled according to industry best practices, and the plugin is designed to help businesses stay compliant with regulations.

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/bakedbot-chatbot` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Use the Settings->BakedBot screen to configure the plugin.
4. Enter your API Key and Site Identifier from your BakedBot dashboard.
5. Customize the appearance to match your brand.

= Using the Shortcode =

You can embed the chatbot on specific pages using the shortcode:

`[bakedbot_chatbot width="400px" height="600px" position="right" auto_open="false"]`

== Frequently Asked Questions ==

= Do I need a BakedBot account to use this plugin? =

Yes, you'll need to sign up for a BakedBot account at [bakedbot.ai](https://bakedbot.ai) to get your API key and site identifier.

= Is BakedBot HIPAA compliant? =

BakedBot is designed with privacy in mind, but you should consult with your compliance officer about your specific implementation needs.

= Can I customize the chatbot's appearance? =

Yes, you can customize the colors, position, and other display options in the plugin settings.

= Can users authenticate with their WordPress accounts? =

Yes, BakedBot can use WordPress authentication to provide a seamless experience for your users.

= How does the cross-dispensary authentication work? =

Users can create a BakedBot account once and use it across multiple dispensaries that use BakedBot, allowing for consistent recommendations based on their preferences.

== Screenshots ==

1. BakedBot chatbot interface
2. Admin settings page
3. Customization options
4. Mobile view of the chatbot

== Changelog ==

= 1.5.0 =
* Updated version number.

= 1.0.0 =
* Initial release

== Upgrade Notice ==

= 1.5.0 =
* Important updates in this version.

= 1.0.0 =
Initial release of the BakedBot Chatbot plugin.

== Support ==

For support, please visit [bakedbot.ai/support](https://bakedbot.ai/support) or contact <NAME_EMAIL>. 