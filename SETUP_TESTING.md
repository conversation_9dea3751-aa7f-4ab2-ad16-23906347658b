# Testing Setup Guide for HeadlessShop

This guide will help you set up and test the new HeadlessShop component with proper environment variables.

## 🚀 Quick Setup

### 1. Create Environment File

Copy the example environment file and configure your API credentials:

```bash
cp env.example .env
```

### 2. Configure Required Variables

Edit your `.env` file with your actual BakedBot API credentials:

```bash
# Required for HeadlessShop to work
VITE_BAKED_BOT_API=https://beta.bakedbot.ai/api
VITE_BAKED_BOT_API_KEY=your-actual-api-key-here

# Required for other components
VITE_BASE_URL=https://your-chat-api-domain.com
```

### 3. Start Development Server

```bash
npm run dev
```

### 4. Test the New Routes

Open your browser and navigate to:

- **Homepage Experience**: `http://localhost:5173/shop`
- **Menu-Only Experience**: `http://localhost:5173/menu`

## 🧪 Available Test Routes

| Route               | Description              | Features                                        |
| ------------------- | ------------------------ | ----------------------------------------------- |
| `/shop`             | Full homepage experience | Categories, featured products, promotion banner |
| `/shop/:customerID` | Customer-specific shop   | Same as `/shop` with customer context           |
| `/menu`             | Direct menu access       | Existing behavior - straight to product menu    |
| `/menu/:customerID` | Customer-specific menu   | Same as `/menu` with customer context           |

## 🔧 Configuration

The routes are now configured with environment variables:

```tsx
// Automatically uses your .env configuration
<HeadlessShop
  apiKey={import.meta.env.VITE_BAKED_BOT_API_KEY} // From .env
  enableHomepage={true}
  defaultView="homepage"
  theme="light"
  primaryColor="#065f46"
  secondaryColor="#10b981"
  showPromotion={true}
  promotionTitle="Premium Cannabis Marketplace"
  promotionSubtitle="Discover our curated selection of premium cannabis products"
/>
```

## 🎯 What to Test

### Homepage Features (`/shop`)

- [ ] **Hero Banner** - Shows promotion content
- [ ] **Category Cards** - Navigate to filtered menu
- [ ] **Featured Products** - Product cards with cart functionality
- [ ] **Category Sections** - Products grouped by category
- [ ] **Popular Brands** - Brand cards with navigation
- [ ] **Search Bar** - Navigates to menu with search
- [ ] **Cart Functionality** - Add to cart, view cart sidebar
- [ ] **Responsive Design** - Test on mobile/tablet

### Menu Features (`/menu`)

- [ ] **Direct Menu Access** - Bypasses homepage
- [ ] **All Existing Functionality** - Filters, search, pagination
- [ ] **Cart Integration** - Same cart behavior
- [ ] **Navigation Breadcrumb** - Back to home button (when accessed from shop)

### Navigation Flow

- [ ] **Homepage to Menu** - Category clicks filter products
- [ ] **Menu to Homepage** - Back button returns to homepage
- [ ] **Product Clicks** - Navigate to menu with product modal
- [ ] **Search Flow** - Search navigates to filtered menu
- [ ] **Cart Persistence** - Cart state maintained across navigation

## 🐛 Troubleshooting

### No Products Loading

**Issue**: Homepage or menu shows empty state
**Solution**:

1. Check your `VITE_BAKED_BOT_API_KEY` is correct
2. Verify network connectivity to the API
3. Check browser console for API errors

### Environment Variables Not Working

**Issue**: `import.meta.env.VITE_BAKED_BOT_API_KEY` is undefined
**Solution**:

1. Make sure your `.env` file is in the project root
2. Restart the development server after changing `.env`
3. Verify variable names start with `VITE_`

### Styling Issues

**Issue**: Components look broken or unstyled
**Solution**:

1. Check CSS files are loading correctly
2. Verify no conflicting CSS
3. Clear browser cache and reload

### Navigation Not Working

**Issue**: Clicking categories/products doesn't navigate
**Solution**:

1. Check browser console for JavaScript errors
2. Verify React Router is working on other routes
3. Test in incognito mode to rule out extensions

## 🌐 API Endpoints Used

The HeadlessShop components use these API endpoints:

- `GET /public/products` - Fetch products with pagination/filtering
- `GET /public/products/filters` - Get available categories, brands, subcategories
- `POST /orders/checkout` - Process cart checkout

## 📱 Mobile Testing

Test responsive behavior on different screen sizes:

- **Desktop**: Full layout with sidebar navigation
- **Tablet**: Adapted grid layouts
- **Mobile**: Single/double column layouts with optimized touch interactions

## 🎨 Customization

You can customize the appearance by modifying the props in `App.tsx`:

```tsx
<HeadlessShop
  theme="dark" // Change to dark theme
  primaryColor="#7c3aed" // Custom brand color
  secondaryColor="#a855f7" // Custom secondary color
  promotionTitle="Your Store" // Custom promotion text
  featuredProductsCount={6} // Show fewer featured products
  popularCategoriesCount={4} // Show fewer categories
  showPromotion={false} // Hide promotion banner
/>
```

## 🔄 Development Workflow

1. **Make changes** to components in `src/views/`
2. **Test in browser** - Changes auto-reload
3. **Check console** for any errors or warnings
4. **Test different routes** to verify navigation
5. **Test responsive design** with browser dev tools

## 📞 Support

If you encounter issues:

1. Check this troubleshooting guide
2. Review browser console errors
3. Verify API credentials and connectivity
4. Test with a fresh `.env` configuration

Happy testing! 🎉
