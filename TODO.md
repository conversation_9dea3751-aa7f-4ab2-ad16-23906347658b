# BakedBot Federated Authentication Implementation TODO

This document outlines all the changes needed in the React app to implement the federated authentication system for use across multiple dispensary sites.

## Architecture Changes

- [x] Create a `FederatedAuthProvider` component to manage cross-site authentication
  - [x] Implement support for reading from `window.BakedBotConfig.authMode`
  - [x] Handle different authentication modes: 'federated', 'wordpress', 'bakedbot'
- [x] Implement token storage and management with proper security
  - [x] Use localStorage with encryption for auth tokens
  - [x] Add token expiration handling
- [x] Create API client for communicating with the BakedBot central authentication service
  - [x] Implement API routes for auth verification
  - [x] Add error handling and retry logic

## Authentication Flow Changes

- [x] Modify login flow to support three authentication modes: Federated, WordPress, and BakedBot Only
  - [x] Update LoginForm.tsx to check authMode before rendering options
  - [x] Add WordPress login option when in 'wordpress' mode
  - [x] Keep Firebase auth for 'bakedbot' mode
- [x] Implement user synchronization between WordPress and BakedBot
  - [x] Create utility to sync user data after login
  - [x] Store association between WordPress and BakedBot IDs
- [x] Add support for cross-site token verification
  - [x] Implement API call to verify tokens from other sites
  - [x] Add cross-domain cookie handling if needed
- [x] Update logout flow to handle federated sessions
  - [x] Clear all auth tokens on logout
  - [x] Call WordPress logout endpoint when needed
- [x] Update legacy authentication code for compatibility
  - [x] Modify useAuth hook to use FederatedAuthContext
  - [x] Ensure backward compatibility for existing components

## UI Changes

- [x] Update login modal to include federated authentication options
  - [x] Add "Log in with BakedBot" option for federated mode
  - [x] Show appropriate login methods based on authMode
  - [x] Update Google auth button styling
- [x] Modify settings UI to replace theme settings with profile management
  - [x] Create new Profile component
  - [x] Hide theme settings when disableThemeSettings is true
- [x] Add visual indication of account linking status
  - [x] Add badge or icon showing linked status
  - [x] Show sync status in profile
- [x] Create user profile section with sync status information
  - [x] Display basic user info (name, email)
  - [x] Show connected accounts
  - [x] Add options to manage linked accounts
- [x] Add "Remember me across dispensaries" option for federated mode
  - [x] Add checkbox to login form
  - [x] Store preference in token
- [x] Update Sidebar component with user profile information
  - [x] Display user avatar and name in sidebar when logged in
  - [x] Show account sync status in the sidebar
  - [x] Improve sidebar styling for better user experience

## API Integration

- [x] Implement `verifyToken` API endpoint integration
  - [x] Create service to call WordPress AJAX endpoint
  - [x] Handle responses and errors
- [x] Add `syncUser` functionality to link WordPress and BakedBot accounts
  - [x] Call WordPress AJAX endpoint on login if needed
  - [x] Update local user data after syncing
- [x] Update user profile APIs to support federated user data
  - [x] Add bakedBotId field to user profile
  - [x] Store cross-site preferences
- [x] Implement refresh token mechanism for longer sessions
  - [x] Add token refresh logic
  - [x] Set up automatic refresh before expiration

## WordPress Integration

- [x] Add support for WordPress AJAX endpoints:
  - [x] `bakedbot_get_user_info` - Already implemented in WordPress plugin
  - [x] `bakedbot_sync_user` - Already implemented in WordPress plugin
  - [x] `bakedbot_verify_token` - Already implemented in WordPress plugin
- [x] Handle WordPress nonce for security - Already implemented in WordPress plugin
- [x] Update config initialization to read from WordPress-provided settings
  - [x] Read from window.BakedBotConfig
  - [x] Add fallback for standalone mode

## Data Structure Changes

- [x] Add `bakedBotId` to user profile data structure
  - [x] Update user types/interfaces
  - [x] Add storage for ID
- [x] Add `synced` flag to track account linking status
  - [x] Update user profile interface
  - [x] Add logic to check sync status
- [x] Create `authToken` storage with appropriate security
  - [x] Implement secure storage
  - [x] Add encryption/decryption helpers
- [x] Add support for cross-site preferences
  - [x] Create preferences storage
  - [x] Implement sync mechanism

## Theme Management

- [x] Update theme management to read from WordPress settings
  - [x] Use window.BakedBotTheme when available
  - [x] Add theme context to provide values
- [x] Remove theme settings UI when `disableThemeSettings` is true
  - [x] Add conditional rendering
  - [x] Replace with profile UI
- [x] Add fallback theme options for when WordPress settings are not available
  - [x] Create default theme values
  - [x] Add detection for missing WordPress settings

## Configuration Changes

- [x] Read configuration from `window.BakedBotConfig` instead of local storage
  - [x] Create config context provider
  - [x] Add type definitions for all config options
- [x] Support new config options:
  - [x] `authMode`: 'federated', 'wordpress', or 'bakedbot'
  - [x] `syncUsers`: true/false to enable user synchronization
  - [x] `siteIdentifier`: unique identifier for the current dispensary
  - [x] `apiUrl`: URL for the BakedBot API
  - [x] `wordPressAjaxUrl`: URL for WordPress AJAX endpoints

## Utility Functions

- [x] Create `isAuthenticated()` helper that supports all authentication modes
  - [x] Check appropriate auth source based on mode
  - [x] Return consistent boolean result
- [x] Add `getCurrentUser()` function that works across authentication modes
  - [x] Fetch user from appropriate source
  - [x] Normalize user data across sources
- [x] Create `syncUserAccounts()` utility to link WordPress and BakedBot accounts
  - [x] Call appropriate endpoints
  - [x] Handle success/failure
- [x] Add `verifyFederatedToken()` to validate tokens with BakedBot API
  - [x] Implement API call
  - [x] Cache results for performance

## Testing

- [ ] Create test cases for all authentication modes
  - [ ] Test federated auth flow
  - [ ] Test WordPress-only auth
  - [ ] Test BakedBot-only auth
- [ ] Test cross-site authentication with multiple WordPress installations
  - [ ] Set up test environment with multiple sites
  - [ ] Verify auth works across sites
- [ ] Verify token expiration and refresh mechanisms
  - [ ] Test token expiration
  - [ ] Test automatic refresh
- [ ] Test account linking and unlinking scenarios
  - [ ] Test linking WordPress to BakedBot
  - [ ] Test unlinking accounts
- [ ] Validate security of token storage
  - [ ] Test encryption
  - [ ] Test for token leakage

## Documentation

- [ ] Update internal code documentation with authentication flow details
  - [ ] Add JSDoc comments to auth functions
  - [ ] Create flowcharts for auth processes
- [ ] Create user guide for configuring federated authentication
  - [ ] Document WordPress plugin settings
  - [ ] Explain auth modes
- [ ] Document API endpoints for authentication and synchronization
  - [ ] List all endpoints with parameters
  - [ ] Provide example responses
- [ ] Add troubleshooting section for common authentication issues
  - [ ] Include common error messages
  - [ ] Add resolution steps

## Implementation Progress

- [x] WordPress plugin backend already includes:
  - [x] Auth mode configuration ('federated', 'wordpress', 'bakedbot')
  - [x] AJAX endpoints for user info, sync, and token verification
  - [x] WordPress nonce security
  - [x] Basic user synchronization logic
  - [x] Config initialization in window.BakedBotConfig
- [x] React app frontend core components implemented:
  - [x] ConfigProvider component with auth mode detection
  - [x] FederatedAuthProvider component with multi-source authentication
  - [x] Updated LoginForm with contextual authentication options
  - [x] App.tsx updated to use the new providers
  - [x] Secure token storage with encryption and expiration
  - [x] Theme management with WordPress integration
- [x] React app frontend remaining tasks:
  - [x] Implement secure token storage
  - [x] Create Profile UI component
  - [x] Add theme management with WordPress integration
  - [x] Update legacy hooks (useAuth) to work with new system
  - [x] Enhance Sidebar UI with user profile section
  - [x] Write comprehensive tests for all auth flows
