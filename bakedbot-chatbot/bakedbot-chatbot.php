<?php
/**
 * Plugin Name: BakedBot Chatbot
 * Plugin URI: https://bakedbot.ai
 * Description: A budtender AI chatbot powered by BakedBot
 * Version: 1.5.0
 * Author: BakedBot AI
 * Author URI: https://bakedbot.ai
 * Text Domain: bakedbot-chatbot
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin version constant
if (!defined('BAKEDBOT_CHATBOT_VERSION')) {
    define('BAKEDBOT_CHATBOT_VERSION', '1.5.0');
}

// Include CSS isolation functions
if (!function_exists('bakedbot_shadow_dom_implementation')) {
    require_once plugin_dir_path(__FILE__) . 'wordpress-plugin-css-fix.php';
}

class BakedBotChatbot {
    private $plugin_path;
    private $plugin_url;
    private $options;
    private $option_name;
    private $version = '1.5.0';
    private $shortcode_used = false;
    private $chatbot_rendered = false;

    public function __construct() {
        $this->plugin_path = plugin_dir_path(__FILE__);
        $this->plugin_url = plugin_dir_url(__FILE__);
        
        // Initialize options
        $this->option_name = 'bakedbot_chatbot_options';
        $this->options = get_option($this->option_name, array(
            'primary_color' => '#22AD85',
            'secondary_color' => '#24504A',
            'background_color' => '#FFFFFF',
            'header_color' => '#FFFFFF',
            'text_color' => '#2C2C2C',
            'position' => 'right',
            'auto_open' => false,
            'show_on_mobile' => true,
            'show_on_frontpage' => true,
            'api_key' => '',
            'bakedbot_api_url' => 'https://beta.bakedbot.ai/api/',
            'debug_mode' => false,
            'disable_theme_settings' => true,
            'product_page_slug' => '/product'
        ));

        // Register activation and deactivation hooks
        register_activation_hook(__FILE__, array(__CLASS__, 'activate'));
        register_deactivation_hook(__FILE__, array(__CLASS__, 'deactivate'));
        
        // Register scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'register_assets'));
        
        // Initialize Action Scheduler for product sync
        $this->init_action_scheduler();
        
        // Add shortcode for inserting chatbot
        add_shortcode('bakedbot_chatbot', array($this, 'chatbot_shortcode'));
        
        // Add shortcode for headless menu
        add_shortcode('bakedbot_menu', array($this, 'headless_menu_shortcode'));
        
        // Add shortcode for headless homepage
        add_shortcode('bakedbot_homepage', array($this, 'headless_homepage_shortcode'));
        
        // Add shortcode for product details page
        add_shortcode('bakedbot_product', array($this, 'product_details_shortcode'));
        
        // Add SEO features
        add_action('init', array($this, 'add_seo_features'));
        
        // Conditionally add chatbot to the front page content or other designated pages
        add_action('wp_footer', array($this, 'conditionally_add_chatbot_container'));
        
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Register settings
        add_action('admin_init', array($this, 'register_settings'));
        
        // Register AJAX endpoints
        
        // AJAX endpoint for page creation
        add_action('wp_ajax_bakedbot_create_page', array($this, 'ajax_create_page'));
        add_action('wp_ajax_bakedbot_add_to_pages', array($this, 'ajax_add_to_pages'));
        add_action('wp_ajax_bakedbot_remove_from_pages', array($this, 'ajax_remove_from_pages'));
        add_action('wp_ajax_bakedbot_toggle_frontpage', array($this, 'ajax_toggle_frontpage'));
        
        // AJAX endpoints for product sync
        add_action('wp_ajax_bakedbot_sync_products', array($this, 'ajax_sync_products'));
        add_action('wp_ajax_bakedbot_get_import_status', array($this, 'ajax_get_import_status'));
        
        // AJAX endpoints for product export
        add_action('wp_ajax_bakedbot_export_products', array($this, 'ajax_export_products'));
        add_action('wp_ajax_bakedbot_get_export_status', array($this, 'ajax_get_export_status'));
        

        
        // Add full-width page handling
        add_action('wp_head', array($this, 'add_full_width_styles'));
        add_filter('body_class', array($this, 'add_full_width_body_class'));
        
        // Add admin notice if API key or site identifier is missing
        add_action('admin_notices', array($this, 'admin_notices'));
        
        // Remove admin notices on our settings page
        add_action('admin_head', array($this, 'remove_admin_notices'));

        // Use Shadow DOM for CSS isolation
        if (function_exists('bakedbot_shadow_dom_implementation')) {
            add_action('wp_footer', 'bakedbot_shadow_dom_implementation');
        }
    }

    public function register_assets() {
        // Debug script to help identify issues
        wp_register_script(
            'bakedbot-debug',
            '',
            array(),
            '1.0',
            true
        );

        wp_add_inline_script('bakedbot-debug', '
            console.log("%c BakedBot Debug: Container initialization started", "background: #24504A; color: white; padding: 5px; border-radius: 3px;");
            
            // Global error handler to catch JS errors
            window.addEventListener("error", function(event) {
                if (event.filename && event.filename.includes("bakedbot")) {
                    console.error("BakedBot Debug: JS Error in BakedBot script:", event.message, "at", event.filename, ":", event.lineno);
                }
            });

            // More detailed error logging for script errors
            (function() {
                const originalConsoleError = console.error;
                console.error = function() {
                    const args = Array.from(arguments);
                    if (args.length > 0 && typeof args[0] === "string" && args[0].includes("BakedBot")) {
                        console.log("BakedBot Debug: JS Error:", ...args);
                    }
                    originalConsoleError.apply(console, args);
                };
            })();
        ');
        
        wp_enqueue_script('bakedbot-debug');
        
        // Load React and ReactDOM from CDN
        wp_register_script(
            'react',
            'https://unpkg.com/react@18/umd/react.production.min.js',
            array(),
            '18.0.0',
            true
        );
        
        wp_register_script(
            'react-dom',
            'https://unpkg.com/react-dom@18/umd/react-dom.production.min.js',
            array('react'),
            '18.0.0',
            true
        );
        
        // Add History package first
        wp_register_script(
            'history',
            'https://unpkg.com/history@5.3.0/umd/history.production.min.js',
            array('react'),
            '5.3.0',
            true
        );
        
        // Add React Router DOM with specific version compatible with our code
        wp_register_script(
            'react-router',
            'https://unpkg.com/react-router@6.23.0/dist/umd/react-router.production.min.js',
            array('react', 'history'),
            '6.23.0',
            true
        );
        
        wp_register_script(
            'react-router-dom',
            'https://unpkg.com/react-router-dom@6.23.0/dist/umd/react-router-dom.production.min.js',
            array('react', 'react-dom', 'history', 'react-router'),
            '6.23.0',
            true
        );
        
        wp_enqueue_script('react');
        wp_enqueue_script('react-dom');
        wp_enqueue_script('history');
        wp_enqueue_script('react-router');
        wp_enqueue_script('react-router-dom');
        
        // Add a script to properly initialize the React Router history
        wp_register_script(
            'react-router-setup',
            '',
            array('react', 'react-dom', 'history', 'react-router', 'react-router-dom'),
            '1.0',
            true
        );
        
        wp_add_inline_script('react-router-setup', "
            // Ensure global history object is available for React Router
            if (typeof window.History !== 'undefined') {
                console.log('BakedBot Debug: History package loaded');
                
                // Add missing createBrowserHistory to ReactRouterDOM if needed
                if (window.ReactRouterDOM && !window.ReactRouterDOM.createBrowserHistory && window.History.createBrowserHistory) {
                    console.log('BakedBot Debug: Creating polyfill for createBrowserHistory');
                    window.ReactRouterDOM.createBrowserHistory = window.History.createBrowserHistory;
                }
            } else {
                console.error('BakedBot Debug: History package failed to load');
            }
        ");
        
        wp_enqueue_script('react-router-setup');
        
        // Look for bundle.js specifically in dist/assets/js
        $bundle_file = $this->plugin_path . 'dist/assets/js/bundle.js';
        if (file_exists($bundle_file)) {
            $handle = 'bakedbot-bundle';
            wp_register_script(
                $handle,
                $this->plugin_url . 'dist/assets/js/bundle.js',
                array('react', 'react-dom', 'react-router-dom'),
                filemtime($bundle_file),
                true
            );
            
            // Load bundle.js as a module script
            add_filter('script_loader_tag', function($tag, $handle) {
                if ('bakedbot-bundle' === $handle) {
                    return str_replace('<script ', '<script defer ', $tag);
                }
                return $tag;
            }, 10, 2);
            
            wp_enqueue_script($handle);
            
            // Debug JavaScript loading
            wp_add_inline_script('bakedbot-debug', 'console.log("BakedBot Debug: Enqueued bundle.js from: ' . $this->plugin_url . 'dist/assets/js/bundle.js");');
            wp_add_inline_script('bakedbot-debug', 'console.log("BakedBot Debug: Bundle file last modified:", ' . filemtime($bundle_file) . ');');
        } else {
            wp_add_inline_script('bakedbot-debug', 'console.error("BakedBot Debug: bundle.js file not found at: ' . $this->plugin_path . 'dist/assets/js/bundle.js");');
            wp_add_inline_script('bakedbot-debug', 'console.log("BakedBot Debug: Plugin path:", "' . $this->plugin_path . '");');
            
            // Check for alternative bundle locations
            $alt_bundle_file = $this->plugin_path . 'dist/bundle.js';
            if (file_exists($alt_bundle_file)) {
                wp_add_inline_script('bakedbot-debug', 'console.log("BakedBot Debug: Found alternative bundle at dist/bundle.js");');
            }
        }

        // The old method of CSS registration is replaced by the Shadow DOM implementation
        // bakedbot_register_isolated_styles();
    }
    
    /**
     * Get the JavaScript for initializing theme settings
     */
    public function get_theme_script() {
        return "
            window.BakedBotTheme = {
                primaryColor: '" . esc_js($this->get_option('primary_color', '#057540')) . "',
                secondaryColor: '" . esc_js($this->get_option('secondary_color', '#0D211D')) . "',
                backgroundColor: '" . esc_js($this->get_option('background_color', '#FFFFFF')) . "',
                headerColor: '" . esc_js($this->get_option('header_color', '#FFFFFF')) . "',
                textColor: '" . esc_js($this->get_option('text_color', '#2C2C2C')) . "'
            };
            
            console.log('BakedBot Debug: Theme settings initialized');
        ";
    }
    
    /**
     * Get the JavaScript for initializing BakedBot configuration
     * Always pulls the latest settings from WordPress
     */
    public function get_config_script($atts = array()) {
        // Set default values for shortcode attributes
        $default_atts = array(
            'position' => $this->get_option('position', 'right'),
            'auto_open' => false,
            'embedded' => false,
            'container_id' => ''
        );
        
        $atts = wp_parse_args($atts, $default_atts);
        
        return "
            // Always update BakedBotConfig with the latest WordPress settings
            window.BakedBotConfig = {
                position: '" . esc_js($atts['position']) . "',
                initialOpen: " . ($atts['auto_open'] ? 'true' : 'false') . ",
                showOnMobile: " . ($this->get_option('show_on_mobile', true) ? 'true' : 'false') . ",
                useWordPressAuth: " . ($this->get_option('use_wordpress_auth', true) ? 'true' : 'false') . ",
                wordPressAjaxUrl: '" . esc_js(admin_url('admin-ajax.php')) . "',
                userModeOnly: " . ($this->get_option('user_mode_only', false) ? 'true' : 'false') . ",
                disableThemeSettings: " . ($this->get_option('disable_theme_settings', true) ? 'true' : 'false') . ",
                apiKey: '" . esc_js($this->get_option('api_key', '')) . "',
                syncUsers: " . ($this->get_option('sync_users', true) ? 'true' : 'false') . ",
                authMode: '" . esc_js($this->get_option('auth_mode', 'federated')) . "',
                apiUrl: '" . esc_js($this->get_option('bakedbot_api_url', 'https://beta.bakedbot.ai/api/')) . "',
                debugMode: " . ($this->get_option('debug_mode', false) ? 'true' : 'false') . ",
                embedded: " . ($atts['embedded'] ? 'true' : 'false') . ",
                wpEmbedded: " . ($atts['embedded'] ? 'true' : 'false') . ",
                skipRouterCheck: true,
                containerId: '" . esc_js($atts['container_id']) . "',
                
                // Additional settings for comprehensive configuration
                chatbotTitle: '" . esc_js($this->get_option('chatbot_title', 'BakedBot')) . "',
                welcomeMessage: '" . esc_js($this->get_option('welcome_message', 'Hello! How can I help you today?')) . "',
                buttonText: '" . esc_js($this->get_option('button_text', 'Chat with us')) . "',
                avatarUrl: '" . esc_js($this->get_option('avatar_url', '')) . "'
            };
            
            console.log('BakedBot Debug: Configuration updated with latest WordPress settings', window.BakedBotConfig);
        ";
    }
    
    public function chatbot_shortcode($atts) {
        // Extract shortcode attributes
        $atts = shortcode_atts(array(
            'position' => $this->get_option('position', 'right'),
            'width' => '400px',
            'height' => '600px',
            'auto_open' => false,
        ), $atts);
        
        // Generate a unique ID for this embedded chatbot
        $unique_id = 'bakedbot-chatbot-' . mt_rand(1000, 9999);
        
        // Create the inline script to initialize this specific instance
        $script = '<script>
            document.addEventListener("DOMContentLoaded", function() {
                console.log("BakedBot Debug: Embedded chatbot initialized with ID: ' . $unique_id . '");
                
                // Set theme values
                if (!window.BakedBotTheme) {
                    window.BakedBotTheme = {
                        primaryColor: "' . $this->get_option('primary_color', '#22AD85') . '",
                        secondaryColor: "' . $this->get_option('secondary_color', '#24504A') . '",
                        backgroundColor: "' . $this->get_option('background_color', '#FFFFFF') . '",
                        headerColor: "' . $this->get_option('header_color', '#FFFFFF') . '",
                        textColor: "' . $this->get_option('text_color', '#2C2C2C') . '"
                    };
                }
                
                // Set user information if not already set
                if (!window.BakedBotUser) {
                    window.BakedBotUser = {
                        loggedIn: ' . (is_user_logged_in() ? 'true' : 'false') . '
                    };
                    
                    // If user is logged in, add additional user information
                    if (window.BakedBotUser.loggedIn) {
                        ' . $this->get_logged_in_user_script() . '
                    }
                }
                
                // Set configuration values for this instance - always use latest settings
                ' . $this->get_config_script(array(
                    'position' => $atts['position'],
                    'auto_open' => $atts['auto_open'],
                    'embedded' => true,
                    'container_id' => $unique_id
                )) . '
                
                // Try to initialize widget
                function tryInitializeEmbeddedWidget() {
                    if (typeof window.createBakedBotWidget === "function") {
                        window.createBakedBotWidget("' . $unique_id . '");
                        return true;
                    } else if (typeof window.BakedBot?.create === "function") {
                        window.BakedBot.create("' . $unique_id . '");
                        return true;
                    } else if (typeof window.initBakedBot === "function") {
                        window.initBakedBot("' . $unique_id . '");
                        return true;
                    }
                    return false;
                }
                
                // Wait for scripts to load and try to initialize
                if (!tryInitializeEmbeddedWidget()) {
                    console.log("BakedBot Debug: Initialization function not found for embedded chatbot, will retry");
                    // Try again after a delay
                    setTimeout(function() {
                        if (!tryInitializeEmbeddedWidget()) {
                            console.error("BakedBot Debug: Failed to initialize embedded chatbot, widget functions not found");
                        }
                    }, 1000);
                }
            });
        </script>';
        
        // Return the container element for the chatbot with inline script
        $this->shortcode_used = true;
        return '<div id="' . $unique_id . '" 
            class="bakedbot-embedded" 
            style="width: ' . esc_attr($atts['width']) . '; height: ' . esc_attr($atts['height']) . ';"
            data-position="' . esc_attr($atts['position']) . '">
            </div>' . $script;
    }
    
    public function headless_menu_shortcode($atts) {
        // Extract shortcode attributes
        $atts = shortcode_atts(array(
            'enable_filters' => 'true',
            'enable_search' => 'true', 
            'enable_cart' => 'true',
            'layout' => 'grid',
            'products_per_page' => '12',
            'theme' => 'light',
            'primary_color' => $this->get_option('primary_color', '#065f46'),
            'secondary_color' => $this->get_option('secondary_color', '#10b981'),
            'height' => 'auto',
            'api_key' => $this->get_option('api_key', ''),
            'category' => '',
            'brand' => '',
            'show_title' => 'false',
            'title' => '',
            'product_page_url' => $this->get_option('product_page_slug', '/product')
        ), $atts);
        
        // Generate a unique ID for this headless menu instance
        $unique_id = 'bakedbot-menu-' . mt_rand(1000, 9999);
        
        // Get current page from URL for SEO pagination - use 'menu_page' to avoid WP conflict
        $current_page = isset($_GET['menu_page']) ? max(1, intval($_GET['menu_page'])) : 1;
        
        // Always fetch initial products for SEO - with pagination support
        $ssr_result = $this->fetch_products_for_ssr($atts, $current_page);
        $initial_products = isset($ssr_result['products']) ? $ssr_result['products'] : array();
        $pagination_info = isset($ssr_result['pagination']) ? $ssr_result['pagination'] : array(
            'currentPage' => 1,
            'totalPages' => 1,
            'totalProducts' => 0
        );
        $structured_data = $this->generate_structured_data($initial_products);
        
        // Create the inline script to enhance with React
        $script = '';
        if (!empty($structured_data)) {
            $script = '<script type="application/ld+json">' . json_encode($structured_data) . '</script>';
        }
        
        // Add the enhancement function directly in the shortcode to ensure it's available
        $script .= '<script>
            // Define enhancement function if not already defined
            if (!window.enhanceBakedBotMenu) {
                window.enhanceBakedBotMenu = function(containerId) {
                    const container = document.getElementById(containerId);
                    if (!container) {
                        console.error("BakedBot: Container not found:", containerId);
                        return;
                    }
                    
                    console.log("BakedBot Debug: Enhancing menu with ID:", containerId);
                    
                    // Get config
                    const config = window.BakedBotMenuConfig || {};
                    console.log("BakedBot Debug: Menu config:", config);
                    
                    // Check if required dependencies are available
                    if (typeof React === "undefined") {
                        console.error("BakedBot: React not loaded");
                        return;
                    }
                    
                    if (typeof ReactDOM === "undefined") {
                        console.error("BakedBot: ReactDOM not loaded");
                        return;
                    }
                    
                    if (!window.BakedBotMenu) {
                        console.error("BakedBot: BakedBotMenu component not found");
                        return;
                    }
                    
                    try {
                        // Import and enhance with React
                        const props = {
                            ...config,
                            isProgressiveEnhancement: true,
                            containerId: containerId,
                            initialProducts: config.initialProducts || []
                        };
                        
                        console.log("BakedBot Debug: Creating menu with props:", props);
                        
                        // Create HeadlessMenu wrapped with CartProvider
                        const menuElement = React.createElement(
                            window.CartProvider || React.Fragment,
                            {},
                            React.createElement(window.BakedBotMenu, props)
                        );
                        
                        // Progressive enhancement - hydrate existing HTML instead of replacing
                        const root = ReactDOM.createRoot ? ReactDOM.createRoot(container) : null;
                        if (root) {
                            // Use hydrate for progressive enhancement
                            root.render(menuElement);
                            console.log("BakedBot Debug: Menu enhanced successfully with createRoot");
                        } else {
                            // Legacy hydration
                            ReactDOM.hydrate ? ReactDOM.hydrate(menuElement, container) : ReactDOM.render(menuElement, container);
                            console.log("BakedBot Debug: Menu enhanced successfully with hydration");
                        }
                        
                        // Mark as enhanced
                        container.classList.add("enhanced");
                    } catch (error) {
                        console.error("BakedBot Debug: Error enhancing menu:", error);
                    }
                };
            }
            
            document.addEventListener("DOMContentLoaded", function() {
                console.log("BakedBot Debug: SEO-friendly headless menu initialized with ID: ' . $unique_id . '");
                console.log("BakedBot Debug: Checking for required dependencies...");
                console.log("BakedBot Debug: React available:", typeof React !== "undefined");
                console.log("BakedBot Debug: ReactDOM available:", typeof ReactDOM !== "undefined");
                console.log("BakedBot Debug: BakedBotMenu available:", typeof window.BakedBotMenu !== "undefined");
                console.log("BakedBot Debug: CartProvider available:", typeof window.CartProvider !== "undefined");
                console.log("BakedBot Debug: enhanceBakedBotMenu function available:", typeof window.enhanceBakedBotMenu === "function");
                
                // Verify function is now available after definition
                console.log("BakedBot Debug: enhanceBakedBotMenu function available after definition:", typeof window.enhanceBakedBotMenu === "function");
                
                // Set theme values
                if (!window.BakedBotTheme) {
                    window.BakedBotTheme = {
                        primaryColor: "' . esc_js($atts['primary_color']) . '",
                        secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                        backgroundColor: "' . $this->get_option('background_color', '#FFFFFF') . '",
                        headerColor: "' . $this->get_option('header_color', '#FFFFFF') . '",
                        textColor: "' . $this->get_option('text_color', '#2C2C2C') . '"
                    };
                }
                console.log("BakedBot Debug: Theme set:", window.BakedBotTheme);
                
                // Set configuration values for this headless menu instance
                ' . $this->get_config_script(array(
                    'embedded' => true,
                    'container_id' => $unique_id,
                    'menu_mode' => true
                )) . '
                
                // Set menu-specific configuration
                window.BakedBotMenuConfig = {
                    enableFilters: ' . ($atts['enable_filters'] === 'true' ? 'true' : 'false') . ',
                    enableSearch: ' . ($atts['enable_search'] === 'true' ? 'true' : 'false') . ',
                    enableCart: ' . ($atts['enable_cart'] === 'true' ? 'true' : 'false') . ',
                    layout: "' . esc_js($atts['layout']) . '",
                    productsPerPage: ' . intval($atts['products_per_page']) . ',
                    theme: "' . esc_js($atts['theme']) . '",
                    primaryColor: "' . esc_js($atts['primary_color']) . '",
                    secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                    apiKey: "' . esc_js($atts['api_key']) . '",
                    initialProducts: ' . json_encode($initial_products) . ',
                    initialPagination: ' . json_encode($pagination_info) . ',
                    currentPage: ' . intval($current_page) . ',
                    productPageUrl: "' . esc_js($atts['product_page_url']) . '"
                };
                console.log("BakedBot Debug: Menu config set:", window.BakedBotMenuConfig);
                
                // Progressive enhancement - enhance existing HTML with React
                function tryEnhanceHeadlessMenu() {
                    console.log("BakedBot Debug: Attempting to enhance menu...");
                    const container = document.getElementById("' . $unique_id . '");
                    
                    if (container) {
                        // Add enhancing class for smooth transition
                        container.classList.add("enhancing");
                    }
                    
                    if (typeof window.enhanceBakedBotMenu === "function") {
                        console.log("BakedBot Debug: Found enhanceBakedBotMenu function, calling it...");
                        window.enhanceBakedBotMenu("' . $unique_id . '");
                        
                        // Mark as enhanced after a brief delay
                        setTimeout(() => {
                            if (container) {
                                container.classList.remove("enhancing");
                                container.classList.add("enhanced");
                            }
                        }, 150);
                        
                        return true;
                    } else if (typeof window.BakedBot?.enhanceMenu === "function") {
                        console.log("BakedBot Debug: Found BakedBot.enhanceMenu function, calling it...");
                        window.BakedBot.enhanceMenu("' . $unique_id . '");
                        
                        setTimeout(() => {
                            if (container) {
                                container.classList.remove("enhancing");
                                container.classList.add("enhanced");
                            }
                        }, 150);
                        
                        return true;
                    }
                    
                    // Remove enhancing class if enhancement failed
                    if (container) {
                        container.classList.remove("enhancing");
                    }
                    
                    console.log("BakedBot Debug: No enhancement function found");
                    return false;
                }
                
                // Wait for scripts to load and try to enhance
                console.log("BakedBot Debug: Trying initial enhancement...");
                if (!tryEnhanceHeadlessMenu()) {
                    console.log("BakedBot Debug: Enhancement function not found for headless menu, will retry in 1 second");
                    setTimeout(function() {
                        console.log("BakedBot Debug: Retrying enhancement after 1 second...");
                        if (!tryEnhanceHeadlessMenu()) {
                            console.log("BakedBot Debug: Still no enhancement function found, will retry in 3 seconds");
                            setTimeout(function() {
                                console.log("BakedBot Debug: Final retry after 3 seconds...");
                                if (!tryEnhanceHeadlessMenu()) {
                                    console.log("BakedBot Debug: Running in non-enhanced mode (SEO-friendly fallback)");
                                }
                            }, 3000);
                        }
                    }, 1000);
                }
            });
        </script>';
        
        // Add meta tags and page title for SEO
        add_action('wp_head', function() use ($initial_products, $atts, $pagination_info, $current_page) {
            // Basic meta tags
            $page_title = $current_page > 1 ? $atts['title'] . ' - Page ' . $current_page : $atts['title'];
            echo '<meta name="description" content="Browse our selection of premium cannabis products. Find the perfect strain for your needs.">' . "\n";
            echo '<meta property="og:title" content="' . esc_attr($page_title) . '">' . "\n";
            echo '<meta property="og:description" content="Premium cannabis products available for purchase.">' . "\n";
            echo '<meta property="og:type" content="website">' . "\n";
            if (!empty($initial_products)) {
                echo '<meta property="og:image" content="' . esc_url($initial_products[0]['image_url']) . '">' . "\n";
            }
            
            // Pagination meta tags for SEO
            $total_pages = $pagination_info['totalPages'];
            $base_url = remove_query_arg('menu_page');
            
            if ($current_page > 1) {
                $prev_url = ($current_page == 2) ? $base_url : add_query_arg('menu_page', $current_page - 1, $base_url);
                echo '<link rel="prev" href="' . esc_url($prev_url) . '">' . "\n";
            }
            
            if ($current_page < $total_pages) {
                $next_url = add_query_arg('menu_page', $current_page + 1, $base_url);
                echo '<link rel="next" href="' . esc_url($next_url) . '">' . "\n";
            }
            
            // Canonical URL
            $canonical_url = ($current_page == 1) ? $base_url : add_query_arg('menu_page', $current_page, $base_url);
            echo '<link rel="canonical" href="' . esc_url($canonical_url) . '">' . "\n";
        });
        
        // Update page title for pagination
        if ($current_page > 1) {
            add_filter('wp_title', function($title) use ($atts, $current_page) {
                return $atts['title'] . ' - Page ' . $current_page . ' | ' . get_bloginfo('name');
            });
            
            add_filter('document_title_parts', function($parts) use ($atts, $current_page) {
                $parts['title'] = $atts['title'] . ' - Page ' . $current_page;
                return $parts;
            });
        }
        
        // Always return SEO-friendly HTML structure for progressive enhancement
        return $this->render_progressive_menu_html($unique_id, $atts, $initial_products, $pagination_info, $current_page) . $script;
    }
    
    public function headless_homepage_shortcode($atts) {
        // Extract shortcode attributes
        $atts = shortcode_atts(array(
            'enable_cart' => 'true',
            'theme' => 'light',
            'primary_color' => $this->get_option('primary_color', '#065f46'),
            'secondary_color' => $this->get_option('secondary_color', '#10b981'),
            'height' => 'auto',
            'api_key' => $this->get_option('api_key', ''),
            'featured_products_count' => '8',
            'popular_categories_count' => '6',
            'show_promotion' => 'true',
            'promotion_title' => 'Premium Cannabis Marketplace',
            'promotion_subtitle' => 'Discover our curated selection of premium cannabis products',
            'promotion_image_url' => 'https://picsum.photos/400',
            'menu_page_url' => '/dispensary-menu',
            'product_page_url' => $this->get_option('product_page_slug', '/product')
        ), $atts);
        
        // Generate a unique ID for this headless homepage instance
        $unique_id = 'bakedbot-homepage-' . mt_rand(1000, 9999);
        
        // Always fetch initial featured products for SEO
        $ssr_result = $this->fetch_products_for_ssr($atts, 1);
        $featured_products_count = isset($atts['featured_products_count']) ? intval($atts['featured_products_count']) : 8;
        $products_array = isset($ssr_result['products']) ? $ssr_result['products'] : array();
        $initial_products = array_slice($products_array, 0, $featured_products_count);
        
        // Get categories for SEO
        $filter_options = $this->fetch_filter_options_for_ssr();
        $popular_categories_count = isset($atts['popular_categories_count']) ? intval($atts['popular_categories_count']) : 6;
        $categories = array_slice(
            (isset($filter_options['categories']) && is_array($filter_options['categories'])) ? $filter_options['categories'] : array(), 
            0, 
            $popular_categories_count
        );
        
        // Create the inline script to enhance with React
        $script = '<script>
            // Define enhancement function for homepage if not already defined
            if (!window.enhanceBakedBotHomepage) {
                window.enhanceBakedBotHomepage = function(containerId) {
                    const container = document.getElementById(containerId);
                    if (!container) {
                        console.error("BakedBot: Homepage container not found:", containerId);
                        return;
                    }
                    
                    console.log("BakedBot Debug: Enhancing homepage with ID:", containerId);
                    
                    // Get config
                    const config = window.BakedBotHomepageConfig || {};
                    console.log("BakedBot Debug: Homepage config:", config);
                    
                    // Check if required dependencies are available
                    if (typeof React === "undefined") {
                        console.error("BakedBot: React not loaded");
                        return;
                    }
                    
                    if (typeof ReactDOM === "undefined") {
                        console.error("BakedBot: ReactDOM not loaded");
                        return;
                    }
                    
                    if (!window.BakedBotHomepage) {
                        console.error("BakedBot: BakedBotHomepage component not found");
                        return;
                    }
                    
                    try {
                        // Import and enhance with React
                        const props = {
                            ...config,
                            isProgressiveEnhancement: true,
                            containerId: containerId
                        };
                        
                        console.log("BakedBot Debug: Creating homepage with props:", props);
                        
                        // Create HeadlessHomepage wrapped with CartProvider
                        const homepageElement = React.createElement(
                            window.CartProvider || React.Fragment,
                            {},
                            React.createElement(window.BakedBotHomepage, props)
                        );
                        
                        // Progressive enhancement - hydrate existing HTML instead of replacing
                        const root = ReactDOM.createRoot ? ReactDOM.createRoot(container) : null;
                        if (root) {
                            root.render(homepageElement);
                            console.log("BakedBot Debug: Homepage enhanced successfully with createRoot");
                        } else {
                            ReactDOM.render(homepageElement, container);
                            console.log("BakedBot Debug: Homepage enhanced successfully with legacy render");
                        }
                        
                        // Mark as enhanced
                        container.classList.add("enhanced");
                    } catch (error) {
                        console.error("BakedBot Debug: Error enhancing homepage:", error);
                    }
                };
            }
            
            document.addEventListener("DOMContentLoaded", function() {
                console.log("BakedBot Debug: SEO-friendly headless homepage initialized with ID: ' . $unique_id . '");
                
                // Set theme values
                if (!window.BakedBotTheme) {
                    window.BakedBotTheme = {
                        primaryColor: "' . esc_js($atts['primary_color']) . '",
                        secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                        backgroundColor: "' . $this->get_option('background_color', '#FFFFFF') . '",
                        headerColor: "' . $this->get_option('header_color', '#FFFFFF') . '",
                        textColor: "' . $this->get_option('text_color', '#2C2C2C') . '"
                    };
                }
                console.log("BakedBot Debug: Homepage theme set:", window.BakedBotTheme);
                
                // Set configuration values for this headless homepage instance
                ' . $this->get_config_script(array(
                    'embedded' => true,
                    'container_id' => $unique_id,
                    'homepage_mode' => true
                )) . '
                
                // Set homepage-specific configuration
                window.BakedBotHomepageConfig = {
                    enableCart: ' . ($atts['enable_cart'] === 'true' ? 'true' : 'false') . ',
                    theme: "' . esc_js($atts['theme']) . '",
                    primaryColor: "' . esc_js($atts['primary_color']) . '",
                    secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                    apiKey: "' . esc_js($atts['api_key']) . '",
                    featuredProductsCount: ' . intval($atts['featured_products_count']) . ',
                    popularCategoriesCount: ' . intval($atts['popular_categories_count']) . ',
                    showPromotion: ' . ($atts['show_promotion'] === 'true' ? 'true' : 'false') . ',
                    promotionTitle: "' . esc_js($atts['promotion_title']) . '",
                    promotionSubtitle: "' . esc_js($atts['promotion_subtitle']) . '",
                    promotionImageUrl: "' . esc_js($atts['promotion_image_url']) . '",
                    menuPageUrl: "' . esc_js($atts['menu_page_url']) . '",
                    productPageUrl: "' . esc_js($atts['product_page_url']) . '"
                };
                console.log("BakedBot Debug: Homepage config set:", window.BakedBotHomepageConfig);
                
                // Progressive enhancement - enhance existing HTML with React
                function tryEnhanceHeadlessHomepage() {
                    console.log("BakedBot Debug: Attempting to enhance homepage...");
                    if (typeof window.enhanceBakedBotHomepage === "function") {
                        console.log("BakedBot Debug: Found enhanceBakedBotHomepage function, calling it...");
                        window.enhanceBakedBotHomepage("' . $unique_id . '");
                        return true;
                    } else if (typeof window.BakedBot?.enhanceHomepage === "function") {
                        console.log("BakedBot Debug: Found BakedBot.enhanceHomepage function, calling it...");
                        window.BakedBot.enhanceHomepage("' . $unique_id . '");
                        return true;
                    }
                    console.log("BakedBot Debug: No homepage enhancement function found");
                    return false;
                }
                
                // Wait for scripts to load and try to enhance
                console.log("BakedBot Debug: Trying initial homepage enhancement...");
                if (!tryEnhanceHeadlessHomepage()) {
                    console.log("BakedBot Debug: Enhancement function not found for headless homepage, will retry in 1 second");
                    setTimeout(function() {
                        console.log("BakedBot Debug: Retrying homepage enhancement after 1 second...");
                        if (!tryEnhanceHeadlessHomepage()) {
                            console.log("BakedBot Debug: Still no enhancement function found, will retry in 3 seconds");
                            setTimeout(function() {
                                console.log("BakedBot Debug: Final homepage retry after 3 seconds...");
                                if (!tryEnhanceHeadlessHomepage()) {
                                    console.log("BakedBot Debug: Running in non-enhanced mode (SEO-friendly fallback)");
                                }
                            }, 3000);
                        }
                    }, 1000);
                }
            });
        </script>';
        
        // Always return SEO-friendly HTML structure for progressive enhancement
        return $this->render_progressive_homepage_html($unique_id, $atts, $initial_products, $categories) . $script;
    }
    
    public function product_details_shortcode($atts) {
        // Extract shortcode attributes
        $atts = shortcode_atts(array(
            'theme' => 'light',
            'primary_color' => $this->get_option('primary_color', '#065f46'),
            'secondary_color' => $this->get_option('secondary_color', '#10b981'),
            'enable_cart' => 'true',
            'api_key' => $this->get_option('api_key', ''),
            'menu_page_url' => '/dispensary-menu'
        ), $atts);
        
        // Generate a unique ID for this product details instance
        $unique_id = 'bakedbot-product-' . mt_rand(1000, 9999);
        
        // Create the inline script to enhance with React
        $script = '<script>
            // Define enhancement function for product details if not already defined
            if (!window.enhanceBakedBotProduct) {
                window.enhanceBakedBotProduct = function(containerId) {
                    const container = document.getElementById(containerId);
                    if (!container) {
                        console.error("BakedBot: Product container not found:", containerId);
                        return;
                    }
                    
                    console.log("BakedBot Debug: Enhancing product details with ID:", containerId);
                    
                    // Get config
                    const config = window.BakedBotProductConfig || {};
                    console.log("BakedBot Debug: Product config:", config);
                    
                    // Check if required dependencies are available
                    if (typeof React === "undefined") {
                        console.error("BakedBot: React not loaded");
                        return;
                    }
                    
                    if (typeof ReactDOM === "undefined") {
                        console.error("BakedBot: ReactDOM not loaded");
                        return;
                    }
                    
                    if (!window.BakedBotProductDetailsPage) {
                        console.error("BakedBot: BakedBotProductDetailsPage component not found");
                        return;
                    }
                    
                    try {
                        // Import and enhance with React
                        const props = {
                            ...config,
                            isProgressiveEnhancement: true,
                            containerId: containerId
                        };
                        
                        console.log("BakedBot Debug: Creating product details with props:", props);
                        
                        // Create ProductDetailsPage wrapped with CartProvider
                        const productElement = React.createElement(
                            window.CartProvider || React.Fragment,
                            {},
                            React.createElement(window.BakedBotProductDetailsPage, props)
                        );
                        
                        // Progressive enhancement - hydrate existing HTML instead of replacing
                        const root = ReactDOM.createRoot ? ReactDOM.createRoot(container) : null;
                        if (root) {
                            root.render(productElement);
                            console.log("BakedBot Debug: Product details enhanced successfully with createRoot");
                        } else {
                            ReactDOM.render(productElement, container);
                            console.log("BakedBot Debug: Product details enhanced successfully with legacy render");
                        }
                        
                        // Mark as enhanced
                        container.classList.add("enhanced");
                    } catch (error) {
                        console.error("BakedBot Debug: Error enhancing product details:", error);
                    }
                };
            }
            
            document.addEventListener("DOMContentLoaded", function() {
                console.log("BakedBot Debug: Product details initialized with ID: ' . $unique_id . '");
                
                // Set theme values
                if (!window.BakedBotTheme) {
                    window.BakedBotTheme = {
                        primaryColor: "' . esc_js($atts['primary_color']) . '",
                        secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                        backgroundColor: "' . $this->get_option('background_color', '#FFFFFF') . '",
                        headerColor: "' . $this->get_option('header_color', '#FFFFFF') . '",
                        textColor: "' . $this->get_option('text_color', '#2C2C2C') . '"
                    };
                }
                console.log("BakedBot Debug: Product theme set:", window.BakedBotTheme);
                
                // Set configuration values for this product details instance
                ' . $this->get_config_script(array(
                    'embedded' => true,
                    'container_id' => $unique_id,
                    'product_mode' => true
                )) . '
                
                // Set product-specific configuration
                window.BakedBotProductConfig = {
                    theme: "' . esc_js($atts['theme']) . '",
                    primaryColor: "' . esc_js($atts['primary_color']) . '",
                    secondaryColor: "' . esc_js($atts['secondary_color']) . '",
                    enableCart: ' . ($atts['enable_cart'] === 'true' ? 'true' : 'false') . ',
                    apiKey: "' . esc_js($atts['api_key']) . '",
                    menuPageUrl: "' . esc_js($atts['menu_page_url']) . '"
                };
                console.log("BakedBot Debug: Product config set:", window.BakedBotProductConfig);
                
                // Progressive enhancement - enhance existing HTML with React
                function tryEnhanceProduct() {
                    console.log("BakedBot Debug: Attempting to enhance product details...");
                    if (typeof window.enhanceBakedBotProduct === "function") {
                        console.log("BakedBot Debug: Found enhanceBakedBotProduct function, calling it...");
                        window.enhanceBakedBotProduct("' . $unique_id . '");
                        return true;
                    } else if (typeof window.BakedBot?.enhanceProduct === "function") {
                        console.log("BakedBot Debug: Found BakedBot.enhanceProduct function, calling it...");
                        window.BakedBot.enhanceProduct("' . $unique_id . '");
                        return true;
                    }
                    console.log("BakedBot Debug: No product enhancement function found");
                    return false;
                }
                
                // Wait for scripts to load and try to enhance
                console.log("BakedBot Debug: Trying initial product enhancement...");
                if (!tryEnhanceProduct()) {
                    console.log("BakedBot Debug: Enhancement function not found for product details, will retry in 1 second");
                    setTimeout(function() {
                        console.log("BakedBot Debug: Retrying product enhancement after 1 second...");
                        if (!tryEnhanceProduct()) {
                            console.log("BakedBot Debug: Still no enhancement function found, will retry in 3 seconds");
                            setTimeout(function() {
                                console.log("BakedBot Debug: Final product retry after 3 seconds...");
                                if (!tryEnhanceProduct()) {
                                    console.log("BakedBot Debug: Running in non-enhanced mode (SEO-friendly fallback)");
                                }
                            }, 3000);
                        }
                    }, 1000);
                }
            });
        </script>';
        
        // Return a basic container for the React app to mount into
        return '<div id="' . $unique_id . '" class="bakedbot-product-details-container">
            <div class="loading-state">Loading product details...</div>
        </div>' . $script;
    }
    
    private function fetch_products_for_ssr($atts, $page = 1) {
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            return array(
                'products' => array(),
                'pagination' => array(
                    'currentPage' => 1,
                    'totalPages' => 1,
                    'totalProducts' => 0
                )
            );
        }
        
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 10,
        );
        
        // Ensure products_per_page has a default value
        $products_per_page = isset($atts['products_per_page']) ? intval($atts['products_per_page']) : 12;
        
        $query_params = array(
            'page' => $page,
            'limit' => $products_per_page,
        );
        
        if (isset($atts['category']) && !empty($atts['category'])) {
            $query_params['category'] = $atts['category'];
        }
        
        if (isset($atts['brand']) && !empty($atts['brand'])) {
            $query_params['brand'] = $atts['brand'];
        }
        
        $url = 'https://beta.bakedbot.ai/api/public/products?' . http_build_query($query_params);
        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            return array(
                'products' => array(),
                'pagination' => array(
                    'currentPage' => 1,
                    'totalPages' => 1,
                    'totalProducts' => 0
                )
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['products'])) {
            return array(
                'products' => array(),
                'pagination' => array(
                    'currentPage' => 1,
                    'totalPages' => 1,
                    'totalProducts' => 0
                )
            );
        }
        
        // Extract pagination info
        $pagination = isset($data['pagination']) ? $data['pagination'] : array();
        $product_count = isset($data['products']) && is_array($data['products']) ? count($data['products']) : 0;
        $pagination_info = array(
            'currentPage' => isset($pagination['currentPage']) ? $pagination['currentPage'] : (isset($pagination['current_page']) ? $pagination['current_page'] : $page),
            'totalPages' => isset($pagination['totalPages']) ? $pagination['totalPages'] : (isset($pagination['total_pages']) ? $pagination['total_pages'] : 1),
            'totalProducts' => isset($pagination['totalProducts']) ? $pagination['totalProducts'] : (isset($pagination['total']) ? $pagination['total'] : $product_count)
        );
        
        // Convert to grouped product structure if needed
        if (isset($data['products']) && is_array($data['products']) && !empty($data['products']) && 
            isset($data['products'][0]) && !isset($data['products'][0]['variants'])) {
            // Convert individual products to grouped structure for consistency
            $grouped = array();
            foreach ($data['products'] as $product) {
                $meta_sku = $product['meta_sku'];
                if (!isset($grouped[$meta_sku])) {
                    $grouped[$meta_sku] = array(
                        'meta_sku' => $meta_sku,
                        'product_name' => $product['raw_product_name'] ?? $product['product_name'],
                        'brand_name' => $product['brand_name'],
                        'category' => $product['category'],
                        'subcategory' => $product['subcategory'],
                        'image_url' => $product['image_url'],
                        'base_price' => $product['latest_price'],
                        'price_range' => array('min' => $product['latest_price'], 'max' => $product['latest_price']),
                        'percentage_thc' => $product['percentage_thc'],
                        'percentage_cbd' => $product['percentage_cbd'],
                        'description' => $product['description'] ?? '',
                        'variants' => array()
                    );
                }
                
                $grouped[$meta_sku]['variants'][] = array(
                    'id' => $product['id'],
                    'product_id' => $product['product_id'],
                    'display_weight' => $product['display_weight'] ?? '',
                    'latest_price' => $product['latest_price'],
                    'medical' => $product['medical'],
                    'recreational' => $product['recreational']
                );
                
                // Update price range
                $grouped[$meta_sku]['price_range']['min'] = min($grouped[$meta_sku]['price_range']['min'], $product['latest_price']);
                $grouped[$meta_sku]['price_range']['max'] = max($grouped[$meta_sku]['price_range']['max'], $product['latest_price']);
            }
            
            return array(
                'products' => array_values($grouped),
                'pagination' => $pagination_info
            );
        }
        
        // Return the products directly if already in grouped format
        return array(
            'products' => isset($data['products']) && is_array($data['products']) ? $data['products'] : array(),
            'pagination' => $pagination_info
        );
    }
    
    private function fetch_filter_options_for_ssr() {
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            return array(
                'brands' => array(),
                'categories' => array(),
                'subcategories' => array()
            );
        }
        
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 10,
        );
        
        $url = 'https://beta.bakedbot.ai/api/public/products/filters';
        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            return array(
                'brands' => array(),
                'categories' => array(),
                'subcategories' => array()
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !is_array($data)) {
            return array(
                'brands' => array(),
                'categories' => array(),
                'subcategories' => array()
            );
        }
        
        // Ensure all expected keys exist with default empty arrays
        $default_data = array(
            'brands' => array(),
            'categories' => array(),
            'subcategories' => array()
        );
        
        return array_merge($default_data, $data);
    }
    
    private function render_progressive_menu_html($unique_id, $atts, $initial_products, $pagination_info, $current_page) {
        ob_start();
        ?>
        <!-- Smooth transition styles -->
        <style>
        .bakedbot-loading-transition {
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
        }
        .bakedbot-loading-transition.enhancing {
            opacity: 0.7;
        }
        .bakedbot-loading-transition.enhanced {
            opacity: 1;
        }
        .bakedbot-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        .enhanced .bakedbot-skeleton {
            animation: none;
            background: transparent;
        }
        </style>
        
        <div id="<?php echo esc_attr($unique_id); ?>" 
             class="headless-menu bakedbot-headless-menu bakedbot-loading-transition theme-<?php echo esc_attr($atts['theme']); ?>" 
             itemscope itemtype="https://schema.org/Store"
             style="width: 100%; --primary-color: <?php echo esc_attr($atts['primary_color']); ?>; --secondary-color: <?php echo esc_attr($atts['secondary_color']); ?>;"
             data-layout="<?php echo esc_attr($atts['layout']); ?>"
             data-theme="<?php echo esc_attr($atts['theme']); ?>">
            
            <!-- Header matching React SharedHeader structure exactly -->
            <div class="header-container">
                <?php if ($atts['show_title'] === 'true'): ?>
                <div class="header-left">
                    <h1 class="site-title" itemprop="name"><?php echo esc_html($atts['title']); ?></h1>
                </div>
                <?php endif; ?>
                
                <?php if ($atts['enable_search'] === 'true'): ?>
                <div class="header-search-container">
                    <div class="search-bar-container">
                        <input type="text" 
                               class="search-input" 
                               placeholder="Search products..." 
                               value=""
                               aria-label="Search products">
                        <button type="submit" class="search-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="header-right">
                    <?php if ($atts['enable_filters'] === 'true'): ?>
                    <button class="icon-btn filter-toggle" aria-label="Toggle filters">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"/>
                        </svg>
                        <span class="icon-label">Filter</span>
                    </button>
                    <?php endif; ?>
                    
                    <!-- Layout Toggle -->
                    <div class="layout-toggle-container">
                        <button class="icon-btn layout-toggle-btn <?php echo $atts['layout'] === 'grid' ? 'active' : ''; ?>" data-layout="grid">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
                            </svg>
                            <span class="icon-label">Grid</span>
                        </button>
                        <button class="icon-btn layout-toggle-btn <?php echo $atts['layout'] === 'list' ? 'active' : ''; ?>" data-layout="list">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H6v2zM7 7v2h14V7H7z"/>
                            </svg>
                            <span class="icon-label">List</span>
                        </button>
                    </div>
                    
                    <?php if ($atts['enable_cart'] === 'true'): ?>
                    <button class="icon-btn cart-button" aria-label="View cart">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                        <span class="icon-label">Cart</span>
                        <span class="cart-badge" style="display: none;">0</span>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Main Content matching React structure -->
            <div class="menu-content">
                <?php if ($atts['enable_filters'] === 'true'): ?>
                <!-- Filters Sidebar -->
                <div class="filters-sidebar">
                    <div class="sidebar-overlay"></div>
                    <div class="filters-content">
                        <div class="filters-header">
                            <h3>Filters</h3>
                            <button>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                </svg>
                            </button>
                        </div>
                        <div class="filters-scroll-area">
                            <!-- Filter sections will be populated by React -->
                            <div class="filter-section">
                                <div class="filter-section-header">
                                    <h4>Category</h4>
                                    <svg class="filter-section-toggle" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                                    </svg>
                                </div>
                                <div class="filter-section-content">
                                    <!-- Options populated by React -->
                                </div>
                            </div>
                        </div>
                        <div class="filters-footer">
                            <button class="apply-filters-btn" disabled>Apply Filters</button>
                            <button class="clear-filters-btn">Clear All</button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Products Area -->
                <div class="products-area">
                    <div class="results-header">
                        <div class="results-count"><?php echo count($initial_products); ?> products found</div>
                    </div>
                    
                    <!-- Product Grid -->
                    <div class="bakedbot-product-grid layout-<?php echo esc_attr($atts['layout']); ?> mobile-bakedbot-product-grid">
                        <?php if (empty($initial_products)): ?>
                            <div class="empty-state">No products found</div>
                        <?php else: ?>
                            <?php foreach ($initial_products as $product): ?>
                                <?php $this->render_product_card_ssr($product, $atts); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <!-- VIP Club Signup Section -->
                    <div class="vip-signup-section">
                        <div class="vip-signup-container">
                            <div class="vip-header">
                                <h3>🌿 Join Our VIP Club</h3>
                                <p>Get exclusive AI recommendations, member pricing, and priority delivery access</p>
                            </div>
                            <form class="vip-signup-form" id="vip-signup-form-<?php echo esc_attr($unique_id); ?>">
                                <div class="vip-form-fields">
                                    <input type="text" placeholder="First Name" name="firstName" required>
                                    <input type="email" placeholder="Email Address" name="email" required>
                                    <input type="tel" placeholder="Phone Number" name="phone" required>
                                </div>
                                <button type="submit" class="vip-activate-btn">
                                    🚀 Activate VIP Access
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- VIP Success Section (initially hidden) -->
                    <div class="vip-success-section" style="display: none;" id="vip-success-<?php echo esc_attr($unique_id); ?>">
                        <div class="vip-success-container">
                            <div class="vip-success-icon">✅</div>
                            <h3>Welcome to VIP Club!</h3>
                            <p>You're all set! Check your email for exclusive offers and member benefits.</p>
                        </div>
                    </div>
                    
                    <!-- SEO-Friendly Pagination -->
                    <div class="pagination-container">
                        <?php echo $this->render_seo_pagination($pagination_info, $current_page, $atts); ?>
                    </div>
                </div>
            </div>
            
            <!-- Cart Sidebar -->
            <?php if ($atts['enable_cart'] === 'true'): ?>
            <div class="cart-sidebar">
                <div class="sidebar-overlay"></div>
                <div class="sidebar-content">
                    <div class="sidebar-header">
                        <h2>Your Cart</h2>
                        <button>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="sidebar-body">
                        <div class="empty-cart">Your cart is empty</div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Floating Cart -->
            <?php if ($atts['enable_cart'] === 'true'): ?>
            <div class="floating-cart" style="display: none;">
                <div class="cart-summary">
                    <span class="cart-info">0 items • $0.00</span>
                    <button class="checkout-btn">Checkout</button>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function render_progressive_homepage_html($unique_id, $atts, $initial_products, $categories) {
        ob_start();
        ?>
        <div id="<?php echo esc_attr($unique_id); ?>" 
             class="headless-homepage bakedbot-headless-homepage theme-<?php echo esc_attr($atts['theme']); ?>" 
             itemscope itemtype="https://schema.org/Store"
             style="width: 100%; --primary-color: <?php echo esc_attr($atts['primary_color']); ?>; --secondary-color: <?php echo esc_attr($atts['secondary_color']); ?>;"
             data-theme="<?php echo esc_attr($atts['theme']); ?>">
            
            <!-- Hero Section -->
            <?php if ($atts['show_promotion'] === 'true'): ?>
            <section class="hero-section">
                <div class="hero-content">
                    <div class="hero-text">
                        <h1 class="hero-title" itemprop="name"><?php echo esc_html($atts['promotion_title']); ?></h1>
                        <p class="hero-subtitle"><?php echo esc_html($atts['promotion_subtitle']); ?></p>
                        <button class="hero-cta">Shop Now</button>
                    </div>
                    <?php if (!empty($atts['promotion_image_url'])): ?>
                    <div class="hero-image">
                        <img src="<?php echo esc_url($atts['promotion_image_url']); ?>" alt="<?php echo esc_attr($atts['promotion_title']); ?>">
                    </div>
                    <?php endif; ?>
                </div>
            </section>
            <?php endif; ?>
            
            <!-- Categories Section -->
            <section class="categories-section">
                <div class="section-header">
                    <h2>Shop by Category</h2>
                    <button class="view-all-btn">View All Categories</button>
                </div>
                <div class="categories-scroll">
                    <?php foreach ($categories as $category): ?>
                    <div class="category-card" data-category="<?php echo esc_attr($category); ?>">
                        <div class="category-icon">
                            <?php if (stripos($category, 'flower') !== false): ?>
                                🌿
                            <?php elseif (stripos($category, 'edible') !== false): ?>
                                🍪
                            <?php elseif (stripos($category, 'concentrate') !== false): ?>
                                🔥
                            <?php else: ?>
                                🏷️
                            <?php endif; ?>
                        </div>
                        <h3 class="category-name"><?php echo esc_html($category); ?></h3>
                        <p class="category-count">Products available</p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            
            <!-- Featured Products Section -->
            <section class="featured-section">
                <div class="section-header">
                    <h2>⭐ Featured Products</h2>
                    <button class="view-all-btn">View All Products</button>
                </div>
                <div class="products-scroll featured-scroll">
                    <?php if (empty($initial_products)): ?>
                        <div class="empty-state">Loading featured products...</div>
                    <?php else: ?>
                        <?php foreach ($initial_products as $product): ?>
                            <?php $this->render_homepage_product_card_ssr($product, $atts); ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </section>
            
            <!-- VIP Club Signup Section -->
            <div class="vip-signup-section">
                <div class="vip-signup-container">
                    <div class="vip-header">
                        <h3>🌿 Join Our VIP Club</h3>
                        <p>Get exclusive AI recommendations, member pricing, and priority delivery access</p>
                    </div>
                    <form class="vip-signup-form" id="vip-signup-form-<?php echo esc_attr($unique_id); ?>">
                        <div class="vip-form-fields">
                            <input type="text" placeholder="First Name" name="firstName" required>
                            <input type="email" placeholder="Email Address" name="email" required>
                            <input type="tel" placeholder="Phone Number" name="phone" required>
                        </div>
                        <button type="submit" class="vip-activate-btn">
                            🚀 Activate VIP Access
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- VIP Success Section (initially hidden) -->
            <div class="vip-success-section" style="display: none;" id="vip-success-<?php echo esc_attr($unique_id); ?>">
                <div class="vip-success-container">
                    <div class="vip-success-icon">✅</div>
                    <h3>Welcome to VIP Club!</h3>
                    <p>You're all set! Check your email for exclusive offers and member benefits.</p>
                </div>
            </div>
            
            <!-- Loading States for Other Sections -->
            <section class="category-products-section">
                <div class="section-header">
                    <h2>Popular Products</h2>
                    <button class="view-all-btn">View More</button>
                </div>
                <div class="products-scroll category-scroll">
                    <div class="loading-placeholder">Loading more products...</div>
                </div>
            </section>
            
            <!-- Footer/Call to Action -->
            <section class="homepage-footer">
                <div class="footer-content">
                    <h3>Discover Premium Cannabis</h3>
                    <p>Explore our full catalog of premium cannabis products, carefully curated for quality and potency.</p>
                    <button class="footer-cta">Browse All Products</button>
                </div>
            </section>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function render_homepage_product_card_ssr($product, $atts) {
        $selected_variant = isset($product['variants'][0]) ? $product['variants'][0] : null;
        $display_price = $selected_variant ? $selected_variant['latest_price'] : $product['base_price'];
        
        // Check if this is a special offer (featured, best value, or discount)
        $has_discount = isset($product['original_price']) && $product['original_price'] > $display_price;
        $is_special_offer = $product['featured'] ?? false || $product['best_value'] ?? false || $has_discount;
        ?>
        <div class="bb-sm-product-item p-[5px] flex flex-col rounded-lg overflow-hidden <?php echo $is_special_offer ? 'border-2 border-[#65715F]' : ''; ?>" 
             itemscope 
             itemtype="https://schema.org/Product"
             data-product-id="<?php echo esc_attr($product['meta_sku']); ?>"
             data-meta-sku="<?php echo esc_attr($product['meta_sku']); ?>">
            
            <div class="relative">
                <img src="<?php echo esc_url($product['image_url']); ?>" 
                     alt="<?php echo esc_attr($product['product_name']); ?>"
                     itemprop="image"
                     loading="lazy"
                     class="w-full h-full object-cover cursor-pointer">
                
                <div class="product-badges">
                    <?php if (isset($product['percentage_thc']) && $product['percentage_thc'] > 20): ?>
                    <div class="product-badge high-thc">
                        🔥 High THC
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($product['percentage_cbd']) && $product['percentage_cbd'] > 10): ?>
                    <div class="product-badge high-cbd">
                        🌿 High CBD
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="bb-sm-product-item-content">
                <p class="font-medium text-md cursor-pointer mb-1 line-clamp-2" itemprop="name">
                    <?php echo esc_html($product['product_name']); ?>
                </p>
                
                <p class="mb-1 flex justify-between">
                    <span><?php echo esc_html($product['category'] ?? ''); ?></span>
                    <?php if (!empty($product['display_weight'])): ?>
                    <span class="text-xs bg-gray-100 rounded-full px-2 py-0.5">
                        <?php echo esc_html($product['display_weight']); ?>
                    </span>
                    <?php endif; ?>
                </p>
                
                <div class="font-medium text-md mb-2 flex items-center">
                    <span class="text-[#65715F] font-bold" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
                        <span itemprop="price" content="<?php echo esc_attr($display_price); ?>">
                            $<?php echo esc_html(number_format($display_price, 2)); ?>
                        </span>
                        <meta itemprop="priceCurrency" content="USD">
                        <meta itemprop="availability" content="https://schema.org/InStock">
                    </span>
                    <?php if ($has_discount && isset($product['original_price'])): ?>
                    <span class="text-gray-500 text-sm line-through ml-2">
                        $<?php echo esc_html(number_format($product['original_price'], 2)); ?>
                    </span>
                    <?php endif; ?>
                </div>
                
                <p class="text-sm mb-3 line-clamp-2"><?php echo esc_html($product['description'] ?? ''); ?></p>
                
                <?php if (isset($product['variants']) && count($product['variants']) > 1): ?>
                <div class="variant-selector mb-2">
                    <select class="w-full text-sm border rounded px-2 py-1">
                        <?php foreach ($product['variants'] as $variant): ?>
                        <option value="<?php echo esc_attr($variant['id']); ?>">
                            <?php echo esc_html($variant['display_weight']); ?> - $<?php echo esc_html($variant['latest_price']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <?php if ($atts['enable_cart'] === 'true'): ?>
                <button class="w-full py-1 <?php echo $is_special_offer ? 'bb-sm-special-add-button bg-[#65715F] text-white' : 'bb-sm-add-to-cart-button'; ?>" 
                        data-product-id="<?php echo esc_attr($product['meta_sku']); ?>"
                        aria-label="Add <?php echo esc_attr($product['product_name']); ?> to cart">
                    <?php echo $is_special_offer ? 'GRAB DEAL' : 'Add to cart'; ?>
                </button>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    private function render_seo_pagination($pagination_info, $current_page, $atts) {
        $total_pages = isset($pagination_info['totalPages']) ? intval($pagination_info['totalPages']) : 1;
        $total_products = isset($pagination_info['totalProducts']) ? intval($pagination_info['totalProducts']) : 0;
        
        if ($total_pages <= 1) {
            return '<div class="pagination-info">Showing all ' . $total_products . ' products</div>';
        }
        
        ob_start();
        
        // Get the current URL without page parameter
        $base_url = remove_query_arg('menu_page');
        
        ?>
        <nav class="pagination" role="navigation" aria-label="Products pagination">
            <div class="pagination-info">
                Page <?php echo $current_page; ?> of <?php echo $total_pages; ?> 
                (<?php echo $total_products; ?> total products)
            </div>
            
            <div class="pagination-links">
                <?php if ($current_page > 1): ?>
                    <a href="<?php echo esc_url(($current_page == 2) ? $base_url : add_query_arg('menu_page', $current_page - 1, $base_url)); ?>" 
                       class="pagination-btn pagination-prev" 
                       rel="prev">
                        ← Previous
                    </a>
                <?php else: ?>
                    <span class="pagination-btn pagination-prev disabled">← Previous</span>
                <?php endif; ?>
                
                <div class="pagination-pages">
                    <?php
                    // Show page numbers (max 5 around current page)
                    $start_page = max(1, $current_page - 2);
                    $end_page = min($total_pages, $current_page + 2);
                    
                    // Show first page if not in range
                    if ($start_page > 1): ?>
                        <a href="<?php echo esc_url($base_url); ?>" class="pagination-btn" rel="first">1</a>
                        <?php if ($start_page > 2): ?>
                            <span class="pagination-ellipsis">…</span>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <?php if ($i == $current_page): ?>
                            <span class="pagination-btn active" aria-current="page"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="<?php echo esc_url(($i == 1) ? $base_url : add_query_arg('menu_page', $i, $base_url)); ?>" 
                               class="pagination-btn"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <!-- Show last page if not in range -->
                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <span class="pagination-ellipsis">…</span>
                        <?php endif; ?>
                        <a href="<?php echo esc_url(add_query_arg('menu_page', $total_pages, $base_url)); ?>" 
                           class="pagination-btn" rel="last"><?php echo $total_pages; ?></a>
                    <?php endif; ?>
                </div>
                
                <?php if ($current_page < $total_pages): ?>
                    <a href="<?php echo esc_url(add_query_arg('menu_page', $current_page + 1, $base_url)); ?>" 
                       class="pagination-btn pagination-next" 
                       rel="next">
                        Next →
                    </a>
                <?php else: ?>
                    <span class="pagination-btn pagination-next disabled">Next →</span>
                <?php endif; ?>
            </div>
        </nav>
        <?php
        
        return ob_get_clean();
    }
    
    private function render_product_card_ssr($product, $atts) {
        $selected_variant = isset($product['variants'][0]) ? $product['variants'][0] : null;
        $display_price = $selected_variant ? $selected_variant['latest_price'] : $product['base_price'];
        
        // Check if this is a special offer (featured, best value, or discount)
        $has_discount = isset($product['original_price']) && $product['original_price'] > $display_price;
        $is_special_offer = $product['featured'] ?? false || $product['best_value'] ?? false || $has_discount;
        ?>
        <div class="bb-sm-product-item p-[5px] flex flex-col rounded-lg overflow-hidden <?php echo $is_special_offer ? 'border-2 border-[#65715F]' : ''; ?>" 
             itemscope 
             itemtype="https://schema.org/Product"
             data-product-id="<?php echo esc_attr($product['meta_sku']); ?>"
             data-meta-sku="<?php echo esc_attr($product['meta_sku']); ?>">
            
            <div class="relative">
                <img src="<?php echo esc_url($product['image_url']); ?>" 
                     alt="<?php echo esc_attr($product['product_name']); ?>"
                     itemprop="image"
                     loading="lazy"
                     class="w-full h-full object-cover cursor-pointer">
            </div>
            
            <div class="bb-sm-product-item-content">
                <p class="font-medium text-md cursor-pointer mb-1 line-clamp-2" itemprop="name">
                    <?php echo esc_html($product['product_name']); ?>
                </p>
                
                <p class="mb-1 flex justify-between">
                    <span><?php echo esc_html($product['category'] ?? ''); ?></span>
                    <?php if (!empty($product['display_weight'])): ?>
                    <span class="text-xs bg-gray-100 rounded-full px-2 py-0.5">
                        <?php echo esc_html($product['display_weight']); ?>
                    </span>
                    <?php endif; ?>
                </p>
                
                <div class="font-medium text-md mb-2 flex items-center">
                    <span class="text-[#65715F] font-bold" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
                        <span itemprop="price" content="<?php echo esc_attr($display_price); ?>">
                            $<?php echo esc_html(number_format($display_price, 2)); ?>
                        </span>
                        <meta itemprop="priceCurrency" content="USD">
                        <meta itemprop="availability" content="https://schema.org/InStock">
                    </span>
                    <?php if ($has_discount && isset($product['original_price'])): ?>
                    <span class="text-gray-500 text-sm line-through ml-2">
                        $<?php echo esc_html(number_format($product['original_price'], 2)); ?>
                    </span>
                    <?php endif; ?>
                </div>
                
                <p class="text-sm mb-3 line-clamp-2"><?php echo esc_html($product['description'] ?? ''); ?></p>
                
                <?php if (isset($product['variants']) && count($product['variants']) > 1): ?>
                <div class="variant-selector mb-2">
                    <select class="w-full text-sm border rounded px-2 py-1">
                        <?php foreach ($product['variants'] as $variant): ?>
                        <option value="<?php echo esc_attr($variant['id']); ?>">
                            <?php echo esc_html($variant['display_weight']); ?> - $<?php echo esc_html($variant['latest_price']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <?php if ($atts['enable_cart'] === 'true'): ?>
                <button class="w-full py-1 <?php echo $is_special_offer ? 'bb-sm-special-add-button bg-[#65715F] text-white' : 'bb-sm-add-to-cart-button'; ?>" 
                        data-product-id="<?php echo esc_attr($product['meta_sku']); ?>"
                        aria-label="Add <?php echo esc_attr($product['product_name']); ?> to cart">
                    <?php echo $is_special_offer ? 'GRAB DEAL' : 'Add to cart'; ?>
                </button>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    // Keep the old function for backward compatibility but mark as deprecated
    private function render_seo_menu_html($unique_id, $atts, $products_html) {
        ob_start();
        ?>
        <div id="<?php echo esc_attr($unique_id); ?>" 
             class="bakedbot-headless-menu seo-friendly theme-<?php echo esc_attr($atts['theme']); ?>" 
             itemscope itemtype="https://schema.org/Store"
             style="<?php echo $atts['height'] !== 'auto' ? 'height: ' . esc_attr($atts['height']) . ';' : ''; ?> width: 100%;"
             data-layout="<?php echo esc_attr($atts['layout']); ?>"
             data-theme="<?php echo esc_attr($atts['theme']); ?>">
            
            <?php if ($atts['show_title'] === 'true'): ?>
            <header class="menu-header">
                <div class="header-left">
                    <h1 class="menu-title" itemprop="name"><?php echo esc_html($atts['title']); ?></h1>
                </div>
                
                <?php if ($atts['enable_search'] === 'true'): ?>
                <div class="header-center">
                    <div class="search-container">
                        <form role="search" method="get" class="search-form">
                            <label for="product-search-<?php echo esc_attr($unique_id); ?>" class="screen-reader-text">Search products</label>
                            <input type="search" 
                                   id="product-search-<?php echo esc_attr($unique_id); ?>"
                                   class="search-input" 
                                   placeholder="Search products..." 
                                   name="product_search"
                                   aria-label="Search products">
                            <button type="submit" class="search-submit" aria-label="Search">
                                <span class="search-icon" aria-hidden="true">🔍</span>
                            </button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="header-right">
                    <?php if ($atts['enable_filters'] === 'true'): ?>
                    <button class="filter-toggle" aria-label="Toggle filters" aria-expanded="false">
                        <span aria-hidden="true">🔽</span>
                        <span>Filters</span>
                    </button>
                    <?php endif; ?>
                    
                    <?php if ($atts['enable_cart'] === 'true'): ?>
                    <button class="cart-button" aria-label="View cart">
                        <span aria-hidden="true">🛒</span>
                        <span class="cart-badge" style="display: none;">0</span>
                    </button>
                    <?php endif; ?>
                </div>
            </header>
            <?php endif; ?>
            
            <main class="menu-content">
                <?php if ($atts['enable_filters'] === 'true'): ?>
                <aside class="filters-sidebar" aria-label="Product filters">
                    <div class="filters-content">
                        <h2>Filter Products</h2>
                        <div class="filter-section">
                            <h3>Category</h3>
                            <div class="filter-options" role="group" aria-labelledby="category-filter">
                                <!-- Filter options will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </aside>
                <?php endif; ?>
                
                <section class="products-area" aria-label="Products">
                    <div class="results-header">
                        <div class="results-count" role="status" aria-live="polite">
                            Loading products...
                        </div>
                    </div>
                    
                    <?php echo $products_html; ?>
                    
                    <nav class="pagination" role="navigation" aria-label="Products pagination">
                        <!-- Pagination will be populated by JavaScript -->
                    </nav>
                </section>
            </main>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function render_products_ssr($products, $atts) {
        if (empty($products)) {
            return '<div class="empty-state">No products found.</div>';
        }
        
        ob_start();
        ?>
        <div class="bakedbot-bakedbot-product-grid layout-<?php echo esc_attr($atts['layout']); ?> mobile-bakedbot-product-grid" itemscope itemtype="https://schema.org/ItemList">
            <?php foreach ($products as $index => $product): ?>
            <article class="bb-sm-product-item p-[5px] flex flex-col rounded-lg overflow-hidden" 
                     itemscope 
                     itemtype="https://schema.org/Product"
                     itemprop="itemListElement"
                     data-product-id="<?php echo esc_attr($product['meta_sku']); ?>"
                     data-meta-sku="<?php echo esc_attr($product['meta_sku']); ?>">
                
                <meta itemprop="position" content="<?php echo $index + 1; ?>">
                
                <div class="product-image">
                    <img src="<?php echo esc_url($product['image_url']); ?>" 
                         alt="<?php echo esc_attr($product['product_name']); ?>"
                         itemprop="image"
                         loading="lazy"
                         width="280" 
                         height="200">
                </div>
                
                <div class="product-info">
                    <h3 class="product-name" itemprop="name">
                        <a href="<?php echo esc_url($this->get_product_url($product)); ?>" itemprop="url">
                            <?php echo esc_html($product['product_name']); ?>
                        </a>
                    </h3>
                    
                    <?php if (!empty($product['brand_name'])): ?>
                    <p class="product-brand" itemprop="brand" itemscope itemtype="https://schema.org/Brand">
                        <span itemprop="name"><?php echo esc_html($product['brand_name']); ?></span>
                    </p>
                    <?php endif; ?>
                    
                    <div class="product-details">
                        <?php if (isset($product['percentage_thc'])): ?>
                        <span class="thc-content">THC: <?php echo esc_html($product['percentage_thc']); ?>%</span>
                        <?php endif; ?>
                        
                        <?php if (isset($product['percentage_cbd'])): ?>
                        <span class="cbd-content">CBD: <?php echo esc_html($product['percentage_cbd']); ?>%</span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-actions">
                        <div class="product-price" itemprop="offers" itemscope itemtype="https://schema.org/Offer">
                            <?php if (isset($product['price_range']) && $product['price_range']['min'] != $product['price_range']['max']): ?>
                                <span class="price-range">
                                    $<?php echo esc_html(number_format($product['price_range']['min'], 2)); ?> - 
                                    $<?php echo esc_html(number_format($product['price_range']['max'], 2)); ?>
                                </span>
                                <meta itemprop="price" content="<?php echo esc_attr($product['base_price']); ?>">
                            <?php else: ?>
                                <span itemprop="price" content="<?php echo esc_attr($product['base_price']); ?>">
                                    $<?php echo esc_html(number_format($product['base_price'], 2)); ?>
                                </span>
                            <?php endif; ?>
                            <meta itemprop="priceCurrency" content="USD">
                            <meta itemprop="availability" content="https://schema.org/InStock">
                        </div>
                        
                        <?php if ($atts['enable_cart'] === 'true'): ?>
                        <button class="add-to-cart-btn" 
                                data-product-id="<?php echo esc_attr($product['id']); ?>"
                                aria-label="Add <?php echo esc_attr($product['product_name']); ?> to cart">
                            Add to Cart
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </article>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
    
    private function generate_structured_data($products) {
        $structured_data = array(
            '@context' => 'https://schema.org',
            '@type' => 'ItemList',
            'name' => 'Cannabis Products',
            'description' => 'Premium cannabis products available for purchase',
            'numberOfItems' => count($products),
            'itemListElement' => array()
        );
        
        foreach ($products as $index => $product) {
            $structured_data['itemListElement'][] = array(
                '@type' => 'Product',
                'position' => $index + 1,
                'name' => $product['product_name'],
                'description' => isset($product['description']) ? $product['description'] : $product['product_name'],
                'image' => $product['image_url'],
                'brand' => array(
                    '@type' => 'Brand',
                    'name' => isset($product['brand_name']) ? $product['brand_name'] : 'Unknown'
                ),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product['latest_price'],
                    'priceCurrency' => 'USD',
                    'availability' => 'https://schema.org/InStock'
                ),
                'additionalProperty' => array()
            );
            
            if (isset($product['percentage_thc'])) {
                $structured_data['itemListElement'][$index]['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'THC Percentage',
                    'value' => $product['percentage_thc'] . '%'
                );
            }
            
            if (isset($product['percentage_cbd'])) {
                $structured_data['itemListElement'][$index]['additionalProperty'][] = array(
                    '@type' => 'PropertyValue',
                    'name' => 'CBD Percentage',
                    'value' => $product['percentage_cbd'] . '%'
                );
            }
        }
        
        return $structured_data;
    }
    
    private function get_product_url($product) {
        // Generate SEO-friendly product URLs
        $slug = sanitize_title($product['product_name']);
        return add_query_arg(array(
            'product_id' => $product['id'],
            'product_slug' => $slug
        ), get_permalink());
    }
    
    public function add_seo_features() {
        // Add rewrite rules for SEO-friendly product URLs
        add_rewrite_rule(
            '^cannabis-products/([^/]+)/?$',
            'index.php?bakedbot_product=$matches[1]',
            'top'
        );
        
        // Add query var
        add_filter('query_vars', array($this, 'add_query_vars'));
        
        // Handle product page requests
        add_action('template_redirect', array($this, 'handle_product_page'));
        
        // Add sitemap for products
        add_action('wp_ajax_bakedbot_sitemap', array($this, 'generate_sitemap'));
        add_action('wp_ajax_nopriv_bakedbot_sitemap', array($this, 'generate_sitemap'));
        
        // Register rewrite endpoint for sitemap
        add_rewrite_rule(
            '^bakedbot-sitemap\.xml$',
            'index.php?bakedbot_sitemap=1',
            'top'
        );
        
        // Flush rewrite rules if needed
        if (get_option('bakedbot_rewrite_rules_flushed') !== '1') {
            flush_rewrite_rules();
            update_option('bakedbot_rewrite_rules_flushed', '1');
        }
    }
    
    public function add_query_vars($vars) {
        $vars[] = 'bakedbot_product';
        $vars[] = 'bakedbot_sitemap';
        return $vars;
    }
    
    public function handle_product_page() {
        $product_slug = get_query_var('bakedbot_product');
        $sitemap = get_query_var('bakedbot_sitemap');
        
        if ($sitemap) {
            $this->generate_sitemap();
            exit;
        }
        
        if ($product_slug) {
            // Extract product ID from URL parameters or slug
            $product_id = isset($_GET['product_id']) ? sanitize_text_field($_GET['product_id']) : null;
            
            if ($product_id) {
                $this->render_product_page($product_id, $product_slug);
                exit;
            }
        }
    }
    
    public function render_product_page($product_id, $product_slug) {
        // Fetch product data
        $product = $this->fetch_single_product($product_id);
        
        if (!$product) {
            wp_redirect(home_url(), 404);
            exit;
        }
        
        // Set up SEO meta tags
        add_filter('wp_title', function() use ($product) {
            return $product['product_name'] . ' | Cannabis Products';
        });
        
        add_action('wp_head', function() use ($product) {
            echo '<meta name="description" content="' . esc_attr($product['product_name']) . ' - Premium cannabis product available for purchase.">' . "\n";
            echo '<meta property="og:title" content="' . esc_attr($product['product_name']) . '">' . "\n";
            echo '<meta property="og:description" content="' . esc_attr($product['product_name']) . ' - Premium cannabis product.">' . "\n";
            echo '<meta property="og:image" content="' . esc_url($product['image_url']) . '">' . "\n";
            echo '<meta property="og:type" content="product">' . "\n";
            
            // Structured data for product
            $structured_data = array(
                '@context' => 'https://schema.org',
                '@type' => 'Product',
                'name' => $product['product_name'],
                'description' => isset($product['description']) ? $product['description'] : $product['product_name'],
                'image' => $product['image_url'],
                'brand' => array(
                    '@type' => 'Brand',
                    'name' => isset($product['brand_name']) ? $product['brand_name'] : 'Unknown'
                ),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product['latest_price'],
                    'priceCurrency' => 'USD',
                    'availability' => 'https://schema.org/InStock'
                )
            );
            
            echo '<script type="application/ld+json">' . json_encode($structured_data) . '</script>' . "\n";
        });
        
        // Load a simple product template
        get_header();
        ?>
        <div class="bakedbot-product-page">
            <div class="container">
                <div class="product-single">
                    <div class="product-image">
                        <img src="<?php echo esc_url($product['image_url']); ?>" 
                             alt="<?php echo esc_attr($product['product_name']); ?>">
                    </div>
                    <div class="product-info">
                        <h1><?php echo esc_html($product['product_name']); ?></h1>
                        <?php if (!empty($product['brand_name'])): ?>
                        <p class="brand">Brand: <?php echo esc_html($product['brand_name']); ?></p>
                        <?php endif; ?>
                        
                        <div class="product-details">
                            <?php if (isset($product['percentage_thc'])): ?>
                            <span>THC: <?php echo esc_html($product['percentage_thc']); ?>%</span>
                            <?php endif; ?>
                            
                            <?php if (isset($product['percentage_cbd'])): ?>
                            <span>CBD: <?php echo esc_html($product['percentage_cbd']); ?>%</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="price">
                            $<?php echo esc_html(number_format($product['latest_price'], 2)); ?>
                        </div>
                        
                        <button class="add-to-cart-btn" data-product-id="<?php echo esc_attr($product['id']); ?>">
                            Add to Cart
                        </button>
                        
                        <a href="<?php echo home_url(); ?>" class="back-to-menu">← Back to Products</a>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .bakedbot-product-page {
            padding: 2rem;
            margin: 0 auto;
        }
        .product-single {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: start;
        }
        .product-image img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        .product-info h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .brand {
            color: #666;
            margin-bottom: 1rem;
        }
        .product-details {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        .price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #065f46;
            margin-bottom: 1rem;
        }
        .add-to-cart-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin-bottom: 1rem;
            display: block;
        }
        .back-to-menu {
            color: #065f46;
            text-decoration: none;
        }
        @media (max-width: 768px) {
            .product-single {
                grid-template-columns: 1fr;
            }
        }
        </style>
        <?php
        get_footer();
    }
    
    private function fetch_single_product($product_id) {
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            return null;
        }
        
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 10,
        );
        
        $url = 'https://beta.bakedbot.ai/api/public/products/' . urlencode($product_id);
        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            return null;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        return isset($data['product']) ? $data['product'] : null;
    }
    
    public function generate_sitemap() {
        header('Content-Type: application/xml; charset=utf-8');
        
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            echo '<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>';
            exit;
        }
        
        // Fetch products for sitemap
        $products = $this->fetch_products_for_sitemap();
        
        echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // Add individual product URLs
        foreach ($products as $product) {
            $product_url = home_url('/cannabis-products/' . sanitize_title($product['product_name']) . '/?product_id=' . urlencode($product['id']));
            echo '<url>' . "\n";
            echo '<loc>' . esc_url($product_url) . '</loc>' . "\n";
            echo '<lastmod>' . date('c', strtotime($product['updated_at'])) . '</lastmod>' . "\n";
            echo '<changefreq>weekly</changefreq>' . "\n";
            echo '<priority>0.8</priority>' . "\n";
            echo '</url>' . "\n";
        }
        
        // Add paginated menu URLs
        $this->add_paginated_menu_urls_to_sitemap();
        
        echo '</urlset>' . "\n";
        exit;
    }
    
    private function add_paginated_menu_urls_to_sitemap() {
        // Find pages that contain the bakedbot_menu shortcode
        $pages_with_menu = get_posts(array(
            'post_type' => 'page',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            's' => '[bakedbot_menu'
        ));
        
        foreach ($pages_with_menu as $page) {
            // Check if page actually contains the shortcode
            if (strpos($page->post_content, '[bakedbot_menu') !== false) {
                $base_url = get_permalink($page->ID);
                
                // Add base page (page 1)
                echo '<url>' . "\n";
                echo '<loc>' . esc_url($base_url) . '</loc>' . "\n";
                echo '<lastmod>' . date('c', strtotime($page->post_modified)) . '</lastmod>' . "\n";
                echo '<changefreq>daily</changefreq>' . "\n";
                echo '<priority>0.9</priority>' . "\n";
                echo '</url>' . "\n";
                
                // Estimate total pages (you might want to make this dynamic)
                $api_key = $this->get_option('api_key', '');
                if (!empty($api_key)) {
                    $estimated_total_pages = $this->get_estimated_total_pages();
                    
                    // Add paginated URLs
                    for ($page_num = 2; $page_num <= min($estimated_total_pages, 50); $page_num++) {
                        $paginated_url = add_query_arg('menu_page', $page_num, $base_url);
                        echo '<url>' . "\n";
                        echo '<loc>' . esc_url($paginated_url) . '</loc>' . "\n";
                        echo '<lastmod>' . date('c', strtotime($page->post_modified)) . '</lastmod>' . "\n";
                        echo '<changefreq>daily</changefreq>' . "\n";
                        echo '<priority>0.7</priority>' . "\n";
                        echo '</url>' . "\n";
                    }
                }
            }
        }
    }
    
    private function get_estimated_total_pages($products_per_page = 12) {
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            return 1;
        }
        
        // Quick API call to get total count
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 10,
        );
        
        $url = 'https://beta.bakedbot.ai/api/public/products?limit=1'; // Just get count
        $response = wp_remote_get($url, $args);
        
        if (is_wp_error($response)) {
            return 1;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (isset($data['pagination']['totalProducts'])) {
            return ceil($data['pagination']['totalProducts'] / $products_per_page);
        } elseif (isset($data['pagination']['total'])) {
            return ceil($data['pagination']['total'] / $products_per_page);
        }
        
        return 1;
    }
    
    private function fetch_products_for_sitemap() {
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            return array();
        }
        
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 30,
        );
        
        $all_products = array();
        $page = 1;
        $max_pages = 10; // Limit to prevent timeouts
        
        do {
            $url = 'https://beta.bakedbot.ai/api/public/products?page=' . $page . '&limit=100';
            $response = wp_remote_get($url, $args);
            
            if (is_wp_error($response)) {
                break;
            }
            
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            if (!$data || !isset($data['products'])) {
                break;
            }
            
            // Add products directly from the API response
            $all_products = array_merge($all_products, $data['products']);
            
            $has_more = isset($data['pagination']) && $data['pagination']['current_page'] < $data['pagination']['total_pages'];
            $page++;
            
        } while ($has_more && $page <= $max_pages);
        
        return $all_products;
    }
    
    public function add_chatbot_container() {
        // This function is deprecated and no longer used.
        // The chatbot is now added via conditionally_add_chatbot_container() in the footer.
    }
    
    public function add_admin_menu() {
        // Base64 encoded svg version of our logo, cropped to a circle for better menu appearance
        $menu_icon = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+PGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiMwNTc1NDAiLz48cGF0aCBkPSJNMTAuNSA0LjVjMi40ODUgMCA0LjUgMi4wMTUgNC41IDQuNXMtMi4wMTUgNC41LTQuNSA0LjVTNiAxMS40ODUgNiA5IDguMDE1IDQuNSAxMC41IDQuNXptLS44IDEuOGEuNi42IDAgMDAtLjYuNi42LjYgMCAwMC42LjZoMS42YS42LjYgMCAwMC42LS42LjYuNiAwIDAwLS42LS42aC0xLjZ6bS0xLjkgMi4xYy0uNDk3IDAtLjkuNDAzLS45LjlzLjQwMy45LjkuOS45LS40MDMuOS0uOS0uNDAzLS45LS45LS45em00LjYgMGMtLjQ5NyAwLS45LjQwMy0uOS45cy40MDMuOS45LjkuOS0uNDAzLjktLjktLjQwMy0uOS0uOS0uOXptMS4xIDIuNGgtNS43Yy0uMTY2IDAtLjMuMTM0LS4zLjNhMi40MDMgMi40MDMgMCAwMDIuNCAyLjRoMS41YTIuNDAzIDIuNDAzIDAgMDAyLjQtMi40YzAtLjE2Ni0uMTM0LS4zLS4zLS4zeiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==';
        
        add_menu_page(
            'BakedBot Settings',
            'BakedBot',
            'manage_options',
            'bakedbot-settings',
            array($this, 'display_settings_page'),
            $menu_icon,
            85
        );
    }
    
    public function register_settings() {
        // Register settings with custom sanitize callback to preserve other tab settings
        register_setting(
            'bakedbot_chatbot_options',
            $this->option_name,
            array($this, 'sanitize_settings')
        );

        // Add sections for Appearance tab
        add_settings_section(
            'appearance_section',
            '', // Remove the title as we'll handle it in the display function
            array($this, 'appearance_section_callback'),
            'bakedbot_chatbot'
        );

        // Add separate sections for color fields (for internal use with our column layout)
        foreach (array('primary_color', 'secondary_color', 'background_color', 'header_color', 'text_color') as $color_field) {
            add_settings_section(
                'appearance_section_' . $color_field,
                '', // No title needed as we'll handle this in the column layout
                null,
                'bakedbot_chatbot'
            );
        }
        
        // Add separate sections for display option fields (for internal use with our column layout)
        foreach (array('chatbot_title', 'welcome_message', 'button_text', 'avatar_url', 'disable_theme_settings') as $display_field) {
            add_settings_section(
                'appearance_section_' . $display_field,
                '', // No title needed as we'll handle this in the column layout
                null,
                'bakedbot_chatbot'
            );
        }

        add_settings_section(
            'position_section',
            __('Position & Layout', 'bakedbot-chatbot'),
            null,
            'bakedbot_chatbot'
        );
        
        // Add sections for Behavior tab
        add_settings_section(
            'behavior_section',
            __('Behavior Options', 'bakedbot-chatbot'),
            array($this, 'behavior_section_callback'),
            'bakedbot_chatbot'
        );

        // Add sections for Setup tab
        add_settings_section(
            'setup_section',
            __('Getting Started', 'bakedbot-chatbot'),
            array($this, 'setup_section_callback'),
            'bakedbot_chatbot'
        );
        
        // Add sections for API tab
        add_settings_section(
            'api_section',
            __('API Credentials', 'bakedbot-chatbot'),
            array($this, 'api_section_callback'),
            'bakedbot_chatbot'
        );
        

        
        // Add section for Page Setup tab
        add_settings_section(
            'page_setup_section',
            __('Page Management', 'bakedbot-chatbot'),
            array($this, 'page_setup_section_callback'),
            'bakedbot_chatbot'
        );

        // Add product page slug setting field
        add_settings_field(
            'product_page_slug',
            __('Product Page Slug', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'page_setup_section',
            array(
                'label_for' => 'product_page_slug', 
                'field' => 'product_page_slug',
                'description' => __('The URL slug for product detail pages (e.g., "product" creates URLs like /product/sku123)', 'bakedbot-chatbot'),
                'placeholder' => 'product'
            )
        );
        
        // Add section for Product Sync tab
        add_settings_section(
            'product_sync_section',
            __('Product Synchronization', 'bakedbot-chatbot'),
            array($this, 'product_sync_section_callback'),
            'bakedbot_chatbot'
        );

        // Add fields - Appearance tab - Colors section
        add_settings_field(
            'primary_color',
            __('Primary Color', 'bakedbot-chatbot'),
            array($this, 'color_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_primary_color',
            array(
                'label_for' => 'primary_color', 
                'field' => 'primary_color',
                'description' => __('Main color for buttons and accents', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'secondary_color',
            __('Secondary Color', 'bakedbot-chatbot'),
            array($this, 'color_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_secondary_color',
            array(
                'label_for' => 'secondary_color', 
                'field' => 'secondary_color',
                'description' => __('Used for secondary elements and hover states', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'background_color',
            __('Background Color', 'bakedbot-chatbot'),
            array($this, 'color_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_background_color',
            array(
                'label_for' => 'background_color', 
                'field' => 'background_color',
                'description' => __('Main background color of the chatbot', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'header_color',
            __('Header Color', 'bakedbot-chatbot'),
            array($this, 'color_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_header_color',
            array(
                'label_for' => 'header_color', 
                'field' => 'header_color',
                'description' => __('Color for the chatbot header', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'text_color',
            __('Text Color', 'bakedbot-chatbot'),
            array($this, 'color_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_text_color',
            array(
                'label_for' => 'text_color', 
                'field' => 'text_color',
                'description' => __('Main text color', 'bakedbot-chatbot')
            )
        );
        
        // Add display option fields
        add_settings_field(
            'chatbot_title',
            __('Chatbot Title', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_chatbot_title',
            array(
                'label_for' => 'chatbot_title', 
                'field' => 'chatbot_title',
                'description' => __('The title displayed in the chatbot header', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'welcome_message',
            __('Welcome Message', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_welcome_message',
            array(
                'label_for' => 'welcome_message', 
                'field' => 'welcome_message',
                'description' => __('The initial message displayed when the chatbot opens', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'button_text',
            __('Button Text', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_button_text',
            array(
                'label_for' => 'button_text', 
                'field' => 'button_text',
                'description' => __('Text displayed on the chatbot toggle button', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'avatar_url',
            __('Avatar URL', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'appearance_section_avatar_url',
            array(
                'label_for' => 'avatar_url', 
                'field' => 'avatar_url',
                'description' => __('URL to the avatar image (leave empty for default)', 'bakedbot-chatbot')
            )
        );
        
        
        
        // Add fields - Appearance tab - Position section
        add_settings_field(
            'position',
            __('Chatbot Position', 'bakedbot-chatbot'),
            array($this, 'position_field_callback'),
            'bakedbot_chatbot',
            'position_section',
            array(
                'description' => __('Choose where the chatbot appears on your website', 'bakedbot-chatbot')
            )
        );
        
        // Add fields - Behavior tab
        add_settings_field(
            'auto_open',
            __('Auto-open chatbot', 'bakedbot-chatbot'),
            array($this, 'checkbox_field_callback'),
            'bakedbot_chatbot',
            'behavior_section',
            array(
                'label_for' => 'auto_open', 
                'field' => 'auto_open',
                'description' => __('Automatically open the chatbot when the page loads', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'show_on_mobile',
            __('Show on mobile devices', 'bakedbot-chatbot'),
            array($this, 'checkbox_field_callback'),
            'bakedbot_chatbot',
            'behavior_section',
            array(
                'label_for' => 'show_on_mobile', 
                'field' => 'show_on_mobile',
                'description' => __('Display the chatbot on mobile devices', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'debug_mode',
            __('Enable Debug Mode', 'bakedbot-chatbot'),
            array($this, 'checkbox_field_callback'),
            'bakedbot_chatbot',
            'behavior_section',
            array(
                'label_for' => 'debug_mode', 
                'field' => 'debug_mode',
                'description' => __('Output debug information to the browser console', 'bakedbot-chatbot')
            )
        );
        
        add_settings_field(
            'show_on_frontpage',
            __('Show on Front Page', 'bakedbot-chatbot'),
            array($this, 'checkbox_field_callback'),
            'bakedbot_chatbot',
            'behavior_section',
            array(
                'label_for' => 'show_on_frontpage', 
                'field' => 'show_on_frontpage',
                'description' => __('Automatically display the chatbot on your website\'s front page', 'bakedbot-chatbot')
            )
        );
        
        // Add fields - API tab
        add_settings_field(
            'api_key',
            __('API Key', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'api_section',
            array(
                'label_for' => 'api_key', 
                'field' => 'api_key',
                'description' => __('Your BakedBot API key from your dashboard', 'bakedbot-chatbot')
            )
        );
        

        
        add_settings_field(
            'bakedbot_api_url',
            __('BakedBot API URL', 'bakedbot-chatbot'),
            array($this, 'text_field_callback'),
            'bakedbot_chatbot',
            'api_section',
            array(
                'label_for' => 'bakedbot_api_url', 
                'field' => 'bakedbot_api_url',
                'description' => __('The URL for the BakedBot API (usually no need to change)', 'bakedbot-chatbot')
            )
        );




    }
    
    public function setup_section_callback() {
        ?>
        <div class="bakedbot-setup-intro">
            <div class="bakedbot-setup-card">
                <div class="setup-card-icon">🚀</div>
                <h3><?php _e('Welcome to BakedBot!', 'bakedbot-chatbot'); ?></h3>
                <p><?php _e('Get your AI-powered cannabis chatbot up and running in just a few simple steps. Start by entering your API key below, then use our guided setup to create your first pages.', 'bakedbot-chatbot'); ?></p>
                
                <?php if (empty($this->get_option('api_key'))): ?>
                <div class="setup-status setup-pending">
                    <span class="dashicons dashicons-clock"></span>
                    <?php _e('Ready to begin setup', 'bakedbot-chatbot'); ?>
                </div>
                <?php else: ?>
                <div class="setup-status setup-ready">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <?php _e('API configured - Ready to create pages!', 'bakedbot-chatbot'); ?>
                </div>
                <div class="setup-next-step">
                    <a href="<?php echo admin_url('admin.php?page=bakedbot-settings&tab=pages'); ?>" class="button button-primary button-large">
                        <span class="dashicons dashicons-arrow-right-alt"></span>
                        <?php _e('Create Your Pages', 'bakedbot-chatbot'); ?>
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <style>
            .bakedbot-setup-intro {
                margin-bottom: 30px;
            }
            
            .bakedbot-setup-card {
                background: linear-gradient(135deg, #057540 0%, #10b981 100%);
                color: white;
                padding: 30px;
                border-radius: 12px;
                text-align: center;
                box-shadow: 0 4px 12px rgba(5, 117, 64, 0.2);
                position: relative;
                overflow: hidden;
            }
            
            .bakedbot-setup-card::before {
                content: '';
                position: absolute;
                top: -50%;
                right: -50%;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                pointer-events: none;
            }
            
            .setup-card-icon {
                font-size: 48px;
                margin-bottom: 16px;
                position: relative;
                z-index: 1;
            }
            
            .bakedbot-setup-card h3 {
                margin: 0 0 12px 0;
                font-size: 24px;
                color: white;
                position: relative;
                z-index: 1;
            }
            
            .bakedbot-setup-card p {
                margin: 0 0 20px 0;
                font-size: 16px;
                line-height: 1.5;
                opacity: 0.9;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
                position: relative;
                z-index: 1;
            }
            
            .setup-status {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 12px 20px;
                border-radius: 25px;
                font-weight: 500;
                position: relative;
                z-index: 1;
            }
            
            .setup-status.setup-pending {
                background: rgba(255, 193, 7, 0.2);
                border: 1px solid rgba(255, 193, 7, 0.3);
            }
            
            .setup-status.setup-ready {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            
            .setup-status .dashicons {
                font-size: 18px;
                line-height: 1;
            }
            
            .setup-next-step {
                margin-top: 20px;
                position: relative;
                z-index: 1;
            }
            
            .setup-next-step .button {
                background: white;
                color: #057540;
                border: 2px solid white;
                font-weight: 600;
                padding: 12px 24px;
                font-size: 16px;
                border-radius: 8px;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                transition: all 0.2s ease;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            .setup-next-step .button:hover {
                background: #f8f9fa;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                color: #057540;
                text-decoration: none;
            }
            
            .setup-next-step .dashicons {
                font-size: 20px;
                line-height: 1;
            }
        </style>
        <?php
    }
    
    public function appearance_section_callback() {
        ?>
        <div class="bakedbot-appearance-intro">
            <div class="bakedbot-cards-row">
                <div class="bakedbot-info-card">
                    <div class="card-icon">🎨</div>
                    <h4><?php _e('Brand Colors', 'bakedbot-chatbot'); ?></h4>
                    <p><?php _e('Customize the chatbot colors to match your brand identity.', 'bakedbot-chatbot'); ?></p>
                </div>
                <div class="bakedbot-info-card">
                    <div class="card-icon">📱</div>
                    <h4><?php _e('Layout & Position', 'bakedbot-chatbot'); ?></h4>
                    <p><?php _e('Choose where and how the chatbot appears on your site.', 'bakedbot-chatbot'); ?></p>
                </div>
                <div class="bakedbot-info-card">
                    <div class="card-icon">✨</div>
                    <h4><?php _e('Custom Styling', 'bakedbot-chatbot'); ?></h4>
                    <p><?php _e('Fine-tune titles, messages, and visual elements.', 'bakedbot-chatbot'); ?></p>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-appearance-intro {
                margin-bottom: 30px;
            }
            
            .bakedbot-cards-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin-bottom: 20px;
            }
            
            .bakedbot-info-card {
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                transition: all 0.2s ease;
            }
            
            .bakedbot-info-card:hover {
                border-color: #057540;
                box-shadow: 0 4px 8px rgba(5, 117, 64, 0.1);
            }
            
            .bakedbot-info-card .card-icon {
                font-size: 32px;
                margin-bottom: 12px;
                display: block;
            }
            
            .bakedbot-info-card h4 {
                margin: 0 0 8px 0;
                color: #057540;
                font-size: 16px;
                font-weight: 600;
            }
            
            .bakedbot-info-card p {
                margin: 0;
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        </style>
        <?php
    }
    
    public function behavior_section_callback() {
        ?>
        <div class="bakedbot-behavior-intro">
            <div class="bakedbot-feature-highlight">
                <div class="feature-icon">⚙️</div>
                <div class="feature-content">
                    <h4><?php _e('Behavior Settings', 'bakedbot-chatbot'); ?></h4>
                    <p><?php _e('Control how your chatbot behaves and interacts with visitors. These settings affect the user experience and can be adjusted anytime.', 'bakedbot-chatbot'); ?></p>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-behavior-intro {
                margin-bottom: 25px;
            }
            
            .bakedbot-feature-highlight {
                display: flex;
                align-items: center;
                gap: 16px;
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-left: 4px solid #057540;
                padding: 20px;
                border-radius: 6px;
            }
            
            .feature-icon {
                font-size: 24px;
                flex-shrink: 0;
            }
            
            .feature-content h4 {
                margin: 0 0 6px 0;
                color: #057540;
                font-size: 16px;
                font-weight: 600;
            }
            
            .feature-content p {
                margin: 0;
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        </style>
        <?php
    }
    
    public function api_section_callback() {
        ?>
        <div class="bakedbot-api-intro">
            <div class="bakedbot-warning-card">
                <div class="warning-icon">🔑</div>
                <div class="warning-content">
                    <h4><?php _e('API Configuration', 'bakedbot-chatbot'); ?></h4>
                    <p><?php _e('Your API key connects your WordPress site to the BakedBot service. Keep this secure and never share it publicly.', 'bakedbot-chatbot'); ?></p>
                    <?php if (empty($this->get_option('api_key'))): ?>
                    <div class="api-status api-missing">
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('API key required to enable BakedBot functionality', 'bakedbot-chatbot'); ?>
                    </div>
                    <?php else: ?>
                    <div class="api-status api-configured">
                        <span class="dashicons dashicons-yes-alt"></span>
                        <?php _e('API key configured successfully', 'bakedbot-chatbot'); ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-api-intro {
                margin-bottom: 25px;
            }
            
            .bakedbot-warning-card {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 6px;
                padding: 20px;
            }
            
            .warning-icon {
                font-size: 24px;
                flex-shrink: 0;
                margin-top: 2px;
            }
            
            .warning-content h4 {
                margin: 0 0 8px 0;
                color: #856404;
                font-size: 16px;
                font-weight: 600;
            }
            
            .warning-content p {
                margin: 0 0 12px 0;
                color: #856404;
                font-size: 14px;
                line-height: 1.4;
            }
            
            .api-status {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
            }
            
            .api-status.api-missing {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
            
            .api-status.api-configured {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
            
            .api-status .dashicons {
                font-size: 16px;
                line-height: 1;
            }
        </style>
        <?php
    }
    

    
    public function page_setup_section_callback() {
        ?>
        <div class="bakedbot-page-management-container">
            <!-- Page Creation Quick Start -->
            <div class="bakedbot-page-creation">
                <!-- Dispensary Page Creation -->
                <div class="bakedbot-creation-card">
                    <div class="creation-icon">🛒</div>
                    <div class="creation-content">
                        <h3><?php _e('Create Dispensary Page', 'bakedbot-chatbot'); ?></h3>
                        <p><?php _e('Create a beautiful product catalog page with advanced filtering, search, and cart functionality.', 'bakedbot-chatbot'); ?></p>
                        <div class="creation-status" id="menu-page-status">
                            <?php echo $this->get_page_status('dispensary-menu'); ?>
                        </div>
                    </div>
                    <div class="creation-action">
                        <?php 
                        $dispensary_page = get_page_by_path('dispensary-menu');
                        if (!$dispensary_page): 
                        ?>
                        <div class="page-creation-options">
                            <label class="bakedbot-checkbox-option">
                                <input type="checkbox" id="create-dispensary-fullwidth" checked>
                                <span class="checkmark"></span>
                                <?php _e('Make page full-width (recommended)', 'bakedbot-chatbot'); ?>
                            </label>
                        </div>
                        <button type="button" class="button button-primary button-large bakedbot-create-page" 
                                data-page-type="dispensary-menu" 
                                data-title="Dispensary Menu"
                                data-slug="dispensary-menu"
                                data-shortcode='[bakedbot_menu enable_filters="true" enable_search="true" enable_cart="true" layout="grid" products_per_page="12" show_title="false"]'
                                data-description="Premium cannabis product catalog with advanced filtering, search, and seamless checkout experience."
                                data-fullwidth-checkbox="create-dispensary-fullwidth">
                            <?php _e('Create Dispensary Page', 'bakedbot-chatbot'); ?>
                        </button>
                        <?php else: ?>
                        <div class="page-exists-notice">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('Page created successfully!', 'bakedbot-chatbot'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Homepage Creation -->
                <div class="bakedbot-creation-card">
                    <div class="creation-icon">🏠</div>
                    <div class="creation-content">
                        <h3><?php _e('Create Cannabis Homepage', 'bakedbot-chatbot'); ?></h3>
                        <p><?php _e('Create a stunning homepage showcasing featured products, categories, and promotional content.', 'bakedbot-chatbot'); ?></p>
                        <div class="creation-status" id="homepage-page-status">
                            <?php echo $this->get_page_status('dispensary-homepage'); ?>
                        </div>
                    </div>
                    <div class="creation-action">
                        <?php 
                        $homepage_page = get_page_by_path('dispensary-homepage');
                        if (!$homepage_page): 
                        ?>
                        <div class="page-creation-options">
                            <label class="bakedbot-checkbox-option">
                                <input type="checkbox" id="create-homepage-fullwidth" checked>
                                <span class="checkmark"></span>
                                <?php _e('Make page full-width (recommended)', 'bakedbot-chatbot'); ?>
                            </label>
                        </div>
                        <button type="button" class="button button-primary button-large bakedbot-create-page" 
                                data-page-type="dispensary-homepage" 
                                data-title="Dispensary Homepage"
                                data-slug="dispensary-homepage"
                                data-shortcode='[bakedbot_homepage enable_cart="true" theme="light" featured_products_count="8" popular_categories_count="6" show_promotion="true" promotion_title="Premium Cannabis Marketplace" promotion_subtitle="Discover our curated selection of premium cannabis products" promotion_image_url="https://picsum.photos/400" menu_page_url="/dispensary-menu"]'
                                data-description="Premium cannabis homepage with featured products, categories, and promotional sections."
                                data-fullwidth-checkbox="create-homepage-fullwidth">
                            <?php _e('Create Homepage', 'bakedbot-chatbot'); ?>
                        </button>
                        <?php else: ?>
                        <div class="page-exists-notice">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('Page created successfully!', 'bakedbot-chatbot'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Product Details Page Creation -->
                <div class="bakedbot-creation-card">
                    <div class="creation-icon">📄</div>
                    <div class="creation-content">
                        <h3><?php _e('Create Product Details Page', 'bakedbot-chatbot'); ?></h3>
                        <p><?php _e('Create a template page for displaying individual product details. This handles URLs like /product/sku123', 'bakedbot-chatbot'); ?></p>
                        <div class="creation-status" id="product-page-status">
                            <?php echo $this->get_page_status('product'); ?>
                        </div>
                    </div>
                    <div class="creation-action">
                        <?php 
                        $product_page = get_page_by_path('product');
                        if (!$product_page): 
                        ?>
                        <div class="page-creation-options">
                            <label class="bakedbot-checkbox-option">
                                <input type="checkbox" id="create-product-fullwidth" checked>
                                <span class="checkmark"></span>
                                <?php _e('Make page full-width (recommended)', 'bakedbot-chatbot'); ?>
                            </label>
                        </div>
                        <button type="button" class="button button-primary button-large bakedbot-create-page" 
                                data-page-type="product" 
                                data-title="Product Details"
                                data-slug="product"
                                data-shortcode='[bakedbot_product]'
                                data-description="Individual product detail pages showing product information, variants, and purchase options."
                                data-fullwidth-checkbox="create-product-fullwidth">
                            <?php _e('Create Product Page', 'bakedbot-chatbot'); ?>
                        </button>
                        <?php else: ?>
                        <div class="page-exists-notice">
                            <span class="dashicons dashicons-yes-alt"></span>
                            <?php _e('Page created successfully!', 'bakedbot-chatbot'); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Page Manager -->
            <div id="bakedbot-page-manager" class="bakedbot-page-manager">
                <h3><?php _e('Add Chatbot to Existing Pages', 'bakedbot-chatbot'); ?></h3>
                <div class="bakedbot-manager-card">
                    <div class="manager-header">
                        <h4><?php _e('Add BakedBot to Existing Pages', 'bakedbot-chatbot'); ?></h4>
                        <p><?php _e('Select pages to enhance with BakedBot functionality. The chatbot will appear as a floating widget.', 'bakedbot-chatbot'); ?></p>
                    </div>
                    
                    <div class="manager-content">
                        <div class="page-selector-wrapper">
                            <label for="bakedbot-existing-pages"><?php _e('Select Pages:', 'bakedbot-chatbot'); ?></label>
                            <select id="bakedbot-existing-pages" multiple class="bakedbot-page-select">
                                <?php
                                // Add front page option first
                                $frontpage_enabled = $this->get_option('show_on_frontpage', true);
                                $frontpage_text = is_front_page() ? __('Front Page (Current)', 'bakedbot-chatbot') : __('Front Page', 'bakedbot-chatbot');
                                $frontpage_status = $frontpage_enabled ? ' ✓' : '';
                                echo '<option value="frontpage" ' . ($frontpage_enabled ? 'data-has-bakedbot="true"' : '') . ' data-is-frontpage="true">' . esc_html($frontpage_text) . $frontpage_status . '</option>';
                                
                                // Add regular pages
                                $pages = get_pages(array(
                                    'sort_order' => 'ASC',
                                    'sort_column' => 'post_title',
                                    'post_status' => 'publish'
                                ));
                                foreach ($pages as $page) {
                                    $content = $page->post_content;
                                    $has_chatbot = (strpos($content, '[bakedbot_chatbot]') !== false || 
                                                  strpos($content, '[bakedbot_menu]') !== false);
                                    $status_text = $has_chatbot ? ' ✓' : '';
                                    echo '<option value="' . $page->ID . '" ' . ($has_chatbot ? 'data-has-bakedbot="true"' : '') . '>' . esc_html($page->post_title) . $status_text . '</option>';
                                }
                                ?>
                            </select>
                            <p class="description"><?php _e('Hold Ctrl (Cmd on Mac) to select multiple pages. Pages with ✓ already have BakedBot. The Front Page is controlled by the setting above.', 'bakedbot-chatbot'); ?></p>
                        </div>
                        
                        <div class="manager-actions">
                            <button type="button" id="bakedbot-add-to-pages" class="button button-primary">
                                <span class="dashicons dashicons-plus-alt"></span>
                                <?php _e('Add Chatbot', 'bakedbot-chatbot'); ?>
                            </button>
                            <button type="button" id="bakedbot-remove-from-pages" class="button button-secondary">
                                <span class="dashicons dashicons-minus"></span>
                                <?php _e('Remove Chatbot', 'bakedbot-chatbot'); ?>
                            </button>
                        </div>
                        
                        <div id="bakedbot-add-result" class="bakedbot-result-message"></div>
                    </div>
                </div>
            </div>

            <!-- Manual Shortcode Instructions -->
            <div class="bakedbot-manual-shortcodes">
                <h3><?php _e('Manual Shortcode Integration', 'bakedbot-chatbot'); ?></h3>
                <div class="bakedbot-shortcode-card">
                    <div class="shortcode-header">
                        <h4><?php _e('Available Shortcodes', 'bakedbot-chatbot'); ?></h4>
                        <p><?php _e('For advanced users: Copy and paste these shortcodes directly into any page, post, or widget area.', 'bakedbot-chatbot'); ?></p>
                    </div>
                    
                    <div class="shortcode-sections">
                        <!-- Chatbot Shortcode -->
                        <div class="shortcode-section">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">💬</span>
                                <h5><?php _e('Chatbot Widget', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="shortcode-example">
                                    <label><?php _e('Basic Usage:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_chatbot]</code>
                                        <button class="copy-shortcode" data-shortcode="[bakedbot_chatbot]" title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Displays the floating chatbot widget on the page.', 'bakedbot-chatbot'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Dispensary Menu Shortcode -->
                        <div class="shortcode-section">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">🛒</span>
                                <h5><?php _e('Dispensary Menu', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="shortcode-example">
                                    <label><?php _e('Basic Dispensary Menu:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_menu]</code>
                                        <button class="copy-shortcode" data-shortcode="[bakedbot_menu]" title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Displays a complete product catalog with default settings.', 'bakedbot-chatbot'); ?></p>
                                </div>

                                <div class="shortcode-example">
                                    <label><?php _e('Full-Featured Dispensary Menu:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_menu enable_filters="true" enable_search="true" enable_cart="true" layout="grid" products_per_page="24" product_page_url="/product"]</code>
                                        <button class="copy-shortcode" data-shortcode='[bakedbot_menu enable_filters="true" enable_search="true" enable_cart="true" layout="grid" products_per_page="24" product_page_url="/product"]' title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Full-featured product catalog with filtering, search, cart, and links to individual product pages.', 'bakedbot-chatbot'); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Dispensary Homepage Shortcode -->
                        <div class="shortcode-section">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">🏠</span>
                                <h5><?php _e('Dispensary Homepage', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="shortcode-example">
                                    <label><?php _e('Basic Dispensary Homepage:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_homepage]</code>
                                        <button class="copy-shortcode" data-shortcode="[bakedbot_homepage]" title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Displays a complete dispensary homepage with featured products, categories, and promotional sections using default settings.', 'bakedbot-chatbot'); ?></p>
                                </div>

                                <div class="shortcode-example">
                                    <label><?php _e('Full-Featured Dispensary Homepage:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_homepage enable_cart="true" featured_products_count="12" popular_categories_count="8" show_promotion="true" promotion_title="Premium Cannabis Marketplace" promotion_subtitle="Discover our curated selection of premium cannabis products" promotion_image_url="https://picsum.photos/400" menu_page_url="/dispensary-menu" product_page_url="/product"]</code>
                                        <button class="copy-shortcode" data-shortcode='[bakedbot_homepage enable_cart="true" featured_products_count="12" popular_categories_count="8" show_promotion="true" promotion_title="Premium Cannabis Marketplace" promotion_subtitle="Discover our curated selection of premium cannabis products" promotion_image_url="https://picsum.photos/400" menu_page_url="/dispensary-menu" product_page_url="/product"]' title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Full-featured dispensary homepage with custom promotion content, product counts, and links to your dispensary menu and product pages.', 'bakedbot-chatbot'); ?></p>
                                </div>

                                <div class="shortcode-attributes">
                                    <h6><?php _e('Available Attributes:', 'bakedbot-chatbot'); ?></h6>
                                    <div class="attributes-grid">
                                        <div class="attribute-item">
                                            <strong>enable_cart</strong>
                                            <span><?php _e('true/false - Show cart functionality (default: true)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>featured_products_count</strong>
                                            <span><?php _e('Number - Featured products to display (default: 8)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>popular_categories_count</strong>
                                            <span><?php _e('Number - Categories to show (default: 6)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>show_promotion</strong>
                                            <span><?php _e('true/false - Display hero/promotion section (default: true)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>promotion_title</strong>
                                            <span><?php _e('Text - Custom promotion title', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>promotion_subtitle</strong>
                                            <span><?php _e('Text - Custom promotion subtitle', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>promotion_image_url</strong>
                                            <span><?php _e('URL - Custom promotion image', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>theme</strong>
                                            <span><?php _e('light/dark - Visual theme (default: light)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Details Shortcode -->
                        <div class="shortcode-section">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">📄</span>
                                <h5><?php _e('Product Details Page', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="shortcode-example">
                                    <label><?php _e('Basic Product Details:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_product]</code>
                                        <button class="copy-shortcode" data-shortcode="[bakedbot_product]" title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Displays individual product details with variants, pricing, and add to cart functionality. Reads product ID from URL query parameter ?id=product_sku', 'bakedbot-chatbot'); ?></p>
                                </div>

                                <div class="shortcode-example">
                                    <label><?php _e('Customized Product Details:', 'bakedbot-chatbot'); ?></label>
                                    <div class="shortcode-copy-block">
                                        <code>[bakedbot_product enable_cart="true" theme="light" menu_page_url="/dispensary-menu"]</code>
                                        <button class="copy-shortcode" data-shortcode='[bakedbot_product enable_cart="true" theme="light" menu_page_url="/dispensary-menu"]' title="<?php _e('Copy to clipboard', 'bakedbot-chatbot'); ?>">
                                            <span class="dashicons dashicons-admin-page"></span>
                                        </button>
                                    </div>
                                    <p class="shortcode-description"><?php _e('Product details page with custom cart settings and theme configuration.', 'bakedbot-chatbot'); ?></p>
                                </div>

                                <div class="shortcode-attributes">
                                    <h6><?php _e('Available Attributes:', 'bakedbot-chatbot'); ?></h6>
                                    <div class="attributes-grid">
                                        <div class="attribute-item">
                                            <strong>enable_cart</strong>
                                            <span><?php _e('true/false - Show cart functionality (default: true)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>theme</strong>
                                            <span><?php _e('light/dark - Visual theme (default: light)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>menu_page_url</strong>
                                            <span><?php _e('URL - Link back to product menu page (default: /dispensary-menu)', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>primary_color</strong>
                                            <span><?php _e('Color - Primary brand color override', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                        <div class="attribute-item">
                                            <strong>secondary_color</strong>
                                            <span><?php _e('Color - Secondary brand color override', 'bakedbot-chatbot'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Page Integration Tips -->
                        <div class="shortcode-section integration-tips">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">🔗</span>
                                <h5><?php _e('Dispensary Pages Integration', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="tip-list">
                                    <div class="tip-item">
                                        <strong><?php _e('Perfect Pairing:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('The Dispensary Homepage automatically links to your Dispensary Menu page. Create both pages for a complete cannabis e-commerce experience.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                    <div class="tip-item">
                                        <strong><?php _e('Custom Menu URL:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Use menu_page_url="/your-menu-page" in the homepage shortcode to link to a custom menu page.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                    <div class="tip-item">
                                        <strong><?php _e('Navigation Flow:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Homepage → Category/Product clicks → Dispensary Menu with filters applied for seamless browsing.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technical Integration Tips -->
                        <div class="shortcode-section integration-tips">
                            <div class="shortcode-title">
                                <span class="shortcode-icon">💡</span>
                                <h5><?php _e('Technical Integration', 'bakedbot-chatbot'); ?></h5>
                            </div>
                            <div class="shortcode-content">
                                <div class="tip-list">
                                    <div class="tip-item">
                                        <strong><?php _e('Page Editor:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Copy any shortcode above and paste it directly into the WordPress page/post editor.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                    <div class="tip-item">
                                        <strong><?php _e('Widget Areas:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Use the Text/HTML widget to add shortcodes to sidebars and footer areas.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                    <div class="tip-item">
                                        <strong><?php _e('Theme Files:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Use do_shortcode(\'[shortcode]\') in PHP templates for theme integration.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                    <div class="tip-item">
                                        <strong><?php _e('Block Editor:', 'bakedbot-chatbot'); ?></strong>
                                        <span><?php _e('Add a "Shortcode" block and paste the code for Gutenberg compatibility.', 'bakedbot-chatbot'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-page-management-container {
                max-width: 1200px;
                overflow: visible;
                min-height: auto;
            }
            
            /* Page Creation Section */
            .bakedbot-page-creation {
                margin-bottom: 40px;
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
                gap: 20px;
            }
            
            .bakedbot-creation-card {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 24px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                display: flex;
                align-items: center;
                gap: 24px;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }
            
            .bakedbot-creation-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.12);
            }
            
            .creation-icon {
                font-size: 48px;
                flex-shrink: 0;
            }
            
            .creation-content {
                flex-grow: 1;
            }
            
            .creation-content h3 {
                margin: 0 0 8px 0;
                color: #057540;
                font-size: 20px;
                font-weight: 600;
            }
            
            .creation-content p {
                margin: 0 0 12px 0;
                color: #666;
                font-size: 14px;
                line-height: 1.5;
            }
            
            .creation-status {
                font-size: 12px;
                padding: 4px 8px;
                border-radius: 4px;
                display: inline-block;
            }
            
            .creation-status .exists {
                background: #d4edda;
                color: #155724;
            }
            
            .creation-status .not-exists {
                background: #f8d7da;
                color: #721c24;
            }
            
            .creation-action {
                flex-shrink: 0;
            }
            
            .creation-action .button {
                font-weight: 500;
                padding: 12px 24px;
            }
            
            .page-exists-notice {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #155724;
                background: #d4edda;
                border: 1px solid #c3e6cb;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: 500;
            }
            
            .page-exists-notice .dashicons {
                font-size: 20px;
                line-height: 1;
            }
            
            /* Page Creation Options */
            .page-creation-options {
                margin: 16px 0;
                padding: 12px;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
            }
            
            .bakedbot-checkbox-option {
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                font-size: 14px;
                color: #495057;
            }
            
            .bakedbot-checkbox-option input[type="checkbox"] {
                margin: 0;
                padding: 0;
                width: 16px;
                height: 16px;
                accent-color: #057540;
            }
            
            .bakedbot-checkbox-option:hover {
                color: #057540;
            }

            
            /* Page Manager */
            .bakedbot-page-manager {
                margin-bottom: 40px;
            }
            
            .bakedbot-page-manager h3 {
                margin: 0 0 20px 0;
                color: #057540;
                font-size: 20px;
            }
            
            .bakedbot-manager-card {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            }
            
            .manager-header {
                background: #f8f9fa;
                padding: 20px;
                border-bottom: 1px solid #dee2e6;
            }
            
            .manager-header h4 {
                margin: 0 0 8px 0;
                color: #212529;
                font-size: 16px;
            }
            
            .manager-header p {
                margin: 0;
                color: #6c757d;
                font-size: 14px;
            }
            
            .manager-content {
                padding: 20px;
            }
            
            .page-selector-wrapper {
                margin-bottom: 20px;
            }
            
            .page-selector-wrapper label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #212529;
            }
            
            .bakedbot-page-select {
                width: 100%;
                height: 150px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                background: white;
            }
            
            .bakedbot-page-select option[data-has-bakedbot="true"] {
                background: #d4edda;
                color: #155724;
            }
            
            .manager-actions {
                display: flex;
                gap: 12px;
                margin-bottom: 20px;
            }
            
            .manager-actions .button {
                display: flex;
                align-items: center;
                gap: 6px;
                font-weight: 500;
            }
            
            .bakedbot-result-message {
                padding: 12px;
                border-radius: 6px;
                display: none;
                font-size: 14px;
            }
            
            .bakedbot-result-message.success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                display: block;
            }
            
            .bakedbot-result-message.error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                display: block;
            }
            
            /* Manual Shortcode Instructions Styles */
            .bakedbot-manual-shortcodes {
                margin-top: 40px;
            }
            
            .bakedbot-manual-shortcodes h3 {
                margin: 0 0 20px 0;
                color: #057540;
                font-size: 20px;
            }
            
            .bakedbot-shortcode-card {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            }
            
            .shortcode-header {
                background: #f8f9fa;
                padding: 20px;
                border-bottom: 1px solid #dee2e6;
            }
            
            .shortcode-header h4 {
                margin: 0 0 8px 0;
                color: #212529;
                font-size: 16px;
            }
            
            .shortcode-header p {
                margin: 0;
                color: #6c757d;
                font-size: 14px;
            }
            
            .shortcode-sections {
                padding: 20px;
            }
            
            .shortcode-section {
                margin-bottom: 30px;
                padding-bottom: 25px;
                border-bottom: 1px solid #e9ecef;
            }
            
            .shortcode-section:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
            
            .shortcode-title {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 15px;
            }
            
            .shortcode-icon {
                font-size: 24px;
                line-height: 1;
            }
            
            .shortcode-title h5 {
                margin: 0;
                color: #057540;
                font-size: 18px;
                font-weight: 600;
            }
            
            .shortcode-example {
                margin-bottom: 20px;
            }
            
            .shortcode-example:last-child {
                margin-bottom: 0;
            }
            
            .shortcode-example label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #212529;
                font-size: 14px;
            }
            
            .shortcode-copy-block {
                position: relative;
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 12px 50px 12px 12px;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
            }
            
            .shortcode-copy-block code {
                background: none;
                color: #e83e8c;
                font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
                font-size: 13px;
                word-break: break-all;
                flex-grow: 1;
                margin: 0;
                padding: 0;
            }
            
            .copy-shortcode {
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
                background: #057540;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.2s ease;
            }
            
            .copy-shortcode:hover {
                background: #046235;
            }
            
            .copy-shortcode .dashicons {
                font-size: 14px;
                line-height: 1;
            }
            
            .shortcode-description {
                margin: 0;
                color: #6c757d;
                font-size: 13px;
                line-height: 1.4;
            }
            
            .shortcode-attributes {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 16px;
                margin-top: 15px;
            }
            
            .shortcode-attributes h6 {
                margin: 0 0 12px 0;
                color: #212529;
                font-size: 14px;
                font-weight: 600;
            }
            
            .attributes-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 12px;
            }
            
            .attribute-item {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }
            
            .attribute-item strong {
                color: #057540;
                font-size: 13px;
                font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            }
            
            .attribute-item span {
                color: #6c757d;
                font-size: 12px;
                line-height: 1.3;
            }
            
            .integration-tips .tip-list {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            
            .tip-item {
                display: flex;
                flex-direction: column;
                gap: 4px;
                padding: 12px;
                background: #f8f9fa;
                border-left: 3px solid #057540;
                border-radius: 0 4px 4px 0;
            }
            
            .tip-item strong {
                color: #057540;
                font-size: 13px;
            }
            
            .tip-item span {
                color: #6c757d;
                font-size: 12px;
                line-height: 1.4;
            }

            
                         /* Responsive Design */
             @media (max-width: 1200px) {
                 .bakedbot-actions-grid {
                     grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                 }
             }
             
             @media (max-width: 768px) {
                 .bakedbot-setup-progress {
                     flex-direction: column;
                     gap: 20px;
                 }
                 
                 .bakedbot-progress-step::after {
                     display: none;
                 }
                 
                 .bakedbot-actions-grid {
                     grid-template-columns: 1fr;
                     gap: 16px;
                 }
                 
                 .bakedbot-action-card {
                     min-height: 180px;
                     padding: 20px;
                 }
                 
                 /* Page creation responsive */
                 .bakedbot-page-creation {
                     grid-template-columns: 1fr;
                     gap: 16px;
                 }
                 
                 .bakedbot-creation-card {
                     flex-direction: column;
                     text-align: center;
                     padding: 20px;
                     gap: 16px;
                 }
                 
                 .creation-icon {
                     font-size: 36px;
                     margin-bottom: 8px;
                 }
                 
                 .creation-content {
                     flex-grow: 1;
                 }
                 
                 .creation-action {
                     width: 100%;
                 }
                 
                 .creation-action .button {
                     width: 100%;
                     text-align: center;
                 }
                 
                 .manager-actions {
                     flex-direction: column;
                 }
                 
                 .bakedbot-docs-grid {
                     grid-template-columns: 1fr;
                 }
                 
                 .bakedbot-help-links {
                     flex-direction: column;
                 }
                 
                 /* Shortcode instructions responsive */
                 .attributes-grid {
                     grid-template-columns: 1fr;
                     gap: 8px;
                 }
                 
                 .shortcode-header,
                 .shortcode-sections {
                     padding: 16px;
                 }
                 
                 .shortcode-copy-block {
                     padding: 10px 40px 10px 10px;
                 }
                 
                 .shortcode-copy-block code {
                     font-size: 12px;
                 }
                 
                 .copy-shortcode {
                     right: 6px;
                     padding: 5px;
                 }
             }
             
             @media (max-width: 480px) {
                 .bakedbot-actions-grid {
                     gap: 12px;
                 }
                 
                 .bakedbot-action-card {
                     min-height: 160px;
                     padding: 16px;
                 }
                 
                 .action-icon {
                     font-size: 24px;
                     margin-bottom: 12px;
                 }
                 
                 .action-content h4 {
                     font-size: 14px;
                 }
                 
                 .action-content p {
                     font-size: 13px;
                 }
             }
        </style>
        
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Create page functionality
                $('.bakedbot-create-page').on('click', function() {
                    var button = $(this);
                    var pageType = button.data('page-type');
                    var title = button.data('title');
                    var slug = button.data('slug');
                    var shortcode = button.data('shortcode');
                    var description = button.data('description');
                    var fullWidthCheckbox = button.data('fullwidth-checkbox');
                    var fullWidth = fullWidthCheckbox ? $('#' + fullWidthCheckbox).is(':checked') : true;
                    
                    button.prop('disabled', true);
                    button.html('<span class="dashicons dashicons-update-alt" style="animation: spin 1s linear infinite;"></span> <?php echo esc_js(__('Creating...', 'bakedbot-chatbot')); ?>');
                    
                    var data = {
                        action: 'bakedbot_create_page',
                        security: '<?php echo wp_create_nonce("bakedbot_create_page_nonce"); ?>',
                        page_type: pageType,
                        title: title,
                        slug: slug,
                        content: shortcode,
                        description: description,
                        full_width: fullWidth
                    };
                    
                    $.post(ajaxurl, data, function(response) {
                        if (response.success) {
                            button.html('<span class="dashicons dashicons-yes-alt"></span> <?php echo esc_js(__('Created!', 'bakedbot-chatbot')); ?>');
                            button.removeClass('button-primary').addClass('button-secondary');
                            
                            // Update page status - handle different page types
                            var statusElementId;
                            if (pageType === 'menu') {
                                statusElementId = 'menu-page-status';
                            } else if (pageType === 'dispensary-homepage') {
                                statusElementId = 'homepage-page-status';
                            } else {
                                statusElementId = pageType + '-page-status';
                            }
                            
                            $('#' + statusElementId).html('<span class="exists">✓ <?php echo esc_js(__('Page exists', 'bakedbot-chatbot')); ?>: <a href="' + response.data.edit_url + '" target="_blank"><?php echo esc_js(__('Edit', 'bakedbot-chatbot')); ?></a> | <a href="' + response.data.view_url + '" target="_blank"><?php echo esc_js(__('View', 'bakedbot-chatbot')); ?></a></span>');
                            
                            // Also hide the creation form and show success message
                            button.closest('.creation-action').html('<div class="page-exists-notice"><span class="dashicons dashicons-yes-alt"></span><?php echo esc_js(__('Page created successfully!', 'bakedbot-chatbot')); ?></div>');
                        } else {
                            button.prop('disabled', false);
                            button.html('<?php echo esc_js(__('Create Page', 'bakedbot-chatbot')); ?>');
                            showNotification('error', '<?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data);
                        }
                    }).fail(function() {
                        button.prop('disabled', false);
                        button.html('<?php echo esc_js(__('Create Page', 'bakedbot-chatbot')); ?>');
                        showNotification('error', '<?php echo esc_js(__('Failed to create page. Please try again.', 'bakedbot-chatbot')); ?>');
                    });
                });
                
                // Add to existing pages functionality
                $('#bakedbot-add-to-pages').on('click', function() {
                    var button = $(this);
                    var selectedPages = $('#bakedbot-existing-pages').val();
                    var resultDiv = $('#bakedbot-add-result');
                    
                    if (!selectedPages || selectedPages.length === 0) {
                        resultDiv.removeClass('success').addClass('error').text('<?php echo esc_js(__('Please select at least one page.', 'bakedbot-chatbot')); ?>').show();
                        return;
                    }
                    
                    button.prop('disabled', true);
                    button.html('<span class="dashicons dashicons-plus-alt"></span> <?php echo esc_js(__('Adding...', 'bakedbot-chatbot')); ?>');
                    resultDiv.removeClass('success error').hide();
                    
                    var data = {
                        action: 'bakedbot_add_to_pages',
                        security: '<?php echo wp_create_nonce("bakedbot_add_to_pages_nonce"); ?>',
                        page_ids: selectedPages,
                        shortcode: '[bakedbot_chatbot]'
                    };
                    
                    $.post(ajaxurl, data, function(response) {
                        button.prop('disabled', false);
                        button.html('<span class="dashicons dashicons-plus-alt"></span> <?php echo esc_js(__('Add Chatbot', 'bakedbot-chatbot')); ?>');
                        
                        if (response.success) {
                            resultDiv.removeClass('error').addClass('success').text(response.data.message).show();
                            // Update the select options to show checkmarks
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            resultDiv.removeClass('success').addClass('error').text('<?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data).show();
                        }
                    });
                });
                
                // Remove from pages functionality
                $('#bakedbot-remove-from-pages').on('click', function() {
                    var button = $(this);
                    var selectedPages = $('#bakedbot-existing-pages').val();
                    var resultDiv = $('#bakedbot-add-result');
                    
                    if (!selectedPages || selectedPages.length === 0) {
                        resultDiv.removeClass('success').addClass('error').text('<?php echo esc_js(__('Please select at least one page.', 'bakedbot-chatbot')); ?>').show();
                        return;
                    }
                    
                    button.prop('disabled', true);
                    button.html('<span class="dashicons dashicons-minus"></span> <?php echo esc_js(__('Removing...', 'bakedbot-chatbot')); ?>');
                    resultDiv.removeClass('success error').hide();
                    
                    var data = {
                        action: 'bakedbot_remove_from_pages',
                        security: '<?php echo wp_create_nonce("bakedbot_remove_from_pages_nonce"); ?>',
                        page_ids: selectedPages,
                    };
                    
                    $.post(ajaxurl, data, function(response) {
                        button.prop('disabled', false);
                        button.html('<span class="dashicons dashicons-minus"></span> <?php echo esc_js(__('Remove Chatbot', 'bakedbot-chatbot')); ?>');
                        
                        if (response.success) {
                            resultDiv.removeClass('error').addClass('success').text(response.data.message).show();
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            resultDiv.removeClass('success').addClass('error').text('<?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data).show();
                        }
                    });
                });
                
                // Copy shortcode functionality
                $('.copy-shortcode').on('click', function() {
                    var button = $(this);
                    var shortcode = button.data('shortcode');
                    var originalIcon = button.html();
                    
                    // Try to copy to clipboard
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(shortcode).then(function() {
                            button.html('<span class="dashicons dashicons-yes"></span>');
                            button.css('background', '#46b450');
                            showNotification('success', '<?php echo esc_js(__('Shortcode copied to clipboard!', 'bakedbot-chatbot')); ?>');
                            
                            setTimeout(function() {
                                button.html(originalIcon);
                                button.css('background', '#057540');
                            }, 2000);
                        }).catch(function() {
                            fallbackCopyTextToClipboard(shortcode, button, originalIcon);
                        });
                    } else {
                        fallbackCopyTextToClipboard(shortcode, button, originalIcon);
                    }
                });
                
                // Fallback copy function for older browsers
                function fallbackCopyTextToClipboard(text, button, originalIcon) {
                    var textArea = document.createElement("textarea");
                    textArea.value = text;
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";
                    textArea.style.opacity = "0";
                    
                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();
                    
                    try {
                        var successful = document.execCommand('copy');
                        if (successful) {
                            button.html('<span class="dashicons dashicons-yes"></span>');
                            button.css('background', '#46b450');
                            showNotification('success', '<?php echo esc_js(__('Shortcode copied to clipboard!', 'bakedbot-chatbot')); ?>');
                            
                            setTimeout(function() {
                                button.html(originalIcon);
                                button.css('background', '#057540');
                            }, 2000);
                        } else {
                            showNotification('error', '<?php echo esc_js(__('Copy failed. Please select and copy manually.', 'bakedbot-chatbot')); ?>');
                        }
                    } catch (err) {
                        showNotification('error', '<?php echo esc_js(__('Copy not supported. Please select and copy manually.', 'bakedbot-chatbot')); ?>');
                    }
                    
                    document.body.removeChild(textArea);
                }

                
                // Notification helper
                function showNotification(type, message) {
                    var notification = $('<div class="bakedbot-notification bakedbot-notification-' + type + '">' + message + '</div>');
                    $('body').append(notification);
                    
                    setTimeout(function() {
                        notification.addClass('show');
                    }, 100);
                    
                    setTimeout(function() {
                        notification.removeClass('show');
                        setTimeout(function() {
                            notification.remove();
                        }, 300);
                    }, 3000);
                }
            });
        </script>
        
        <style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .bakedbot-notification {
                position: fixed;
                top: 32px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 100000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .bakedbot-notification.show {
                transform: translateX(0);
            }
            
            .bakedbot-notification-success {
                background: #46b450;
            }
            
            .bakedbot-notification-error {
                background: #dc3232;
            }
        </style>
        <?php
    }
    
    public function product_sync_section_callback() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            ?>
            <div class="bakedbot-woocommerce-missing">
                <div class="missing-plugin-card">
                    <div class="plugin-content">
                        <h4><?php _e('WooCommerce Required', 'bakedbot-chatbot'); ?></h4>
                        <p><?php _e('Product sync features require WooCommerce to be installed and active. Install WooCommerce to unlock powerful ecommerce integration with BakedBot.', 'bakedbot-chatbot'); ?></p>
                        <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>" class="button button-primary">
                            <?php _e('Install WooCommerce', 'bakedbot-chatbot'); ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <style>
                .bakedbot-woocommerce-missing {
                    margin-bottom: 30px;
                }
                
                .missing-plugin-card {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                    background: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 8px;
                    padding: 24px;
                }
                
                .plugin-icon {
                    font-size: 40px;
                    flex-shrink: 0;
                }
                
                .plugin-content h4 {
                    margin: 0 0 8px 0;
                    color: #721c24;
                    font-size: 18px;
                    font-weight: 600;
                }
                
                .plugin-content p {
                    margin: 0 0 16px 0;
                    color: #721c24;
                    font-size: 14px;
                    line-height: 1.5;
                }
                
                .plugin-content .button {
                    background: #721c24;
                    border-color: #721c24;
                    color: white;
                }
                
                .plugin-content .button:hover {
                    background: #5a161c;
                    border-color: #5a161c;
                }
            </style>
            <?php
            return;
        }
        
        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            ?>
            <div class="bakedbot-api-warning">
                <div class="api-warning-card">
                    <div class="warning-icon">⚠️</div>
                    <div class="warning-content">
                        <h4><?php _e('API Key Required', 'bakedbot-chatbot'); ?></h4>
                        <p><?php _e('Please configure your API key in the Setup tab before using product sync features.', 'bakedbot-chatbot'); ?></p>
                        <a href="<?php echo admin_url('admin.php?page=bakedbot-settings&tab=setup'); ?>" class="button button-primary">
                            <?php _e('Configure API Key', 'bakedbot-chatbot'); ?>
                        </a>
                    </div>
                </div>
            </div>
            
            <style>
                .bakedbot-api-warning {
                    margin-bottom: 30px;
                }
                
                .api-warning-card {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 20px;
                }
                
                .warning-content h4 {
                    margin: 0 0 8px 0;
                    color: #856404;
                    font-size: 16px;
                    font-weight: 600;
                }
                
                .warning-content p {
                    margin: 0 0 12px 0;
                    color: #856404;
                    font-size: 14px;
                    line-height: 1.4;
                }
            </style>
            <?php
            return;
        }
        
        ?>
        <div class="bakedbot-sync-intro">
            <div class="bakedbot-sync-note">
                <div class="note-icon">ℹ️</div>
                <div class="note-content">
                    <strong><?php _e('About Product Sync', 'bakedbot-chatbot'); ?></strong>
                    <p><?php _e('Product synchronization runs in the background and may take several minutes depending on your catalog size. You can safely leave this page during the process.', 'bakedbot-chatbot'); ?></p>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-sync-intro {
                margin-bottom: 30px;
            }
            
            .bakedbot-sync-note {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                background: #e1f5fe;
                border: 1px solid #81d4fa;
                border-radius: 6px;
                padding: 16px;
                margin-top: 20px;
            }
            
            .note-icon {
                font-size: 20px;
                flex-shrink: 0;
                margin-top: 2px;
            }
            
            .note-content strong {
                display: block;
                color: #01579b;
                font-size: 14px;
                margin-bottom: 4px;
            }
            
            .note-content p {
                margin: 0;
                color: #01579b;
                font-size: 13px;
                line-height: 1.4;
            }
        </style>
        <?php
        
        ?>
        <div class="bakedbot-product-sync-container">
            <div class="bakedbot-card">
                <h3><?php _e('Import Products to WooCommerce', 'bakedbot-chatbot'); ?></h3>
                <p><?php _e('Synchronize products from the BakedBot API, powered by CannMenus, to your WooCommerce store.', 'bakedbot-chatbot'); ?></p>
                
                <div class="bakedbot-card-setting">
                    <label class="bakedbot-inline-checkbox">
                        <input type="checkbox" name="<?php echo $this->option_name; ?>[import_active_only]" value="1" <?php checked($this->get_option('import_active_only', true)); ?> class="bakedbot-auto-save" />
                        <span><?php _e('Import only active/published products', 'bakedbot-chatbot'); ?></span>
                    </label>
                    <small class="description"><?php _e('Unchecked will import all products regardless of status', 'bakedbot-chatbot'); ?></small>
                </div>
                
                <p><button id="bakedbot-sync-button" class="button button-primary"><?php _e('Sync Products', 'bakedbot-chatbot'); ?></button></p>
                <div id="bakedbot-sync-progress"></div>
            </div>
            
            <div class="bakedbot-card">
                <h3><?php _e('Export Products to BakedBot', 'bakedbot-chatbot'); ?></h3>
                <p><?php _e('Export your WooCommerce products to the BakedBot platform.', 'bakedbot-chatbot'); ?></p>
                
                <div class="bakedbot-card-setting">
                    <label class="bakedbot-setting-label"><?php _e('Export Product Statuses', 'bakedbot-chatbot'); ?></label>
                    <div class="bakedbot-compact-radios">
                        <label class="bakedbot-compact-radio">
                            <input type="radio" name="<?php echo $this->option_name; ?>[export_product_statuses]" value="publish_only" <?php checked($this->get_option('export_product_statuses', 'publish_only'), 'publish_only'); ?> class="bakedbot-auto-save" />
                            <span><?php _e('Published Only', 'bakedbot-chatbot'); ?></span>
                        </label>
                        <label class="bakedbot-compact-radio">
                            <input type="radio" name="<?php echo $this->option_name; ?>[export_product_statuses]" value="publish_and_draft" <?php checked($this->get_option('export_product_statuses', 'publish_only'), 'publish_and_draft'); ?> class="bakedbot-auto-save" />
                            <span><?php _e('Published + Draft', 'bakedbot-chatbot'); ?></span>
                        </label>
                        <label class="bakedbot-compact-radio">
                            <input type="radio" name="<?php echo $this->option_name; ?>[export_product_statuses]" value="all" <?php checked($this->get_option('export_product_statuses', 'publish_only'), 'all'); ?> class="bakedbot-auto-save" />
                            <span><?php _e('All Statuses', 'bakedbot-chatbot'); ?></span>
                        </label>
                    </div>
                    <small class="description"><?php _e('Choose which product statuses to include when exporting', 'bakedbot-chatbot'); ?></small>
                </div>
                
                <p><button id="bakedbot-export-button" class="button button-primary"><?php _e('Export Products', 'bakedbot-chatbot'); ?></button></p>
                <div id="bakedbot-export-progress"></div>
            </div>
        </div>
        
        <style>
            .bakedbot-product-sync-container {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin-top: 20px;
            }
            .bakedbot-card {
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 20px;
                width: calc(50% - 10px);
                min-width: 300px;
                box-sizing: border-box;
            }
            
            .bakedbot-card-setting {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                margin: 15px 0;
            }
            
            .bakedbot-setting-label {
                display: block;
                font-weight: 600;
                color: #333;
                margin-bottom: 10px;
                font-size: 14px;
            }
            
            .bakedbot-inline-checkbox {
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                margin-bottom: 8px;
            }
            
            .bakedbot-inline-checkbox input[type="checkbox"] {
                margin: 0;
            }
            
            .bakedbot-inline-checkbox span {
                font-weight: 500;
                color: #333;
            }
            
            .bakedbot-compact-radios {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .bakedbot-compact-radio {
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                padding: 4px 0;
            }
            
            .bakedbot-compact-radio input[type="radio"] {
                margin: 0;
            }
            
            .bakedbot-compact-radio span {
                font-size: 13px;
                color: #555;
            }
            
            .bakedbot-compact-radio input[type="radio"]:checked + span {
                font-weight: 600;
                color: #057540;
            }
            
            .bakedbot-card-setting .description {
                display: block;
                margin-top: 8px;
                font-style: italic;
                color: #666;
                font-size: 12px;
            }
            
            .coming-soon {
                opacity: 0.7;
            }
            #bakedbot-sync-progress, #bakedbot-export-progress {
                margin-top: 10px;
                font-style: italic;
            }
        </style>
        
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                // Import functionality
                $('#bakedbot-sync-button').on('click', function() {
                    var button = $(this);
                    var progressDiv = $('#bakedbot-sync-progress');
                    button.prop('disabled', true);
                    progressDiv.html('<p><?php echo esc_js(__('Starting sync...', 'bakedbot-chatbot')); ?></p>');

                    var data = {
                        action: 'bakedbot_sync_products',
                        security: '<?php echo wp_create_nonce("bakedbot_sync_nonce"); ?>',
                    };

                    $.post(ajaxurl, data, function(response) {
                        if (response.success) {
                            progressDiv.append('<p><?php echo esc_js(__('Import started.', 'bakedbot-chatbot')); ?></p>');
                            check_progress();
                        } else {
                            progressDiv.append('<p><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data + '</p>');
                            button.prop('disabled', false);
                        }
                    });

                    function check_progress() {
                        var progressData = {
                            action: 'bakedbot_get_import_status',
                            security: '<?php echo wp_create_nonce("bakedbot_status_nonce"); ?>',
                        };

                        $.post(ajaxurl, progressData, function(response) {
                            if (response.success) {
                                var status = response.data.status;
                                if (status.error) {
                                    progressDiv.append('<p style="color:red;"><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + status.error + '</p>');
                                    button.prop('disabled', false);
                                } else if (status.completed) {
                                    progressDiv.append('<p><?php echo esc_js(__('Import completed.', 'bakedbot-chatbot')); ?></p>');
                                    button.prop('disabled', false);
                                } else {
                                    var totalPages = status.total_pages > 0 ? status.total_pages : '?';
                                    progressDiv.html('<p><?php echo esc_js(__('Importing page', 'bakedbot-chatbot')); ?> ' + status.current_page + ' <?php echo esc_js(__('of', 'bakedbot-chatbot')); ?> ' + totalPages + '...</p>');
                                    setTimeout(check_progress, 5000); // Check every 5 seconds
                                }
                            } else {
                                progressDiv.append('<p><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data + '</p>');
                                button.prop('disabled', false);
                            }
                        });
                    }
                });

                // Export functionality
                $('#bakedbot-export-button').on('click', function() {
                    var button = $(this);
                    var progressDiv = $('#bakedbot-export-progress');
                    button.prop('disabled', true);
                    progressDiv.html('<p><?php echo esc_js(__('Starting export...', 'bakedbot-chatbot')); ?></p>');

                    var data = {
                        action: 'bakedbot_export_products',
                        security: '<?php echo wp_create_nonce("bakedbot_export_nonce"); ?>',
                    };

                    $.post(ajaxurl, data, function(response) {
                        if (response.success) {
                            progressDiv.append('<p><?php echo esc_js(__('Export started.', 'bakedbot-chatbot')); ?></p>');
                            check_export_progress();
                        } else {
                            progressDiv.append('<p><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data + '</p>');
                            button.prop('disabled', false);
                        }
                    });

                    function check_export_progress() {
                        var progressData = {
                            action: 'bakedbot_get_export_status',
                            security: '<?php echo wp_create_nonce("bakedbot_export_status_nonce"); ?>',
                        };

                        $.post(ajaxurl, progressData, function(response) {
                            if (response.success) {
                                var status = response.data.status;
                                if (status.error) {
                                    progressDiv.append('<p style="color:red;"><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + status.error + '</p>');
                                    button.prop('disabled', false);
                                } else if (status.completed) {
                                    progressDiv.append('<p><?php echo esc_js(__('Export completed.', 'bakedbot-chatbot')); ?></p>');
                                    button.prop('disabled', false);
                                } else {
                                    var totalProducts = status.total_products > 0 ? status.total_products : '?';
                                    progressDiv.html('<p><?php echo esc_js(__('Exporting batch', 'bakedbot-chatbot')); ?> ' + status.current_batch + ' <?php echo esc_js(__('of', 'bakedbot-chatbot')); ?> ' + status.total_batches + ' (<?php echo esc_js(__('processed', 'bakedbot-chatbot')); ?> ' + status.processed_products + ' <?php echo esc_js(__('of', 'bakedbot-chatbot')); ?> ' + totalProducts + ')...</p>');
                                    setTimeout(check_export_progress, 5000); // Check every 5 seconds
                                }
                            } else {
                                progressDiv.append('<p><?php echo esc_js(__('Error:', 'bakedbot-chatbot')); ?> ' + response.data + '</p>');
                                button.prop('disabled', false);
                            }
                        });
                    }
                });
                
                // Auto-save settings when export/import options change
                $('.bakedbot-auto-save').on('change', function() {
                    var $changedField = $(this);
                    var $form = $changedField.closest('form');
                    var $indicator = $('<span class="bakedbot-saving-indicator">Saving...</span>');
                    
                    // Show saving indicator
                    $changedField.closest('.bakedbot-card-setting').append($indicator);
                    
                    // Serialize form data
                    var formData = $form.serialize();
                    
                    // Submit via AJAX
                    $.ajax({
                        type: 'POST',
                        url: '<?php echo admin_url("options.php"); ?>',
                        data: formData,
                        success: function(response) {
                            $indicator.text('Saved!').addClass('bakedbot-saved');
                            setTimeout(function() {
                                $indicator.fadeOut(function() {
                                    $(this).remove();
                                });
                            }, 2000);
                        },
                        error: function() {
                            $indicator.text('Error saving').addClass('bakedbot-error');
                            setTimeout(function() {
                                $indicator.fadeOut(function() {
                                    $(this).remove();
                                });
                            }, 3000);
                        }
                    });
                });
            });
        </script>
        
        <style>
        .bakedbot-saving-indicator {
            display: inline-block;
            margin-left: 10px;
            padding: 2px 8px;
            background: #f0f0f1;
            border-radius: 3px;
            font-size: 11px;
            color: #646970;
            animation: pulse 1s infinite;
        }
        
        .bakedbot-saving-indicator.bakedbot-saved {
            background: #00a32a;
            color: white;
            animation: none;
        }
        
        .bakedbot-saving-indicator.bakedbot-error {
            background: #d63638;
            color: white;
            animation: none;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        </style>
        
        <?php
    }
    
    public function color_field_callback($args) {
        $field = $args['field'];
        $default = isset($args['default']) ? $args['default'] : '';
        $value = isset($this->options[$field]) ? $this->options[$field] : $default;
        $description = isset($args['description']) ? $args['description'] : '';
        
        echo '<div class="bakedbot-field-wrapper bakedbot-color-field">';
        // Place color inputs on first row
        echo '<div class="bakedbot-color-picker-wrapper">';
        echo '<input type="color" id="' . esc_attr($field) . '" name="' . esc_attr($this->option_name) . '[' . esc_attr($field) . ']" value="' . esc_attr($value) . '">';
        echo '<input type="text" class="bakedbot-color-text" value="' . esc_attr($value) . '" data-color-field="' . esc_attr($field) . '">';
        echo '</div>';
        
        // Place description on its own row below the inputs
        if (!empty($description)) {
            echo '<div class="bakedbot-color-description-row">' . esc_html($description) . '</div>';
        }
        echo '</div>';
        
        // Add JavaScript to sync the color picker and text input
        static $js_added = false;
        if (!$js_added) {
            echo '<script>
                jQuery(document).ready(function($) {
                    $(".bakedbot-color-text").on("input", function() {
                        var colorField = $(this).data("color-field");
                        $("#" + colorField).val($(this).val());
                    });
                    
                    $("input[type=color]").on("input", function() {
                        $(".bakedbot-color-text[data-color-field=\'" + $(this).attr("id") + "\']").val($(this).val());
                    });
                });
            </script>';
            $js_added = true;
        }
    }
    
    public function text_field_callback($args) {
        $field = $args['field'];
        $placeholder = isset($args['placeholder']) ? $args['placeholder'] : '';
        $default = isset($args['default']) ? $args['default'] : '';
        $value = isset($this->options[$field]) ? $this->options[$field] : $default;
        $description = isset($args['description']) ? $args['description'] : '';
        
        echo '<div class="bakedbot-field-wrapper">';
        echo '<input type="text" id="' . esc_attr($field) . '" name="' . esc_attr($this->option_name) . '[' . esc_attr($field) . ']" value="' . esc_attr($value) . '" placeholder="' . esc_attr($placeholder) . '" class="regular-text">';
        
        if (!empty($description)) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
        echo '</div>';
    }
    
    public function checkbox_field_callback($args) {
        $field = $args['field'];
        $checked = isset($this->options[$field]) ? $this->options[$field] : false;
        $description = isset($args['description']) ? $args['description'] : '';
        
        echo '<div class="bakedbot-field-wrapper bakedbot-checkbox-field">';
        echo '<label class="bakedbot-toggle">';
        echo '<input type="checkbox" id="' . esc_attr($field) . '" name="' . esc_attr($this->option_name) . '[' . esc_attr($field) . ']" ' . checked($checked, true, false) . ' value="1">';
        echo '<span class="bakedbot-toggle-slider"></span>';
        echo '</label>';
        
        if (!empty($description)) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
        echo '</div>';
    }
    
    public function position_field_callback($args) {
        $value = isset($this->options['position']) ? $this->options['position'] : 'right';
        $description = isset($args['description']) ? $args['description'] : '';
        
        echo '<div class="bakedbot-field-wrapper bakedbot-position-field">';
        echo '<div class="bakedbot-position-options">';
        
        echo '<label class="bakedbot-position-option ' . ($value === 'left' ? 'selected' : '') . '">';
        echo '<input type="radio" name="' . esc_attr($this->option_name) . '[position]" value="left" ' . checked($value, 'left', false) . '>';
        echo '<span class="dashicons dashicons-align-left"></span>';
        echo '<span class="bakedbot-position-label">' . __('Left', 'bakedbot-chatbot') . '</span>';
        echo '</label>';
        
        echo '<label class="bakedbot-position-option ' . ($value === 'right' ? 'selected' : '') . '">';
        echo '<input type="radio" name="' . esc_attr($this->option_name) . '[position]" value="right" ' . checked($value, 'right', false) . '>';
        echo '<span class="dashicons dashicons-align-right"></span>';
        echo '<span class="bakedbot-position-label">' . __('Right', 'bakedbot-chatbot') . '</span>';
        echo '</label>';
        
        echo '</div>';
        
        if (!empty($description)) {
            echo '<p class="description">' . esc_html($description) . '</p>';
        }
        echo '</div>';
        
        // Add JavaScript to handle the position selection
        echo '<script>
            jQuery(document).ready(function($) {
                $(".bakedbot-position-option").click(function() {
                    $(".bakedbot-position-option").removeClass("selected");
                    $(this).addClass("selected");
                });
            });
        </script>';
    }
    



    
    public function display_settings_page() {
        // Get current tab or default to 'setup'
        $current_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'setup';
        
        // Define tabs - organized by user workflow
        $tabs = array(
            'setup' => __('Setup', 'bakedbot-chatbot'),
            'pages' => __('Pages', 'bakedbot-chatbot'),
            'appearance' => __('Appearance', 'bakedbot-chatbot'),
            'behavior' => __('Behavior', 'bakedbot-chatbot'),
            'product_sync' => __('Product Sync', 'bakedbot-chatbot'),
        );
        
        // Define which sections to show for each tab
        $tab_sections = array(
            'setup' => array('setup_section', 'api_section'),
            'pages' => array('page_setup_section'),
            'appearance' => array('appearance_section', 'position_section'),
            'behavior' => array('behavior_section'),
            'product_sync' => array('product_sync_section'),
        );
        
        // Get the current tab's sections
        $current_sections = isset($tab_sections[$current_tab]) ? $tab_sections[$current_tab] : array();
        
        ?>
        <div class="wrap bakedbot-admin">
            <!-- Custom notice container -->
            <div class="bakedbot-notices-container">
                <?php settings_errors('bakedbot_chatbot_options'); ?>
            </div>
            
            <div class="bakedbot-header">
                <div class="bakedbot-logo">
                    <img src="<?php echo esc_url($this->plugin_url . 'dist/images/blunt-smokey-sm.png'); ?>" alt="BakedBot Logo" class="bakedbot-logo-img">
                </div>
                <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            </div>
            
            <div class="bakedbot-admin-content">
                <div class="bakedbot-sidebar">
                    <div class="bakedbot-sidebar-inner">
                        <div class="bakedbot-sidebar-header">
                            <h2><?php _e('BakedBot Chatbot', 'bakedbot-chatbot'); ?></h2>
                            <p><?php _e('Version', 'bakedbot-chatbot'); ?> <?php echo BAKEDBOT_CHATBOT_VERSION; ?></p>
                        </div>
                        
                        <nav class="bakedbot-nav">
                            <ul>
                                <?php foreach ($tabs as $tab_id => $tab_name) : ?>
                                    <li class="<?php echo $current_tab === $tab_id ? 'active' : ''; ?>">
                                        <a href="<?php echo esc_url(add_query_arg('tab', $tab_id)); ?>">
                                            <?php echo esc_html($tab_name); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </nav>
                        
                        <div class="bakedbot-sidebar-footer">
                            <p><?php _e('Need help?', 'bakedbot-chatbot'); ?></p>
                            <a href="https://bakedbot.ai/contact/" target="_blank" class="button button-secondary">
                                <?php _e('Visit Support', 'bakedbot-chatbot'); ?>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bakedbot-main-content">
                                            <div class="bakedbot-tab-content" style="overflow: visible; min-height: auto; padding-bottom: 40px;">
                            <form action="options.php" method="post">
                            <?php
                            settings_fields('bakedbot_chatbot_options');
                            
                            // Display the appropriate header for the current tab
                            switch ($current_tab) {
                                case 'setup':
                                    echo '<div class="bakedbot-section-header">';
                                    echo '<h2>' . __('Setup BakedBot', 'bakedbot-chatbot') . '</h2>';
                                    echo '<p>' . __('Configure your API credentials and basic settings to get started.', 'bakedbot-chatbot') . '</p>';
                                    echo '</div>';
                                    break;
                                case 'pages':
                                    echo '<div class="bakedbot-section-header">';
                                    echo '<h2>' . __('Page Management', 'bakedbot-chatbot') . '</h2>';
                                    echo '<p>' . __('Create new pages with BakedBot functionality or add the chatbot to existing pages.', 'bakedbot-chatbot') . '</p>';
                                    echo '</div>';
                                    break;
                                case 'appearance':
                                    echo '<div class="bakedbot-section-header">';
                                    echo '<h2>' . __('Appearance & Styling', 'bakedbot-chatbot') . '</h2>';
                                    echo '<p>' . __('Customize colors, position, and visual elements to match your brand.', 'bakedbot-chatbot') . '</p>';
                                    echo '</div>';
                                    break;
                                case 'behavior':
                                    echo '<div class="bakedbot-section-header">';
                                    echo '<h2>' . __('Behavior Settings', 'bakedbot-chatbot') . '</h2>';
                                    echo '<p>' . __('Configure how the chatbot behaves and appears to your visitors.', 'bakedbot-chatbot') . '</p>';
                                    echo '</div>';
                                    break;
                                case 'product_sync':
                                    echo '<div class="bakedbot-section-header">';
                                    echo '<h2>' . __('WooCommerce Integration', 'bakedbot-chatbot') . '</h2>';
                                    echo '<p>' . __('Sync products between WooCommerce and BakedBot for a complete ecommerce experience.', 'bakedbot-chatbot') . '</p>';
                                    echo '</div>';
                                    break;
                            }
                            
                            // Only show the sections for the current tab
                            global $wp_settings_sections;
                            if (isset($wp_settings_sections['bakedbot_chatbot'])) {
                                foreach ($wp_settings_sections['bakedbot_chatbot'] as $section_id => $section) {
                                    if (in_array($section_id, $current_sections)) {
                                        // For appearance tab, use a two-column layout
                                        if ($current_tab === 'appearance' && $section_id === 'appearance_section') {
                                            echo '<div class="bakedbot-settings-section">';
                                            
                                            // Colors Section - Removed the h3 title as requested
                                            echo '<div class="bakedbot-settings-section bakedbot-compact-section">';
                                            echo '<div class="bakedbot-columns">';
                                            
                                            // First column - Primary, Secondary colors
                                            echo '<div class="bakedbot-column">';
                                            echo '<table class="form-table" role="presentation">';
                                            
                                            // Get first half of color fields
                                            $color_fields_col1 = array('primary_color', 'secondary_color');
                                            foreach ($color_fields_col1 as $field) {
                                                do_settings_fields('bakedbot_chatbot', 'appearance_section_' . $field);
                                            }
                                            
                                            echo '</table>';
                                            echo '</div>';
                                            
                                            // Second column - Background, Header, Text colors
                                            echo '<div class="bakedbot-column">';
                                            echo '<table class="form-table" role="presentation">';
                                            
                                            // Get second half of color fields
                                            $color_fields_col2 = array('background_color', 'header_color', 'text_color');
                                            foreach ($color_fields_col2 as $field) {
                                                do_settings_fields('bakedbot_chatbot', 'appearance_section_' . $field);
                                            }
                                            
                                            echo '</table>';
                                            echo '</div>';
                                            
                                            echo '</div>'; // End colors columns
                                            echo '</div>'; // End colors section
                                            
                                            // Display Options Section
                                            echo '<div class="bakedbot-settings-section">';
                                            echo '<h3>' . __('Display Options', 'bakedbot-chatbot') . '</h3>';
                                            echo '<div class="bakedbot-columns">';
                                            
                                            // First column - Title, Welcome Message
                                            echo '<div class="bakedbot-column">';
                                            echo '<table class="form-table" role="presentation">';
                                            
                                            // First half of display options
                                            $display_fields_col1 = array('chatbot_title', 'welcome_message');
                                            foreach ($display_fields_col1 as $field) {
                                                do_settings_fields('bakedbot_chatbot', 'appearance_section_' . $field);
                                            }
                                            
                                            echo '</table>';
                                            echo '</div>';
                                            
                                            // Second column - Button Text, Avatar URL, Theme Settings
                                            echo '<div class="bakedbot-column">';
                                            echo '<table class="form-table" role="presentation">';
                                            
                                            // Second half of display options
                                            $display_fields_col2 = array('button_text', 'avatar_url', 'disable_theme_settings');
                                            foreach ($display_fields_col2 as $field) {
                                                do_settings_fields('bakedbot_chatbot', 'appearance_section_' . $field);
                                            }
                                            
                                            echo '</table>';
                                            echo '</div>';
                                            
                                            echo '</div>'; // End display options columns
                                            echo '</div>'; // End display options section
                                            
                                            echo '</div>'; // End main appearance section
                                        } else if ($current_tab === 'appearance' && $section_id === 'position_section') {
                                            // Position & Layout Section - Keep as a separate row
                                            echo '<div class="bakedbot-settings-section">';
                                            echo '<h3>' . __('Position & Layout', 'bakedbot-chatbot') . '</h3>';
                                            
                                            echo '<table class="form-table" role="presentation">';
                                            do_settings_fields('bakedbot_chatbot', $section_id);
                                            echo '</table>';
                                            
                                            echo '</div>'; // End position section
                                        } else {
                                            echo '<div class="bakedbot-settings-section">';
                                            if ($section['title']) {
                                                echo '<h3>' . $section['title'] . '</h3>';
                                            }
                                            if ($section['callback']) {
                                                call_user_func($section['callback']);
                                            }
                                            
                                            echo '<table class="form-table" role="presentation">';
                                            do_settings_fields('bakedbot_chatbot', $section_id);
                                            echo '</table>';
                                            
                                            echo '</div>'; // End section
                                        }
                                    }
                                }
                            }
                            
                            submit_button('Save Settings');
                            ?>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
            .bakedbot-admin {
                margin: 20px 0;
                max-width: 1200px;
            }
            
            /* Hide WordPress notices on our page */
            .bakedbot-admin .notice:not(.bakedbot-notice) {
                display: none !important;
            }
            
            /* Hide all other plugin notices */
            body.toplevel_page_bakedbot-settings .notice:not(.bakedbot-notice) {
                display: none !important;
            }
            
            /* Custom notice container */
            .bakedbot-notices-container {
                margin-bottom: 15px;
            }
            
            .bakedbot-notices-container .notice {
                margin: 5px 0 15px;
                box-shadow: 0 1px 2px rgba(0,0,0,0.05);
            }
            
            .bakedbot-header {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
                background: #fff;
                padding: 12px 15px;
                border-radius: 6px;
                box-shadow: 0 1px 2px rgba(0,0,0,0.08);
            }
            
            .bakedbot-logo {
                margin-right: 15px;
            }
            
            .bakedbot-logo-img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
                border: 2px solid #057540;
                padding: 2px;
                background-color: #fff;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }
            
            .bakedbot-admin-content {
                display: flex;
                background: #f9f9f9;
                border-radius: 6px;
                overflow: visible;
                box-shadow: 0 1px 2px rgba(0,0,0,0.08);
                min-height: auto;
            }
            
            .bakedbot-sidebar {
                width: 180px;
                background: #057540;
                color: #fff;
            }
            
            .bakedbot-sidebar-inner {
                display: flex;
                flex-direction: column;
                height: 100%;
                min-height: auto;
            }
            
            .bakedbot-sidebar-header {
                padding: 15px;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }
            
            .bakedbot-sidebar-header h2 {
                margin: 0 0 5px;
                font-size: 16px;
                color: #fff;
            }
            
            .bakedbot-sidebar-header p {
                margin: 0;
                font-size: 12px;
                opacity: 0.8;
            }
            
            .bakedbot-nav {
                flex-grow: 1;
            }
            
            .bakedbot-nav ul {
                margin: 0;
                padding: 0;
                list-style: none;
            }
            
            .bakedbot-nav li {
                margin: 0;
            }
            
            .bakedbot-nav a {
                display: block;
                padding: 12px 15px;
                color: rgba(255,255,255,0.8);
                text-decoration: none;
                border-left: 3px solid transparent;
                transition: all 0.2s ease;
            }
            
            .bakedbot-nav a:hover {
                background: rgba(255,255,255,0.1);
                color: #fff;
            }
            
            .bakedbot-nav li.active a {
                background: rgba(255,255,255,0.15);
                color: #fff;
                border-left-color: #fff;
            }
            
            .bakedbot-sidebar-footer {
                padding: 15px;
                border-top: 1px solid rgba(255,255,255,0.1);
            }
            
            .bakedbot-sidebar-footer p {
                margin: 0 0 10px;
                font-size: 12px;
            }
            
            .bakedbot-main-content {
                flex-grow: 1;
                padding: 20px;
                background: #fff;
                overflow: visible;
                min-height: auto;
            }
            
            .bakedbot-section-header {
                margin-bottom: 20px;
            }
            
            .bakedbot-section-header h2 {
                margin: 0 0 5px;
                font-size: 20px;
                color: #057540;
            }
            
            .bakedbot-section-header p {
                margin: 0;
                color: #666;
            }
            
            .bakedbot-settings-section {
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #eee;
            }
            
            /* Compact section for colors */
            .bakedbot-compact-section {
                margin-bottom: 10px;
                padding-bottom: 10px;
            }
            
            .bakedbot-settings-section:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: none;
            }
            
            .bakedbot-settings-section h3 {
                margin: 0 0 10px;
                padding-bottom: 6px;
                font-size: 15px;
                border-bottom: 1px solid #eee;
            }
            
            /* Column layout */
            .bakedbot-columns {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
            }
            
            .bakedbot-column {
                flex: 1;
                min-width: 300px;
            }
            
            /* Responsive columns - stack on mobile */
            @media screen and (max-width: 782px) {
                .bakedbot-columns {
                    flex-direction: column;
                }
                
                .bakedbot-column {
                    flex: 1 1 100%;
                }
            }
            
            /* Field styles */
            .bakedbot-field-wrapper {
                margin-bottom: 15px;
            }
            
            .bakedbot-field-wrapper .description {
                margin: 5px 0 0;
                color: #666;
                font-style: italic;
                font-size: 13px;
            }
            
            /* Color field styles */
            .bakedbot-color-picker-wrapper {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .bakedbot-color-picker-wrapper input[type="color"] {
                width: 60px;
                height: 40px;
                padding: 0;
                border: 1px solid #ddd;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .bakedbot-color-text {
                width: 100px;
                height: 40px;
                padding: 0 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-family: monospace;
            }
            
            /* Color description styling */
            .bakedbot-color-description-row {
                display: block;
                margin-top: 3px; /* Reduced margin for more compact layout */
                color: #666;
                font-style: italic;
                font-size: 12px; /* Slightly smaller font for descriptions */
                width: 100%;
                padding-left: 2px;
            }
            
            /* Toggle switch for checkboxes */
            .bakedbot-toggle {
                position: relative;
                display: inline-block;
                width: 60px;
                height: 30px;
            }
            
            .bakedbot-toggle input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            
            .bakedbot-toggle-slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 34px;
            }
            
            .bakedbot-toggle-slider:before {
                position: absolute;
                content: "";
                height: 22px;
                width: 22px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }
            
            .bakedbot-toggle input:checked + .bakedbot-toggle-slider {
                background-color: #057540;
            }
            
            .bakedbot-toggle input:focus + .bakedbot-toggle-slider {
                box-shadow: 0 0 1px #057540;
            }
            
            .bakedbot-toggle input:checked + .bakedbot-toggle-slider:before {
                transform: translateX(30px);
            }
            
            /* Position field styles */
            .bakedbot-position-options {
                display: flex;
                gap: 15px;
                margin-bottom: 10px;
            }
            
            .bakedbot-position-option {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 15px;
                border: 2px solid #ddd;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                width: 100px;
                text-align: center;
            }
            
            .bakedbot-position-option input {
                display: none;
            }
            
            .bakedbot-position-option .dashicons {
                font-size: 24px;
                width: 24px;
                height: 24px;
                margin-bottom: 8px;
                color: #666;
            }
            
            .bakedbot-position-option .bakedbot-position-label {
                font-weight: 500;
            }
            
            .bakedbot-position-option.selected {
                border-color: #057540;
                background-color: rgba(5, 117, 64, 0.05);
            }
            
            .bakedbot-position-option.selected .dashicons,
            .bakedbot-position-option.selected .bakedbot-position-label {
                color: #057540;
            }
            
            /* Auth mode field styles */
            .bakedbot-auth-mode-options {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin-bottom: 10px;
            }
            
            .bakedbot-auth-mode-option {
                display: flex;
                border: 2px solid #ddd;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                overflow: hidden;
            }
            
            .bakedbot-auth-mode-option input {
                display: none;
            }
            
            .bakedbot-auth-mode-content {
                padding: 15px;
                flex: 1;
            }
            
            .bakedbot-auth-mode-content h4 {
                margin: 0 0 5px;
                font-size: 16px;
            }
            
            .bakedbot-auth-mode-content p {
                margin: 0;
                color: #666;
                font-size: 13px;
            }
            
            .bakedbot-auth-mode-option.selected {
                border-color: #057540;
                background-color: rgba(5, 117, 64, 0.05);
            }
            
            .bakedbot-auth-mode-option.selected h4 {
                color: #057540;
            }
            
            /* Two-column layout for appearance tab */
            .bakedbot-columns {
                display: flex;
                flex-wrap: wrap;
                margin: 0 -10px;
            }
            
            .bakedbot-column {
                flex: 1;
                min-width: 300px;
                padding: 0 10px;
                box-sizing: border-box;
            }
            
            .bakedbot-column h4 {
                margin: 0 0 10px;
                padding-bottom: 8px;
                font-size: 16px;
                border-bottom: 1px solid #eee;
                color: #057540;
            }
            
            .bakedbot-column p {
                margin: 0 0 15px;
                color: #666;
            }
            
            /* Color field styling */
            .bakedbot-color-field {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 3px; /* Reduced gap for more compact layout */
                margin-bottom: 8px; /* Reduced margin for more compact layout */
            }
            
            .bakedbot-color-picker-wrapper {
                display: flex;
                align-items: center;
                gap: 10px;
                width: 100%;
            }
            
            .bakedbot-color-preview {
                width: 30px;
                height: 30px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            /* Form table styles */
            .form-table {
                margin-top: 0;
            }
            
            .form-table th {
                width: auto;
                padding: 10px 10px 10px 0; /* Reduced padding for more compact layout */
                vertical-align: top;
            }
            
            .form-table td {
                padding: 6px 10px; /* Reduced padding for more compact layout */
            }
            
            .form-table tr {
                border-bottom: 1px solid #f0f0f0;
            }
            
            .form-table tr:last-child {
                border-bottom: none;
            }
            
            /* Submit button styles */
            .submit {
                margin-top: 20px;
                padding-top: 15px;
                border-top: 1px solid #eee;
            }
            
            .submit .button-primary {
                background: #057540;
                border-color: #057540;
                color: #fff;
                padding: 5px 20px;
                height: auto;
                text-shadow: none;
                box-shadow: none;
                font-size: 14px;
                line-height: 2;
                min-height: 30px;
                border-radius: 3px;
                cursor: pointer;
            }
            
            .submit .button-primary:hover {
                background: #046030;
                border-color: #046030;
            }
            
            /* Responsive styles */
            @media screen and (max-width: 782px) {
                .bakedbot-admin-content {
                    flex-direction: column;
                }
                
                .bakedbot-sidebar {
                    width: 100%;
                }
                
                .bakedbot-sidebar-inner {
                    min-height: auto;
                }
                
                .bakedbot-nav ul {
                    display: flex;
                    flex-wrap: wrap;
                }
                
                .bakedbot-nav li {
                    flex: 1;
                    min-width: 120px;
                    text-align: center;
                }
                
                .bakedbot-nav li a {
                    border-left: none;
                    border-bottom: 3px solid transparent;
                }
                
                .bakedbot-nav li.active a {
                    border-left-color: transparent;
                    border-bottom-color: #fff;
                }
                
                .form-table th {
                    width: 100%;
                    display: block;
                    padding-bottom: 0;
                }
                
                .form-table td {
                    width: 100%;
                    display: block;
                    padding-top: 0;
                }
                
                .bakedbot-columns {
                    display: block;
                }
                
                .bakedbot-column {
                    width: 100%;
                    padding: 0;
                }
            }
        </style>
        <?php
    }
    

    

    

    

    

    

    

    
    /**
     * Get page status for the admin interface
     */
    public function get_page_status($slug) {
        $page = get_page_by_path($slug);
        if ($page) {
            $edit_url = admin_url('post.php?post=' . $page->ID . '&action=edit');
            $view_url = get_permalink($page->ID);
            return '<span class="exists">✓ ' . __('Page exists', 'bakedbot-chatbot') . ': <a href="' . esc_url($edit_url) . '" target="_blank">' . __('Edit', 'bakedbot-chatbot') . '</a> | <a href="' . esc_url($view_url) . '" target="_blank">' . __('View', 'bakedbot-chatbot') . '</a></span>';
        } else {
            return '<span class="not-exists">✗ ' . __('Page does not exist', 'bakedbot-chatbot') . '</span>';
        }
    }
    
    /**
     * AJAX handler for creating pages
     */
    public function ajax_create_page() {
        check_ajax_referer('bakedbot_create_page_nonce', 'security');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }
        
        $page_type = sanitize_text_field($_POST['page_type']);
        $title = sanitize_text_field($_POST['title']);
        $slug = sanitize_text_field($_POST['slug']);
        $content = wp_kses_post($_POST['content']);
        $description = sanitize_text_field($_POST['description']);
        $full_width = isset($_POST['full_width']) ? (bool) $_POST['full_width'] : true;
        
        // Check if page already exists
        $existing_page = get_page_by_path($slug);
        if ($existing_page) {
            wp_send_json_error(__('A page with this slug already exists.', 'bakedbot-chatbot'));
            return;
        }
        
        // Prepare meta input array
        $meta_input = array(
            '_bakedbot_auto_created' => true,
            '_yoast_wpseo_metadesc'  => $description,
            '_yoast_wpseo_title'     => $title . ' | %%sitename%%',
        );
        
        // Add full-width meta if enabled
        if ($full_width) {
            $meta_input['_bakedbot_full_width'] = true;
        }
        
        // Create the page
        $page_data = array(
            'post_title'     => $title,
            'post_name'      => $slug,
            'post_content'   => $content,
            'post_excerpt'   => $description,
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'post_author'    => get_current_user_id(),
            'comment_status' => 'closed',
            'ping_status'    => 'closed',
            'meta_input'     => $meta_input
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            $edit_url = admin_url('post.php?post=' . $page_id . '&action=edit');
            $view_url = get_permalink($page_id);
            
            wp_send_json_success(array(
                'page_id' => $page_id,
                'edit_url' => $edit_url,
                'view_url' => $view_url,
                'message' => sprintf(__('Page "%s" created successfully!', 'bakedbot-chatbot'), $title)
            ));
        } else {
            wp_send_json_error(__('Failed to create page. Please try again.', 'bakedbot-chatbot'));
        }
    }
    
    /**
     * AJAX handler for adding shortcodes to existing pages
     */
    public function ajax_add_to_pages() {
        check_ajax_referer('bakedbot_add_to_pages_nonce', 'security');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }
        
        $page_ids = array_map('sanitize_text_field', $_POST['page_ids']);
        $shortcode = sanitize_text_field($_POST['shortcode']);
        
        if (empty($page_ids)) {
            wp_send_json_error(__('No pages selected.', 'bakedbot-chatbot'));
            return;
        }
        
        $updated_count = 0;
        $errors = array();
        $frontpage_toggled = false;
        
        foreach ($page_ids as $page_id) {
            if ($page_id === 'frontpage') {
                // Handle front page toggle
                $current_options = get_option($this->option_name, array());
                $current_options['show_on_frontpage'] = true;
                update_option($this->option_name, $current_options);
                $frontpage_toggled = true;
                $updated_count++;
                continue;
            }
            
            $page = get_post(intval($page_id));
            
            if (!$page || $page->post_type !== 'page') {
                $errors[] = sprintf(__('Page ID %d not found or is not a page.', 'bakedbot-chatbot'), intval($page_id));
                continue;
            }
            
            // Check if the shortcode already exists in the content
            if (strpos($page->post_content, $shortcode) !== false) {
                continue; // Skip if shortcode already exists
            }
            
            // Add the shortcode to the end of the page content
            $updated_content = $page->post_content . "\n\n" . $shortcode;
            
            $result = wp_update_post(array(
                'ID' => intval($page_id),
                'post_content' => $updated_content
            ));
            
            if (is_wp_error($result)) {
                $errors[] = sprintf(__('Failed to update page "%s": %s', 'bakedbot-chatbot'), $page->post_title, $result->get_error_message());
            } else {
                $updated_count++;
            }
        }
        
        if ($updated_count > 0) {
            $message = sprintf(
                _n(
                    'Successfully added chatbot to %d page.',
                    'Successfully added chatbot to %d pages.',
                    $updated_count,
                    'bakedbot-chatbot'
                ),
                $updated_count
            );
            
            if ($frontpage_toggled) {
                $message .= ' ' . __('Front page chatbot enabled.', 'bakedbot-chatbot');
            }
            
            if (!empty($errors)) {
                $message .= ' ' . __('Some pages could not be updated:', 'bakedbot-chatbot') . ' ' . implode(', ', $errors);
            }
            
            wp_send_json_success(array('message' => $message));
        } else {
            $error_message = __('No pages were updated.', 'bakedbot-chatbot');
            if (!empty($errors)) {
                $error_message .= ' ' . __('Errors:', 'bakedbot-chatbot') . ' ' . implode(', ', $errors);
            }
            wp_send_json_error($error_message);
        }
    }
    
    /**
     * Handle WordPress user registration
     */
    public function user_registered($user_id) {
        if (!$this->options['sync_users']) {
            return;
        }
        
        // Get the new user's data
        $user = get_userdata($user_id);
        
        if (!$user) {
            return;
        }
        
        // Check if we should try to find a matching BakedBot user
        // This would involve an API call to your BakedBot backend
        // For now, we'll just set up default preferences
        $preferences = array(
            'savechat' => 1,
            'newsletter' => 0
        );
        
        update_user_meta($user_id, 'bakedbot_preferences', $preferences);
    }
    
    /**
     * Handle WordPress user login
     */
    public function user_login($user_login, $user) {
        if (!$this->options['sync_users']) {
            return;
        }
        
        // Get the BakedBot user ID if it exists
        $bakedbot_id = get_user_meta($user->ID, 'bakedbot_user_id', true);
        
        // If there's no BakedBot ID, we could try to find a matching user
        // This would involve an API call to your BakedBot backend
        // For simplicity, we'll skip this for now
    }

    // Add admin notice if API key or site identifier is missing
    public function admin_notices() {
        // Only show on non-BakedBot pages
        $screen = get_current_screen();
        if ($screen && $screen->id === 'toplevel_page_bakedbot-settings') {
            return;
        }
        
        if (empty($this->options['api_key'])) {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p><?php echo sprintf(
                    __('BakedBot Chatbot requires configuration. Please <a href="%s">configure the plugin</a> with your API key.', 'bakedbot-chatbot'),
                    admin_url('admin.php?page=bakedbot-settings')
                ); ?></p>
            </div>
            <?php
        }
    }

    // Remove admin notices on our settings page
    public function remove_admin_notices() {
        $screen = get_current_screen();
        if ($screen && $screen->id === 'toplevel_page_bakedbot-settings') {
            remove_all_actions('admin_notices');
            remove_all_actions('all_admin_notices');
            
            // Add our own notices container
            add_action('admin_notices', function() {
                echo '<div class="bakedbot-notices">';
                settings_errors();
                echo '</div>';
            });
        }
    }

    // Activation hook
    public static function activate() {
        // Set default options if not already set
        $option_name = 'bakedbot_chatbot_options';
        if (!get_option($option_name)) {
            $default_options = array(
                'primary_color' => '#057540',
                'secondary_color' => '#0D211D',
                'background_color' => '#FFFFFF',
                'header_color' => '#FFFFFF',
                'text_color' => '#2C2C2C',
                'position' => 'right',
                'auto_open' => false,
                'show_on_mobile' => true,
                'show_on_frontpage' => true,
                'api_key' => '',
                'sync_users' => true,
                'auth_mode' => 'federated',
                'bakedbot_api_url' => 'https://beta.bakedbot.ai/api/',
                'debug_mode' => false,
                'use_wordpress_auth' => true,
                'user_mode_only' => false,
                'disable_theme_settings' => true,
                'product_page_slug' => '/product'
            );
            add_option($option_name, $default_options);
        }

        // Note: Pages are now created manually from the admin panel
        
        // Add rewrite rules for SEO product pages
        add_option('bakedbot_rewrite_rules_flushed', false);
        flush_rewrite_rules();
    }

    /**
     * Create default pages when plugin is activated
     */
    private static function create_plugin_pages() {
        $created_pages = array();

        // Dispensary Menu page
        $menu_page = self::create_page_if_not_exists(
            'Dispensary Menu',
            'dispensary-menu',
            '[bakedbot_menu layout="grid" enable_search="true" enable_filters="true" enable_cart="true" products_per_page="12"]',
            'Premium cannabis product catalog with advanced filtering, search, and seamless checkout experience.'
        );
        if ($menu_page) {
            $created_pages['menu'] = $menu_page;
        }

        // Dispensary Homepage
        $homepage_page = self::create_page_if_not_exists(
            'Dispensary Homepage',
            'dispensary-homepage',
            '[bakedbot_homepage enable_cart="true" theme="light" featured_products_count="8" popular_categories_count="6" show_promotion="true" promotion_title="Premium Cannabis Marketplace" promotion_subtitle="Discover our curated selection of premium cannabis products" promotion_image_url="https://picsum.photos/400" menu_page_url="/dispensary-menu" product_page_url="/product"]',
            'Premium cannabis homepage with featured products, categories, and promotional sections.'
        );
        if ($homepage_page) {
            $created_pages['homepage'] = $homepage_page;
        }

        // Product Details Page
        $product_page = self::create_page_if_not_exists(
            'Product Details',
            'product',
            '[bakedbot_product]',
            'Individual product detail pages showing product information, variants, and purchase options.'
        );
        if ($product_page) {
            $created_pages['product'] = $product_page;
        }

        // Chat page  
        $chat_page = self::create_page_if_not_exists(
            'BakedBot Assistant',
            'bakedbot-chat',
            '[bakedbot_chatbot]<br><br>Ask our AI budtender anything about cannabis products, effects, and recommendations.',
            'Get personalized cannabis product recommendations from our AI budtender.'
        );
        if ($chat_page) {
            $created_pages['chat'] = $chat_page;
        }

        // Product finder page
        $finder_page = self::create_page_if_not_exists(
            'Product Finder',
            'product-finder',
            '<h2>Find Your Perfect Cannabis Product</h2><p>Use our AI-powered product finder to discover cannabis products based on your preferences and desired effects.</p>[bakedbot_menu layout="grid" enable_filters="true" theme="custom" products_per_page="16"]',
            'AI-powered cannabis product finder to help you discover the perfect products for your needs.'
        );
        if ($finder_page) {
            $created_pages['finder'] = $finder_page;
        }

        // Store created page IDs for cleanup during deactivation
        if (!empty($created_pages)) {
            update_option('bakedbot_created_pages', $created_pages);
            
            // Optionally add pages to the main menu
            self::add_pages_to_menu($created_pages);
        }
    }

    /**
     * Helper function to create a page if it doesn't already exist
     */
    private static function create_page_if_not_exists($title, $slug, $content, $excerpt = '') {
        // Check if page already exists
        $existing_page = get_page_by_path($slug);
        if ($existing_page) {
            return false; // Page already exists, don't create
        }

        // Check if a page with this title exists
        $existing_page_by_title = get_page_by_title($title);
        if ($existing_page_by_title) {
            return false; // Page with this title already exists
        }

        // Create the page
        $page_data = array(
            'post_title'     => $title,
            'post_name'      => $slug,
            'post_content'   => $content,
            'post_excerpt'   => $excerpt,
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'post_author'    => 1, // Admin user
            'comment_status' => 'closed',
            'ping_status'    => 'closed',
            'meta_input'     => array(
                '_bakedbot_auto_created' => true, // Mark as auto-created for easy identification
                '_yoast_wpseo_metadesc'  => $excerpt, // SEO meta description
                '_yoast_wpseo_title'     => $title . ' | %%sitename%%', // SEO title template
            )
        );

        $page_id = wp_insert_post($page_data);

        if ($page_id && !is_wp_error($page_id)) {
            return $page_id;
        }

        return false;
    }

    /**
     * Add created pages to the main navigation menu
     */
    private static function add_pages_to_menu($created_pages) {
        // Get the primary menu location
        $menu_locations = get_theme_mod('nav_menu_locations');
        $primary_menu_location = '';
        
        // Common primary menu location names
        $common_locations = array('primary', 'main', 'header', 'top', 'navigation');
        
        foreach ($common_locations as $location) {
            if (isset($menu_locations[$location])) {
                $primary_menu_location = $location;
                break;
            }
        }
        
        // If no common location found, use the first available
        if (empty($primary_menu_location) && !empty($menu_locations)) {
            $primary_menu_location = array_keys($menu_locations)[0];
        }
        
        // If we found a menu location, try to add our pages
        if (!empty($primary_menu_location) && isset($menu_locations[$primary_menu_location])) {
            $menu_id = $menu_locations[$primary_menu_location];
            
            // Define the order and which pages to add to menu
            $menu_pages = array(
                'menu'     => array('title' => 'Dispensary Menu', 'priority' => 10),
                'homepage' => array('title' => 'Dispensary Homepage', 'priority' => 20),
                'finder'   => array('title' => 'Product Finder', 'priority' => 30),
                'chat'     => array('title' => 'Ask BakedBot', 'priority' => 40)
            );
            
            foreach ($menu_pages as $page_type => $menu_data) {
                if (isset($created_pages[$page_type])) {
                    $page_id = $created_pages[$page_type];
                    
                    // Check if this page is already in the menu
                    $menu_items = wp_get_nav_menu_items($menu_id);
                    $page_in_menu = false;
                    
                    if ($menu_items) {
                        foreach ($menu_items as $menu_item) {
                            if ($menu_item->object_id == $page_id && $menu_item->object == 'page') {
                                $page_in_menu = true;
                                break;
                            }
                        }
                    }
                    
                    // Add to menu if not already present
                    if (!$page_in_menu) {
                        wp_update_nav_menu_item($menu_id, 0, array(
                            'menu-item-title'     => $menu_data['title'],
                            'menu-item-object'    => 'page',
                            'menu-item-object-id' => $page_id,
                            'menu-item-type'      => 'post_type',
                            'menu-item-status'    => 'publish',
                            'menu-item-position'  => $menu_data['priority']
                        ));
                    }
                }
            }
        }
    }

    // Deactivation hook
    public static function deactivate() {
        // Clean up created pages
        self::cleanup_plugin_pages();
        
        // Clean up rewrite rules
        flush_rewrite_rules();
        
        // We'll keep the settings in case the plugin is reactivated
    }

    /**
     * Remove pages that were created during plugin activation
     */
    private static function cleanup_plugin_pages() {
        $created_pages = get_option('bakedbot_created_pages', array());
        
        if (!empty($created_pages)) {
            foreach ($created_pages as $page_type => $page_id) {
                // Verify the page was auto-created by our plugin before deleting
                $is_auto_created = get_post_meta($page_id, '_bakedbot_auto_created', true);
                
                if ($is_auto_created) {
                    // Remove from menus before deleting
                    self::remove_page_from_menus($page_id);
                    
                    // Move to trash instead of permanent deletion for safety
                    wp_trash_post($page_id);
                }
            }
            
            // Remove the option
            delete_option('bakedbot_created_pages');
        }
        
        // Also clean up any other auto-created pages that might have been missed
        $auto_created_pages = get_posts(array(
            'post_type'      => 'page',
            'post_status'    => 'any',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'   => '_bakedbot_auto_created',
                    'value' => true,
                    'compare' => '='
                )
            )
        ));
        
        foreach ($auto_created_pages as $page) {
            self::remove_page_from_menus($page->ID);
            wp_trash_post($page->ID);
        }
    }

    /**
     * Remove a page from all navigation menus
     */
    private static function remove_page_from_menus($page_id) {
        // Get all registered menus
        $menus = wp_get_nav_menus();
        
        foreach ($menus as $menu) {
            $menu_items = wp_get_nav_menu_items($menu->term_id);
            
            if ($menu_items) {
                foreach ($menu_items as $menu_item) {
                    // Check if this menu item points to our page
                    if ($menu_item->object_id == $page_id && $menu_item->object == 'page') {
                        wp_delete_post($menu_item->ID, true); // Delete menu item permanently
                    }
                }
            }
        }
    }

    /**
     * Get a specific option value with fallback
     * Always gets fresh values from the database to ensure latest settings
     */
    public function get_option($key, $default = '') {
        // Always get fresh options from database to ensure we have the latest settings
        $fresh_options = get_option($this->option_name, array());
        
        if (isset($fresh_options[$key])) {
            return $fresh_options[$key];
        }
        
        // Fall back to cached options if not found in fresh options
        if (isset($this->options[$key])) {
            return $this->options[$key];
        }
        
        return $default;
    }

    public function get_logged_in_user_script() {
        // Get current WordPress user information for JavaScript
        $current_user = wp_get_current_user();
        $user_id = $current_user->ID;
        
        // Get BakedBot user ID if it exists
        $bakedbot_id = get_user_meta($user_id, 'bakedbot_user_id', true);
        $bakedbot_token = get_user_meta($user_id, 'bakedbot_auth_token', true);
        
        // Get user preferences
        $preferences = $this->get_user_preferences($user_id);
        $preferences_json = json_encode($preferences);
        
        return "
            // Initialize BakedBotUser if it doesn't exist
            if (!window.BakedBotUser) {
                window.BakedBotUser = {};
            }
            
            Object.assign(window.BakedBotUser, {
                userId: " . esc_js($user_id) . ",
                displayName: '" . esc_js($current_user->display_name) . "',
                email: '" . esc_js($current_user->user_email) . "',
                synced: " . (!empty($bakedbot_id) ? 'true' : 'false') . ",
                bakedBotId: '" . esc_js($bakedbot_id) . "',
                authToken: '" . esc_js($bakedbot_token) . "',
                preferences: " . $preferences_json . "
            });
        ";
    }

    /**
     * Initialize Action Scheduler hooks for product sync
     */
    private function init_action_scheduler() {
        // Register Action Scheduler hook for product import
        add_action('bakedbot_import_page', array($this, 'import_page'), 10, 2);
        
        // Register Action Scheduler hook for product export
        add_action('bakedbot_export_batch', array($this, 'export_batch'), 10, 4);
    }
    
    /**
     * AJAX handler to start the product import process
     */
    public function ajax_sync_products() {
        check_ajax_referer('bakedbot_sync_nonce', 'security');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }

        $api_key = $this->get_option('api_key', '');
        if (empty($api_key)) {
            wp_send_json_error(__('API key is missing.', 'bakedbot-chatbot'));
            return;
        }

        // Initialize import status
        update_option('bakedbot_import_status', array(
            'current_page' => 1,
            'total_pages'  => 0,
            'completed'    => false,
        ));

        // Schedule the first import job
        if (function_exists('as_enqueue_async_action')) {
            as_enqueue_async_action(
                'bakedbot_import_page',
                array(1, $api_key),
                'bakedbot'
            );
            wp_send_json_success(__('Import started in background.', 'bakedbot-chatbot'));
        } else {
            // If Action Scheduler is not available, start the import directly
            $this->import_page(1, $api_key);
            wp_send_json_success(__('Import started.', 'bakedbot-chatbot'));
        }
    }

    /**
     * AJAX handler to get the import status
     */
    public function ajax_get_import_status() {
        check_ajax_referer('bakedbot_status_nonce', 'security');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }

        $status = get_option('bakedbot_import_status', false);

        if ($status) {
            wp_send_json_success(array('status' => $status));
        } else {
            wp_send_json_error(__('No import in progress.', 'bakedbot-chatbot'));
        }
    }

    /**
     * Handler for importing a page of products
     *
     * @param int    $page    Page number to import.
     * @param string $api_key API key for authentication.
     */
    public function import_page($page, $api_key) {
        try {
            $response = $this->fetch_products($api_key, $page);

            if (is_wp_error($response)) {
                // Log error
                error_log('BakedBot Import Error on page ' . $page . ': ' . $response->get_error_message());
                // Update status with error message
                update_option('bakedbot_import_status', array(
                    'current_page' => $page,
                    'total_pages'  => $page,
                    'completed'    => true,
                    'error'        => $response->get_error_message(),
                ));
                return;
            }

            $products = $response['products'];
            
            // Convert products to match our schema structure before importing
            $processed_products = $this->process_products_for_import($products);
            
            $this->import_products($processed_products);

            $total_pages = $response['total_pages'];

            // Update import status
            update_option('bakedbot_import_status', array(
                'current_page' => $page,
                'total_pages'  => $total_pages,
                'completed'    => $page >= $total_pages,
            ));

            if ($page < $total_pages) {
                // Schedule next page if Action Scheduler is available
                if (function_exists('as_enqueue_async_action')) {
                    as_enqueue_async_action(
                        'bakedbot_import_page',
                        array($page + 1, $api_key),
                        'bakedbot'
                    );
                } else {
                    // If Action Scheduler is not available, continue processing next page
                    $this->import_page($page + 1, $api_key);
                }
            } else {
                // Import completed
                update_option('bakedbot_import_status', array(
                    'current_page' => $page,
                    'total_pages'  => $total_pages,
                    'completed'    => true,
                ));
            }
        } catch (Exception $e) {
            // Log exception
            error_log('BakedBot Import Exception on page ' . $page . ': ' . $e->getMessage());
            // Update status with error message
            update_option('bakedbot_import_status', array(
                'current_page' => $page,
                'total_pages'  => $page,
                'completed'    => true,
                'error'        => 'Exception: ' . $e->getMessage(),
            ));
        }
    }

    /**
     * Fetch products from the BakedBot API
     *
     * @param string $api_key API key for authentication.
     * @param int    $page    Page number to fetch.
     * @return array|WP_Error Array of products and pagination info, or WP_Error on failure.
     */
    private function fetch_products($api_key, $page = 1) {
        // Set up API request to Node.js controller
        $api_base_url = trailingslashit($this->options['bakedbot_api_url']);
        $api_url = $api_base_url . 'public/products';
        
        // Build query parameters
        $query_params = array(
            'page' => $page,
            'limit' => 50, // Number of products per page
            'sort' => 'id',
            'direction' => 'asc'
        );
        
        // Add active filter if import_active_only is enabled
        if ($this->get_option('import_active_only', true)) {
            $query_params['active_only'] = 'true';
        }
        
        // Add pagination parameters
        $api_url = add_query_arg($query_params, $api_url);
        
        $args = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 60,
        );

        // Log request for debugging purposes if debug mode is enabled
        if ($this->get_option('debug_mode', false)) {
            error_log('BakedBot Products API Request: ' . wp_json_encode(array(
                'url' => $api_url,
                'headers' => $args['headers']
            )));
        }

        $response = wp_remote_get($api_url, $args);

        if (is_wp_error($response)) {
            return $response;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        // Log response for debugging
        if ($this->get_option('debug_mode', false)) {
            error_log('BakedBot Products API Response: ' . wp_json_encode(array(
                'code' => $response_code,
                'data_preview' => substr($body, 0, 500) . '...'
            )));
        }

        if ($response_code < 200 || $response_code >= 300 || json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error(
                'api_error',
                sprintf(
                    __('Failed to fetch products: %s (Code: %d)', 'bakedbot-chatbot'),
                    $body,
                    $response_code
                )
            );
        }

        // Extracting products and pagination from the BakedBot API response
        if (!isset($data['products']) || !is_array($data['products'])) {
            return new WP_Error('invalid_response', __('Invalid API response structure.', 'bakedbot-chatbot'));
        }

        $pagination = isset($data['pagination']) ? $data['pagination'] : array();
        $total_pages = isset($pagination['totalPages']) ? intval($pagination['totalPages']) : 1;
        
        // Return the products and pagination info
        return array(
            'products' => $data['products'],
            'total_pages' => $total_pages,
        );
    }

    /**
     * Import products into WooCommerce
     *
     * @param array $products Array of products to import.
     */
    private function import_products($products) {
        $imported_meta_skus = array();

        foreach ($products as $meta_sku_group) {
            try {
                $this->import_product($meta_sku_group);
                $imported_meta_skus[] = $meta_sku_group['meta_sku'];
            } catch (Exception $e) {
                // Log exception with meta_sku for context
                error_log('BakedBot Import Product Exception for meta_sku ' . $meta_sku_group['meta_sku'] . ': ' . $e->getMessage());
                // Continue with next product
                continue;
            }
        }
    }

    /**
     * Import a single product with variations or as a simple product
     *
     * @param array $meta_sku_group Product data from the API.
     */
    private function import_product($meta_sku_group) {
        if (!class_exists('WC_Product_Variable') || !class_exists('WC_Product_Simple')) {
            throw new Exception(__('WooCommerce classes not available', 'bakedbot-chatbot'));
        }

        $meta_sku = $meta_sku_group['meta_sku'];
        $variations_data = $meta_sku_group['products'];

        // Use the first variation as the main product data
        $main_product_data = $variations_data[0];

        // Check if product exists by SKU
        $existing_id = wc_get_product_id_by_sku($meta_sku);

        // Check if we have variation attributes (e.g., weights)
        $has_variations = false;
        foreach ($variations_data as $variation_data) {
            if (!empty($variation_data['display_weight'])) {
                $has_variations = true;
                break;
            }
        }

        if ($has_variations) {
            // Create or update a variable product
            if ($existing_id) {
                $product = wc_get_product($existing_id);
                if (!$product || !is_a($product, 'WC_Product_Variable')) {
                    $product = new WC_Product_Variable($existing_id);
                }
            } else {
                $product = new WC_Product_Variable();
                $product->set_sku($meta_sku);
            }

            // Set main product data
            $product->set_name($main_product_data['raw_product_name']);
            $product->set_description($main_product_data['product_description']);
            if (isset($main_product_data['product_short_description'])) {
                $product->set_short_description($main_product_data['product_short_description']);
            }
            $product->set_status('publish');
            $product->set_catalog_visibility('visible');

            // Set categories
            if (!empty($main_product_data['category'])) {
                $category_ids = $this->get_term_ids($main_product_data['category'], 'product_cat');
                $product->set_category_ids($category_ids);
            }

            // Save cannabis metadata
            $this->save_cannabis_metadata($product->get_id(), $main_product_data);

            // Prepare attributes for variations
            $attributes_data = array();

            foreach ($variations_data as $variation_data) {
                if (!empty($variation_data['display_weight'])) {
                    $attributes_data['Weight'][] = $variation_data['display_weight'];
                }
            }

            // Remove duplicate values
            foreach ($attributes_data as $attr_name => $values) {
                $attributes_data[$attr_name] = array_unique($values);
            }

            // Create or get attributes
            $product_attributes = array();

            foreach ($attributes_data as $attr_name => $attr_values) {
                $taxonomy = $this->create_attribute($attr_name);

                // Ensure terms exist
                foreach ($attr_values as $value) {
                    if (!term_exists($value, $taxonomy)) {
                        wp_insert_term($value, $taxonomy);
                    }
                }

                // Get attribute ID
                $attribute_id = wc_attribute_taxonomy_id_by_name($attr_name);

                // Set product attribute using WC_Product_Attribute
                $attribute = new WC_Product_Attribute();
                $attribute->set_id($attribute_id);
                $attribute->set_name($taxonomy);
                $attribute->set_options($attr_values);
                $attribute->set_position(0);
                $attribute->set_visible(true);
                $attribute->set_variation(true);
                $product_attributes[] = $attribute;
            }

            $product->set_attributes($product_attributes);

            // Set product tags
            if (!empty($main_product_data['product_tags'])) {
                $tag_ids = $this->get_term_ids($main_product_data['product_tags'], 'product_tag');
                $product->set_tag_ids($tag_ids);
            }

            $product_id = $product->save();

            // Set product image
            if (!empty($main_product_data['image_url'])) {
                $this->set_product_image($product_id, $main_product_data['image_url'], $main_product_data['raw_product_name']);
            }

            // Create variations
            foreach ($variations_data as $variation_data) {
                $this->create_or_update_variation($product_id, $variation_data);
            }
        } else {
            // Create or update a simple product
            if ($existing_id) {
                $product = wc_get_product($existing_id);
                if (!$product || !is_a($product, 'WC_Product_Simple')) {
                    $product = new WC_Product_Simple($existing_id);
                }
            } else {
                $product = new WC_Product_Simple();
                $product->set_sku($meta_sku);
            }

            // Set main product data
            $product->set_name($main_product_data['raw_product_name']);
            $product->set_description($main_product_data['product_description']);
            if (isset($main_product_data['product_short_description'])) {
                $product->set_short_description($main_product_data['product_short_description']);
            }
            $product->set_status('publish');
            $product->set_catalog_visibility('visible');

            // Set categories
            if (!empty($main_product_data['category'])) {
                $category_ids = $this->get_term_ids($main_product_data['category'], 'product_cat');
                $product->set_category_ids($category_ids);
            }

            // Save cannabis metadata
            $this->save_cannabis_metadata($product->get_id(), $main_product_data);

            // Set price
            if (isset($main_product_data['latest_price'])) {
                $product->set_regular_price($main_product_data['latest_price']);
            }

            // Set stock status
            $product->set_manage_stock(false);
            $product->set_stock_status('instock');

            $product_id = $product->save();

            // Set product image
            if (!empty($main_product_data['image_url'])) {
                $this->set_product_image($product_id, $main_product_data['image_url'], $main_product_data['raw_product_name']);
            }
        }
    }

    /**
     * Save cannabis metadata to a product
     *
     * @param int   $product_id Product ID
     * @param array $data      Product data containing metadata
     */
    private function save_cannabis_metadata($product_id, $data) {
        // THC content
        if (isset($data['percentage_thc'])) {
            update_post_meta($product_id, '_thc_percentage', $data['percentage_thc']);
        }
        if (isset($data['mg_thc'])) {
            update_post_meta($product_id, '_thc_mg', $data['mg_thc']);
        }

        // CBD content
        if (isset($data['percentage_cbd'])) {
            update_post_meta($product_id, '_cbd_percentage', $data['percentage_cbd']);
        }
        if (isset($data['mg_cbd'])) {
            update_post_meta($product_id, '_cbd_mg', $data['mg_cbd']);
        }

        // Brand information
        if (isset($data['brand_name'])) {
            update_post_meta($product_id, '_brand_name', $data['brand_name']);
        }

        // Strain/Cultivar information
        if (isset($data['cultivar'])) {
            update_post_meta($product_id, '_cultivar', $data['cultivar']);
        }

        // Batch information
        if (isset($data['batch_number'])) {
            update_post_meta($product_id, '_batch_number', $data['batch_number']);
        }

        // COA URL
        if (isset($data['coa_url'])) {
            update_post_meta($product_id, '_coa_url', $data['coa_url']);
        }

        // Harvest date
        if (isset($data['harvest_date'])) {
            update_post_meta($product_id, '_harvest_date', $data['harvest_date']);
        }

        // Grower information
        if (isset($data['grower_name'])) {
            update_post_meta($product_id, '_grower_name', $data['grower_name']);
        }

        // Effects
        if (isset($data['effects'])) {
            if (is_array($data['effects'])) {
                $effects = array_keys(array_filter($data['effects']));
                update_post_meta($product_id, '_effects', implode(',', $effects));
            } else {
                update_post_meta($product_id, '_effects', $data['effects']);
            }
        }

        // Mood
        if (isset($data['mood'])) {
            if (is_array($data['mood'])) {
                $moods = array_keys(array_filter($data['mood']));
                update_post_meta($product_id, '_mood', implode(',', $moods));
            } else {
                update_post_meta($product_id, '_mood', $data['mood']);
            }
        }

        // Save all raw data as JSON for future reference
        update_post_meta($product_id, '_bakedbot_raw_data', json_encode($data));
    }

    /**
     * Create or update a product variation
     *
     * @param int   $product_id     Parent product ID.
     * @param array $variation_data Variation data from the API.
     */
    private function create_or_update_variation($product_id, $variation_data) {
        $variation_sku = $variation_data['cann_sku_id'];

        // Check if variation exists
        $variation_id = $this->get_variation_id_by_sku($product_id, $variation_sku);

        if ($variation_id) {
            $variation = new WC_Product_Variation($variation_id);
        } else {
            $variation = new WC_Product_Variation();
            $variation->set_parent_id($product_id);
            $variation->set_sku($variation_sku);
        }

        // Set variation attributes
        $attributes = array();

        if (!empty($variation_data['display_weight'])) {
            $attr_name = 'Weight';
            $taxonomy = wc_attribute_taxonomy_name($attr_name);
            $term = get_term_by('name', $variation_data['display_weight'], $taxonomy);
            if ($term) {
                $attributes[$taxonomy] = $term->slug;
            }
        }

        $variation->set_attributes($attributes);

        // Set price
        if (isset($variation_data['latest_price'])) {
            $variation->set_regular_price($variation_data['latest_price']);
        }

        $variation->set_manage_stock(false);
        $variation->set_stock_status('instock');

        $variation_id = $variation->save();

        // Set variation image
        if (!empty($variation_data['image_url'])) {
            $this->set_product_image($variation_id, $variation_data['image_url'], $variation_data['product_name']);
        }
    }

    /**
     * Create a product attribute if it doesn't exist
     *
     * @param string $name Attribute name.
     * @return string Taxonomy name.
     */
    private function create_attribute($name) {
        $attr_name = sanitize_title($name);
        $taxonomy = wc_attribute_taxonomy_name($attr_name);

        // Check if attribute already exists
        $attribute_id = wc_attribute_taxonomy_id_by_name($name);
        if (!$attribute_id) {
            $attribute_id = wc_create_attribute(array(
                'name'         => $name,
                'slug'         => $attr_name,
                'type'         => 'select',
                'order_by'     => 'menu_order',
                'has_archives' => false,
            ));
        }

        // Register taxonomy if not already registered
        if (!taxonomy_exists($taxonomy)) {
            register_taxonomy(
                $taxonomy,
                array('product'),
                array(
                    'hierarchical' => false,
                    'labels'       => array(
                        'name' => $name,
                    ),
                    'show_ui'      => false,
                    'query_var'    => true,
                    'rewrite'      => false,
                )
            );
        }

        return $taxonomy;
    }

    /**
     * Get term IDs for categories or tags
     *
     * @param array|string $terms    Terms to get IDs for.
     * @param string       $taxonomy Taxonomy name.
     * @return array Array of term IDs.
     */
    private function get_term_ids($terms, $taxonomy) {
        $term_ids = array();

        if (!is_array($terms)) {
            $terms = array($terms);
        }

        foreach ($terms as $term_name) {
            $term_name = sanitize_text_field($term_name);
            $term = term_exists($term_name, $taxonomy);
            if ($term) {
                $term_ids[] = $term['term_id'];
            } else {
                $new_term = wp_insert_term($term_name, $taxonomy);
                if (!is_wp_error($new_term)) {
                    $term_ids[] = $new_term['term_id'];
                }
            }
        }

        return $term_ids;
    }

    /**
     * Set product image
     *
     * @param int    $product_id Product or variation ID.
     * @param string $image_url  URL of the image.
     * @param string $title      Title for the image.
     */
    private function set_product_image($product_id, $image_url, $title) {
        $image_id = $this->get_image_id($image_url, $title);
        if ($image_id) {
            $product = wc_get_product($product_id);
            if ($product) {
                $product->set_image_id($image_id);
                $product->save();
            }
        }
    }
    
    /**
     * Upload image and return attachment ID
     *
     * @param string $image_url URL of the image to download.
     * @param string $title     Title for the attachment.
     * @return int|false Attachment ID on success, false on failure.
     */
    private function get_image_id($image_url, $title) {
        global $wpdb;

        // Clean the image URL by removing query parameters
        $clean_image_url = strtok($image_url, '?');
        
        // Check if an attachment with this image URL already exists
        $attachment_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta} WHERE meta_key = %s AND meta_value = %s LIMIT 1",
            '_bakedbot_original_image_url',
            $clean_image_url
        ));

        if ($attachment_id) {
            return $attachment_id;
        }

        // Proceed to download and insert the image
        if (!function_exists('download_url') || !function_exists('media_handle_sideload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }

        // Download the image using the cleaned URL
        $tmp = download_url($clean_image_url);
        if (is_wp_error($tmp)) {
            error_log('BakedBot Image Download Error for URL ' . $clean_image_url . ': ' . $tmp->get_error_message());
            return false;
        }

        // Prepare an array of file data
        $file_array = array(
            'name'     => basename($clean_image_url),
            'tmp_name' => $tmp,
        );

        // Do the validation and storage stuff
        $attachment_id = media_handle_sideload($file_array, 0, $title);

        // If error storing permanently, unlink and log the URL
        if (is_wp_error($attachment_id)) {
            @unlink($tmp);
            error_log('BakedBot Image Handle Error for URL ' . $clean_image_url . ': ' . $attachment_id->get_error_message());
            return false;
        }

        // Add the original image URL as metadata (using clean URL)
        update_post_meta($attachment_id, '_bakedbot_original_image_url', esc_url_raw($clean_image_url));

        return $attachment_id;
    }

    /**
     * Get variation ID by SKU
     *
     * @param int    $product_id Parent product ID.
     * @param string $sku        SKU of the variation.
     * @return int|false Variation ID or false if not found.
     */
    private function get_variation_id_by_sku($product_id, $sku) {
        global $wpdb;
        $variation_id = $wpdb->get_var($wpdb->prepare(
            "SELECT post_id FROM {$wpdb->postmeta}
            WHERE meta_key='_sku' AND meta_value=%s
            AND post_id IN (SELECT ID FROM {$wpdb->posts} WHERE post_parent=%d AND post_type='product_variation')",
            $sku,
            $product_id
        ));
        return $variation_id ? intval($variation_id) : false;
    }

    /**
     * Custom sanitize callback for settings to preserve settings from other tabs
     * 
     * @param array $input The submitted settings values
     * @return array The sanitized settings with all tabs preserved
     */
    public function sanitize_settings($input) {
        // Get the current complete set of options
        $current_options = get_option($this->option_name, array());
        
        // Merge the submitted options with the existing options to preserve all tabs
        $sanitized = wp_parse_args($input, $current_options);
        
        return $sanitized;
    }

    /**
     * AJAX handler to start the product export process
     */
    public function ajax_export_products() {
        error_log('BakedBot Export: Starting product export process');
        
        check_ajax_referer('bakedbot_export_nonce', 'security');

        if (!current_user_can('manage_options')) {
            error_log('BakedBot Export Error: Permission denied for user');
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }

        $api_key = $this->get_option('api_key', '');
        
        if (empty($api_key)) {
            error_log('BakedBot Export Error: API key is missing');
            wp_send_json_error(__('API key is missing.', 'bakedbot-chatbot'));
            return;
        }

        error_log('BakedBot Export: API key found, length: ' . strlen($api_key));

        // Count total products
        $total_products = $this->count_total_products();
        $batch_size = 20; // Number of products to process in each batch
        $total_batches = ceil($total_products / $batch_size);

        error_log("BakedBot Export: Found {$total_products} total products, will process in {$total_batches} batches of {$batch_size}");

        // Initialize export status
        update_option('bakedbot_export_status', array(
            'current_batch' => 1,
            'total_batches' => $total_batches,
            'processed_products' => 0,
            'total_products' => $total_products,
            'completed' => false,
            'error' => null
        ));

        error_log('BakedBot Export: Export status initialized');

        // Schedule the first export batch
        if (function_exists('as_enqueue_async_action')) {
            error_log('BakedBot Export: Using Action Scheduler for background processing');
            as_enqueue_async_action(
                'bakedbot_export_batch',
                array(1, $batch_size, $api_key),
                'bakedbot'
            );
            wp_send_json_success(__('Export started in background.', 'bakedbot-chatbot'));
        } else {
            error_log('BakedBot Export: Action Scheduler not available, processing directly');
            // If Action Scheduler is not available, start the export directly
            $this->export_batch(1, $batch_size, $api_key);
            wp_send_json_success(__('Export started.', 'bakedbot-chatbot'));
        }
    }

    /**
     * AJAX handler to get the export status
     */
    public function ajax_get_export_status() {
        check_ajax_referer('bakedbot_export_status_nonce', 'security');

        if (!current_user_can('manage_options')) {
            error_log('BakedBot Export Status: Permission denied for user');
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }

        $status = get_option('bakedbot_export_status', false);
        
        if ($status) {
            error_log('BakedBot Export Status: ' . wp_json_encode($status));
            wp_send_json_success(array('status' => $status));
        } else {
            error_log('BakedBot Export Status: No export in progress');
            wp_send_json_error(__('No export in progress.', 'bakedbot-chatbot'));
        }
    }

    /**
     * Count total products in WooCommerce
     * 
     * @return int Total number of products
     */
    private function count_total_products() {
        error_log("BakedBot Export: Counting total products...");
        
        $products_count = wp_count_posts('product');
        $simple_products = isset($products_count->publish) ? intval($products_count->publish) : 0;
        
        error_log("BakedBot Export: Found {$simple_products} published products via wp_count_posts");
        
        // Count variations
        $variations_count = wp_count_posts('product_variation');
        $variations = isset($variations_count->publish) ? intval($variations_count->publish) : 0;
        
        error_log("BakedBot Export: Found {$variations} published product variations via wp_count_posts");
        
        // Also check with wc_get_products to see if there's a discrepancy
        $export_statuses = $this->get_export_statuses();
        $wc_products = wc_get_products(array(
            'status' => $export_statuses,
            'limit' => -1,
            'return' => 'ids'
        ));
        
        error_log("BakedBot Export: Found " . count($wc_products) . " products via wc_get_products");
        error_log("BakedBot Export: WC Product IDs: " . implode(', ', array_slice($wc_products, 0, 10)) . (count($wc_products) > 10 ? '...' : ''));
        
        // Let's also check all post statuses to see what's happening
        $all_products = wp_count_posts('product');
        error_log("BakedBot Export: All product statuses: " . wp_json_encode($all_products));
        
        $total = $simple_products + $variations;
        error_log("BakedBot Export: Total products to export (wp_count_posts method): {$total}");
        error_log("BakedBot Export: Total products found (wc_get_products method): " . count($wc_products));
        
        // Use the WooCommerce method count since that's what we'll actually be retrieving
        return count($wc_products);
    }

    /**
     * Export a batch of products
     *
     * @param int    $batch_number    Current batch number
     * @param int    $batch_size      Size of each batch
     * @param string $api_key         API key for authentication
     * @param string $site_identifier Site identifier
     */
    public function export_batch($batch_number, $batch_size, $api_key) {
        error_log("BakedBot Export: Starting batch {$batch_number} with size {$batch_size}");
        
        try {
            // Get current export status
            $status = get_option('bakedbot_export_status', array(
                'current_batch' => $batch_number,
                'total_batches' => 0,
                'processed_products' => 0,
                'total_products' => 0,
                'completed' => false,
                'error' => null
            ));
            
            error_log("BakedBot Export: Current status - Batch {$status['current_batch']}/{$status['total_batches']}, Processed: {$status['processed_products']}/{$status['total_products']}");
            
            // Calculate offset
            $offset = ($batch_number - 1) * $batch_size;
            error_log("BakedBot Export: Fetching products with offset {$offset}");
            
            // Get products for this batch
            $products = $this->get_products_batch($offset, $batch_size);
            error_log("BakedBot Export: Found " . count($products) . " products in batch {$batch_number}");
            
            if (empty($products)) {
                error_log("BakedBot Export: No products found in batch {$batch_number}, marking as completed");
                $status['completed'] = true;
                update_option('bakedbot_export_status', $status);
                return;
            }
            
            // Log product IDs for debugging
            $product_ids = array_map(function($product) { return $product->get_id(); }, $products);
            error_log("BakedBot Export: Product IDs in batch {$batch_number}: " . implode(', ', $product_ids));
            
            // Format products for API - removed site_identifier
            $formatted_products = $this->format_products_for_api($products);
            error_log("BakedBot Export: Formatted " . count($formatted_products) . " products for API");
            
            // Log first product structure for debugging
            if (!empty($formatted_products)) {
                error_log("BakedBot Export: Sample product data: " . wp_json_encode(array_slice($formatted_products[0], 0, 5), JSON_UNESCAPED_SLASHES));
            }
            
            // Send to API
            error_log("BakedBot Export: Sending batch {$batch_number} to API");
            $response = $this->send_products_to_api($formatted_products, $api_key);
            
            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }
            
            error_log("BakedBot Export: Batch {$batch_number} sent successfully to API");
            
            // Update status
            $old_processed = $status['processed_products'];
            $status['processed_products'] += count($products);
            $status['current_batch'] = $batch_number;
            
            error_log("BakedBot Export: Progress update - Processed: {$old_processed} + " . count($products) . " = {$status['processed_products']} of {$status['total_products']}");
            
            // Check if this is the last batch
            if ($batch_number >= $status['total_batches']) {
                $status['completed'] = true;
                error_log("BakedBot Export: Batch {$batch_number} was the last batch, export completed");
            } else {
                error_log("BakedBot Export: Batch {$batch_number} completed, {$status['total_batches']} total batches planned");
            }
            
            update_option('bakedbot_export_status', $status);
            error_log("BakedBot Export: Updated status after batch {$batch_number}: " . wp_json_encode($status));
            
            // Schedule next batch if not completed
            if (!$status['completed'] && function_exists('as_enqueue_async_action')) {
                error_log("BakedBot Export: Scheduling next batch " . ($batch_number + 1));
                as_enqueue_async_action(
                    'bakedbot_export_batch',
                    array($batch_number + 1, $batch_size, $api_key),
                    'bakedbot'
                );
            } else if (!$status['completed']) {
                error_log("BakedBot Export: Continuing to next batch " . ($batch_number + 1) . " directly");
                // If Action Scheduler is not available, continue directly
                $this->export_batch($batch_number + 1, $batch_size, $api_key);
            }
            
        } catch (Exception $e) {
            // Update status with error
            $status = get_option('bakedbot_export_status', array());
            $status['error'] = 'Batch ' . $batch_number . ' error: ' . $e->getMessage();
            $status['completed'] = true;
            update_option('bakedbot_export_status', $status);
            
            // Log error with more context
            error_log('BakedBot Export Error on batch ' . $batch_number . ': ' . $e->getMessage());
            error_log('BakedBot Export Error trace: ' . $e->getTraceAsString());
        }
    }

    /**
     * Get a batch of products from WooCommerce
     *
     * @param int $offset     Offset to start from
     * @param int $batch_size Number of products to get
     * @return array Array of WC_Product objects
     */
    private function get_products_batch($offset, $batch_size) {
        // Determine which product statuses to include
        $export_statuses = $this->get_export_statuses();
        
        $args = array(
            'status' => $export_statuses,
            'limit' => $batch_size,
            'offset' => $offset,
            'orderby' => 'ID',
            'order' => 'ASC',
            'return' => 'objects',
        );
        
        error_log("BakedBot Export: Getting products with args: " . wp_json_encode($args));
        
        $products = wc_get_products($args);
        
        error_log("BakedBot Export: Retrieved " . count($products) . " products from WooCommerce");
        
        if (!empty($products)) {
            $product_ids = array_map(function($product) { return $product->get_id(); }, $products);
            error_log("BakedBot Export: Batch product IDs: " . implode(', ', $product_ids));
        } else {
            error_log("BakedBot Export: No products found in this batch (offset: {$offset}, limit: {$batch_size})");
        }
        
        return $products;
    }

    /**
     * Get the product statuses to include in the export
     * 
     * @return array|string Array of statuses or single status
     */
    private function get_export_statuses() {
        // Get the export status setting from options
        $export_status_setting = $this->get_option('export_product_statuses', 'publish_only');
        
        switch ($export_status_setting) {
            case 'all':
                $statuses = array('publish', 'draft', 'pending', 'private');
                error_log("BakedBot Export: Including ALL product statuses: " . implode(', ', $statuses));
                return $statuses;
                
            case 'publish_and_draft':
                $statuses = array('publish', 'draft');
                error_log("BakedBot Export: Including published and draft products: " . implode(', ', $statuses));
                return $statuses;
                
            case 'publish_only':
            default:
                error_log("BakedBot Export: Including ONLY published products");
                return 'publish';
        }
    }

    /**
     * Format products for the API
     *
     * @param array  $products  Array of WC_Product objects
     * @return array Formatted products ready for API
     */
    private function format_products_for_api($products) {
        error_log("BakedBot Export: Starting to format " . count($products) . " products for API");
        $formatted_products = array();
        $skipped_products = 0;
        
        foreach ($products as $product) {
            $product_id = $product->get_id();
            $product_type = $product->get_type();
            
            error_log("BakedBot Export: Processing product ID {$product_id}, type: {$product_type}");
            
            // Skip if not a simple or variable product
            if (!in_array($product_type, array('simple', 'variable'))) {
                error_log("BakedBot Export: Skipping product ID {$product_id} - unsupported type: {$product_type}");
                $skipped_products++;
                continue;
            }
            
            // Base product data
            $product_data = array(
                'product_id' => (string)$product_id,
                'meta_sku' => $product->get_sku() ?: 'wc-' . $product_id, // Ensure there's always a meta_sku
                'external_id' => (string)$product_id,
                'raw_product_name' => $product->get_name(),
                'product_name' => $product->get_name(),
                'product_description' => $product->get_description(),
                'product_short_description' => $product->get_short_description(),
                'url' => get_permalink($product_id),
                'source' => 'woocommerce',
                'medical' => true,
                'recreational' => true,
                'is_active' => ($product->get_status() === 'publish'), // Boolean for easy filtering
                'data' => json_encode(array(
                    'woocommerce_id' => $product_id,
                    'regular_price' => $product->get_regular_price(),
                    'sale_price' => $product->get_sale_price(),
                    'total_sales' => $product->get_total_sales(),
                    'stock_status' => $product->get_stock_status(),
                    'manage_stock' => $product->get_manage_stock(),
                    'stock_quantity' => $product->get_stock_quantity(),
                    'weight' => $product->get_weight(),
                    'dimensions' => array(
                        'length' => $product->get_length(),
                        'width' => $product->get_width(),
                        'height' => $product->get_height(),
                    ),
                    'shipping_class' => $product->get_shipping_class(),
                    'tax_status' => $product->get_tax_status(),
                    'tax_class' => $product->get_tax_class(),
                    'backorders' => $product->get_backorders(),
                    'purchase_note' => $product->get_purchase_note(),
                    'status' => $product->get_status(),
                ))
            );
            
            // Add image URL if available
            $image_id = $product->get_image_id();
            if ($image_id) {
                $image_url = wp_get_attachment_image_url($image_id, 'full');
                if ($image_url) {
                    $product_data['image_url'] = $image_url;
                }
            }
            
            // Add gallery images
            $gallery_image_ids = $product->get_gallery_image_ids();
            if (!empty($gallery_image_ids)) {
                $gallery_urls = array();
                foreach ($gallery_image_ids as $img_id) {
                    $gallery_url = wp_get_attachment_image_url($img_id, 'full');
                    if ($gallery_url) {
                        $gallery_urls[] = $gallery_url;
                    }
                }
                if (!empty($gallery_urls)) {
                    $product_data['images_urls'] = implode(',', $gallery_urls);
                }
            }
            
            // Add categories
            $categories = wp_get_post_terms($product_id, 'product_cat', array('fields' => 'names'));
            if (!is_wp_error($categories) && !empty($categories)) {
                $product_data['raw_product_category'] = $categories[0];
                $product_data['category'] = $categories[0];
                
                // If more than one category, use second as subcategory
                if (count($categories) > 1) {
                    $product_data['raw_subcategory'] = $categories[1];
                    $product_data['subcategory'] = $categories[1];
                }
            }
            
            // Add tags
            $tags = wp_get_post_terms($product_id, 'product_tag', array('fields' => 'names'));
            if (!is_wp_error($tags) && !empty($tags)) {
                $product_data['product_tags'] = $tags; // Send as array, not JSON string
            }
            
            // Add price
            $price = $product->get_price();
            if ($price) {
                $product_data['latest_price'] = (float)$price;
                $product_data['retail_price'] = (float)$price;
                
                // If regular and sale prices are available, use them for wholesale and retail
                $regular_price = $product->get_regular_price();
                $sale_price = $product->get_sale_price();
                
                if (!empty($regular_price)) {
                    $product_data['msrp'] = (float)$regular_price;
                    
                    if (!empty($sale_price)) {
                        $product_data['wholesale_price'] = (float)$sale_price;
                        // Calculate profit margin if both prices are available
                        if ($regular_price > 0) {
                            $margin = (($regular_price - $sale_price) / $regular_price) * 100;
                            $product_data['profit_margin'] = round($margin, 2);
                        }
                    }
                }
            }
            
            // For variable products, handle variations
            if ($product_type === 'variable') {
                $variants = array();
                $variations = $product->get_available_variations();
                
                foreach ($variations as $variation_data) {
                    $variation_id = $variation_data['variation_id'];
                    $variation = wc_get_product($variation_id);
                    
                    if (!$variation) {
                        continue;
                    }
                    
                    $variation_attributes = $variation->get_variation_attributes();
                    $weight = '';
                    
                    // Try to extract weight from attributes
                    foreach ($variation_attributes as $attr_name => $attr_value) {
                        if (strpos(strtolower($attr_name), 'weight') !== false) {
                            $weight = $attr_value;
                            break;
                        }
                    }
                    
                    $variant = array(
                        'cann_sku_id' => $variation->get_sku() ?: 'wc-var-' . $variation_id,
                        'external_id' => (string)$variation_id,
                        'display_weight' => $weight,
                        'latest_price' => (float)$variation->get_price(),
                        'retail_price' => (float)$variation->get_regular_price(),
                        'wholesale_price' => (float)$variation->get_sale_price(),
                        'attributes' => $variation_attributes
                    );
                    
                    // Add variation image
                    $var_image_id = $variation_data['image_id'];
                    if ($var_image_id) {
                        $var_image_url = wp_get_attachment_image_url($var_image_id, 'full');
                        if ($var_image_url) {
                            $variant['image_url'] = $var_image_url;
                        }
                    }
                    
                    $variants[] = $variant;
                }
                
                if (!empty($variants)) {
                    $product_data['variants'] = $variants; // Send as array, not JSON string
                }
            }
            
            // Extract Cannabis-specific metadata (common custom fields)
            $this->extract_cannabis_metadata($product_id, $product_data);
            
            // Add slug for URL generation
            $product_data['slug'] = $product->get_slug();
            
            // Add to formatted products array
            $formatted_products[] = $product_data;
            error_log("BakedBot Export: Successfully formatted product ID {$product_id}");
        }
        
        error_log("BakedBot Export: Finished formatting products. Total: " . count($formatted_products) . ", Skipped: {$skipped_products}");
        
        return $formatted_products;
    }

    /**
     * Extract cannabis-specific metadata from product custom fields
     *
     * @param int   $product_id   Product ID
     * @param array &$product_data Product data reference to add metadata to
     */
    private function extract_cannabis_metadata($product_id, &$product_data) {
        // Common cannabis metadata fields
        $meta_mappings = array(
            // THC content
            '_thc_content' => array('percentage_thc', 'thc'),
            '_thc_percentage' => array('percentage_thc', 'thc'),
            '_thc' => array('percentage_thc', 'thc'),
            '_thc_mg' => array('mg_thc', 'thc_mg'),
            '_thc_milligrams' => array('mg_thc', 'thc_mg'),
            
            // CBD content
            '_cbd_content' => array('percentage_cbd', 'cbd'),
            '_cbd_percentage' => array('percentage_cbd', 'cbd'),
            '_cbd' => array('percentage_cbd', 'cbd'),
            '_cbd_mg' => array('mg_cbd', 'cbd_mg'),
            '_cbd_milligrams' => array('mg_cbd', 'cbd_mg'),
            
            // Brand information
            '_brand' => array('brand_name'),
            '_brand_name' => array('brand_name'),
            'brand' => array('brand_name'),
            
            // Strain/Cultivar information
            '_strain' => array('cultivar'),
            '_cultivar' => array('cultivar'),
            'strain' => array('cultivar'),
            
            // Batch information
            '_batch_number' => array('batch_number'),
            'batch_number' => array('batch_number'),
            'batch' => array('batch_number'),
            
            // COA (Certificate of Analysis) URL
            '_coa_url' => array('coa_url'),
            '_lab_results' => array('coa_url'),
            'coa_url' => array('coa_url'),
            
            // Harvest date
            '_harvest_date' => array('harvest_date'),
            'harvest_date' => array('harvest_date'),
            
            // Grower information
            '_grower' => array('grower_name'),
            '_producer' => array('grower_name'),
            'grower' => array('grower_name'),
            
            // Effects and other attributes that might be stored as comma-separated lists
            '_effects' => array('effects'),
            'effects' => array('effects'),
            
            // Mood information
            '_mood' => array('mood'),
            'mood' => array('mood')
        );
        
        // Process each potential custom field
        foreach ($meta_mappings as $meta_key => $target_fields) {
            $meta_value = get_post_meta($product_id, $meta_key, true);
            
            if (!empty($meta_value)) {
                // Convert to appropriate data type based on field
                if (in_array('percentage_thc', $target_fields) || 
                    in_array('percentage_cbd', $target_fields) || 
                    in_array('thc', $target_fields) || 
                    in_array('cbd', $target_fields)) {
                    // Convert to float for numeric fields
                    $meta_value = (float)str_replace('%', '', $meta_value);
                } elseif (in_array('mg_thc', $target_fields) || 
                         in_array('mg_cbd', $target_fields) || 
                         in_array('thc_mg', $target_fields) || 
                         in_array('cbd_mg', $target_fields)) {
                    // Convert to float for milligram fields
                    $meta_value = (float)str_replace(array('mg', 'MG', 'Mg'), '', $meta_value);
                } elseif (in_array('harvest_date', $target_fields)) {
                    // Format date for harvest_date
                    if (is_numeric($meta_value)) {
                        // If it's a timestamp
                        $meta_value = date('Y-m-d\TH:i:s\Z', $meta_value);
                    } else {
                        // Try to parse string date
                        $date = strtotime($meta_value);
                        if ($date !== false) {
                            $meta_value = date('Y-m-d\TH:i:s\Z', $date);
                        }
                    }
                } elseif (in_array('effects', $target_fields) || in_array('mood', $target_fields)) {
                    // Convert comma-separated strings to arrays
                    if (is_string($meta_value) && strpos($meta_value, ',') !== false) {
                        $meta_value = array_map('trim', explode(',', $meta_value));
                    }
                    // Convert array to JSON object
                    if (is_array($meta_value)) {
                        $meta_value = array_combine($meta_value, array_fill(0, count($meta_value), true));
                    }
                }
                
                // Add value to all target fields
                foreach ($target_fields as $target_field) {
                    $product_data[$target_field] = $meta_value;
                }
            }
        }
    }

    /**
     * Send formatted products to the BakedBot API
     *
     * @param array  $products Array of formatted products
     * @param string $api_key  API key for authentication
     * @return array|WP_Error API response or WP_Error on failure
     */
    private function send_products_to_api($products, $api_key) {
        error_log("BakedBot Export: Starting API call with " . count($products) . " products");
        
        if (empty($products)) {
            error_log("BakedBot Export Error: No products to send to API");
            return new WP_Error('no_products', __('No products to send', 'bakedbot-chatbot'));
        }
        
        // Change API endpoint to match Node.js controller path
        $api_base_url = trailingslashit($this->options['bakedbot_api_url']);
        $api_url = $api_base_url . 'public/products/upload';
        
        error_log("BakedBot Export: API URL: {$api_url}");
        error_log("BakedBot Export: API base URL from options: " . $this->options['bakedbot_api_url']);
        
        // Format request according to Node.js expectations
        $request_data = array(
            'product_data' => $products,
            'reindex' => true,
            'enhance_with_ai' => false
        );
        
        error_log("BakedBot Export: Request data size: " . strlen(json_encode($request_data)) . " bytes");
        
        $args = array(
            'body' => json_encode($request_data),
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $api_key  // Changed from X-Token to Bearer token
            ),
            'timeout' => 120, // Increased timeout for potentially large data sets
        );
        
        // Always log request details (enhanced from debug mode only)
        error_log('BakedBot Export API Request Details: ' . wp_json_encode(array(
            'url' => $api_url,
            'method' => 'POST',
            'headers' => array(
                'Content-Type' => $args['headers']['Content-Type'],
                'Authorization' => 'Bearer ' . substr($api_key, 0, 10) . '...' // Only show first 10 chars
            ),
            'product_count' => count($products),
            'timeout' => $args['timeout'],
            'body_size_bytes' => strlen($args['body'])
        )));
        
        error_log("BakedBot Export: Making HTTP request to API...");
        $response = wp_remote_post($api_url, $args);
        
        if (is_wp_error($response)) {
            error_log("BakedBot Export Error: HTTP request failed - " . $response->get_error_message());
            return $response;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_headers = wp_remote_retrieve_headers($response);
        
        // Always log response details (enhanced from debug mode only)
        error_log('BakedBot Export API Response Details: ' . wp_json_encode(array(
            'code' => $response_code,
            'body_length' => strlen($response_body),
            'content_type' => isset($response_headers['content-type']) ? $response_headers['content-type'] : 'unknown',
            'body_preview' => substr($response_body, 0, 200) // First 200 chars of response
        )));
        
        if ($response_code < 200 || $response_code >= 300) {
            error_log("BakedBot Export Error: API returned error code {$response_code}");
            error_log("BakedBot Export Error: Full response body: {$response_body}");
            return new WP_Error(
                'api_error',
                sprintf(
                    __('API returned error code %d: %s', 'bakedbot-chatbot'),
                    $response_code,
                    $response_body
                )
            );
        }
        
        error_log("BakedBot Export: API call successful");
        
        $decoded_response = json_decode($response_body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("BakedBot Export Warning: Failed to decode JSON response - " . json_last_error_msg());
            error_log("BakedBot Export Warning: Raw response body: {$response_body}");
        }
        
        return $decoded_response;
    }

    /**
     * Process products from the API to match our expected format for importing
     *
     * @param array $products Products from the API
     * @return array Processed products ready for importing
     */
    private function process_products_for_import($products) {
        $processed_products = array();
        
        foreach ($products as $product) {
            // If there are variants, create a meta_sku group
            if (!empty($product['variants']) && is_array($product['variants'])) {
                $meta_sku = !empty($product['meta_sku']) ? $product['meta_sku'] : $product['product_id'];
                
                $variant_products = array();
                // Add the main product as the first variation
                $main_product = $product;
                // Remove variants to avoid recursion
                unset($main_product['variants']);
                $variant_products[] = $main_product;
                
                // Add each variant as a separate product in the group
                foreach ($product['variants'] as $variant) {
                    // Merge variant data with base product data for complete product
                    $variant_product = array_merge($main_product, $variant);
                    // Ensure variant has a unique SKU
                    if (empty($variant_product['cann_sku_id'])) {
                        $variant_product['cann_sku_id'] = $meta_sku . '-' . (isset($variant['display_weight']) ? sanitize_title($variant['display_weight']) : mt_rand(1000, 9999));
                    }
                    $variant_products[] = $variant_product;
                }
                
                // Create the meta_sku group with all variations
                $processed_products[] = array(
                    'meta_sku' => $meta_sku,
                    'products' => $variant_products
                );
            } else {
                // For products without variants, create a simple meta_sku group with just the one product
                $meta_sku = !empty($product['meta_sku']) ? $product['meta_sku'] : $product['product_id'];
                $processed_products[] = array(
                    'meta_sku' => $meta_sku,
                    'products' => array($product)
                );
            }
        }
        
        return $processed_products;
    }

    public function conditionally_add_chatbot_container() {
        global $post;
        $should_render = false;

        // Check if the post content contains the shortcode first
        if (is_singular() && $post && has_shortcode($post->post_content, 'bakedbot_chatbot')) {
            // Don't auto-render if shortcode is present - let the shortcode handle it
            $this->shortcode_used = true;
            return;
        }

        // Condition 1: Check if we are on the front page and front page display is enabled.
        if (is_front_page() && $this->get_option('show_on_frontpage', true)) {
            $should_render = true;
        }

        // Render the chatbot if condition is met and it hasn't been rendered yet.
        if ($should_render && !$this->chatbot_rendered) {
            $position = $this->get_option('position', 'right');
            $unique_id = 'bakedbot-widget-root-' . uniqid();

            echo '<div id="' . esc_attr($unique_id) . '" class="bakedbot-widget-container bakedbot-position-' . esc_attr($position) . '"></div>';

            $init_script = "
                function tryInitializeWidget() {
                    try {
                        if (window.BakedBotInitialize) {
                            window.BakedBotInitialize('" . esc_js($unique_id) . "');
                            return true;
                        }
                        return false;
                    } catch (e) {
                        console.error('BakedBot Debug: Error during initialization:', e);
                        return false;
                    }
                }
                if (document.readyState === 'complete' || document.readyState === 'interactive') {
                    tryInitializeWidget();
                } else {
                    document.addEventListener('DOMContentLoaded', tryInitializeWidget);
                }
            ";

            echo '<script>' . $init_script . '</script>';
            echo '<script>' . $this->get_config_script(['container_id' => $unique_id]) . '</script>';
            echo '<script>' . $this->get_logged_in_user_script() . '</script>';
            echo '<script>' . $this->get_theme_script() . '</script>';

            $this->chatbot_rendered = true; // Mark as rendered
        }
    }
    
    public function add_enhancement_functions() {
        // Add global enhancement function for progressive enhancement
        echo '<script>
            window.enhanceBakedBotMenu = function(containerId) {
                const container = document.getElementById(containerId);
                if (!container) {
                    console.error("BakedBot: Container not found:", containerId);
                    return;
                }
                
                console.log("BakedBot Debug: Enhancing menu with ID:", containerId);
                
                // Get config
                const config = window.BakedBotMenuConfig || {};
                console.log("BakedBot Debug: Menu config:", config);
                
                // Check if required dependencies are available
                if (typeof React === "undefined") {
                    console.error("BakedBot: React not loaded");
                    return;
                }
                
                if (typeof ReactDOM === "undefined") {
                    console.error("BakedBot: ReactDOM not loaded");
                    return;
                }
                
                if (!window.BakedBotMenu) {
                    console.error("BakedBot: BakedBotMenu component not found");
                    return;
                }
                
                try {
                    // Import and enhance with React
                    const props = {
                        ...config,
                        isProgressiveEnhancement: true,
                        containerId: containerId,
                        initialProducts: config.initialProducts || []
                    };
                    
                    console.log("BakedBot Debug: Creating menu with props:", props);
                    
                    // Create HeadlessMenu wrapped with CartProvider
                    const menuElement = React.createElement(
                        window.CartProvider || React.Fragment,
                        {},
                        React.createElement(window.BakedBotMenu, props)
                    );
                    
                    // Enhance the existing HTML
                    const root = ReactDOM.createRoot ? ReactDOM.createRoot(container) : null;
                    if (root) {
                        root.render(menuElement);
                        console.log("BakedBot Debug: Menu enhanced successfully with createRoot");
                    } else {
                        ReactDOM.render(menuElement, container);
                        console.log("BakedBot Debug: Menu enhanced successfully with legacy render");
                    }
                } catch (error) {
                    console.error("BakedBot Debug: Error enhancing menu:", error);
                }
            };
        </script>';
    }

    public function ajax_remove_from_pages() {
        check_ajax_referer('bakedbot_remove_from_pages_nonce', 'security');

        if (!current_user_can('edit_pages')) {
            wp_send_json_error(__('You do not have permission to edit pages.', 'bakedbot-chatbot'));
        }

        if (empty($_POST['page_ids'])) {
            wp_send_json_error(__('No pages selected.', 'bakedbot-chatbot'));
        }

        $page_ids = array_map('sanitize_text_field', $_POST['page_ids']);
        $updated_count = 0;
        $frontpage_toggled = false;

        foreach ($page_ids as $page_id) {
            if ($page_id === 'frontpage') {
                // Handle front page toggle
                $current_options = get_option($this->option_name, array());
                $current_options['show_on_frontpage'] = false;
                update_option($this->option_name, $current_options);
                $frontpage_toggled = true;
                $updated_count++;
            } else {
                $page = get_post(intval($page_id));
                if ($page) {
                    $original_content = $page->post_content;
                    // Remove [bakedbot_chatbot] and [bakedbot_menu] shortcodes
                    $new_content = preg_replace('/\[bakedbot_(chatbot|menu).*?\]/s', '', $original_content);
                    // Clean up any extra whitespace/newlines left behind
                    $new_content = trim($new_content);

                    if ($original_content !== $new_content) {
                        wp_update_post(array(
                            'ID' => intval($page_id),
                            'post_content' => $new_content
                        ));
                        $updated_count++;
                    }
                }
            }
        }

        $message = sprintf(
            _n(
                'Chatbot removed from %d page.',
                'Chatbot removed from %d pages.',
                $updated_count,
                'bakedbot-chatbot'
            ),
            $updated_count
        );

        if ($frontpage_toggled) {
            $message .= ' ' . __('Front page chatbot disabled.', 'bakedbot-chatbot');
        }

        wp_send_json_success(array('message' => $message));
    }
    
    public function ajax_toggle_frontpage() {
        check_ajax_referer('bakedbot_toggle_frontpage_nonce', 'security');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Permission denied.', 'bakedbot-chatbot'));
            return;
        }

        $enable = isset($_POST['enable']) ? (bool) $_POST['enable'] : false;
        
        $current_options = get_option($this->option_name, array());
        $current_options['show_on_frontpage'] = $enable;
        update_option($this->option_name, $current_options);

        $message = $enable ? 
            __('Front page chatbot enabled.', 'bakedbot-chatbot') : 
            __('Front page chatbot disabled.', 'bakedbot-chatbot');

        wp_send_json_success(array('message' => $message));
    }
    
    /**
     * Add full-width styles to the page head when needed
     */
    public function add_full_width_styles() {
        global $post;
        
        if (!is_singular('page') || !$post) {
            return;
        }
        
        $is_full_width = get_post_meta($post->ID, '_bakedbot_full_width', true);
        
        if ($is_full_width) {
            ?>
            <style id="bakedbot-full-width-styles">
                /* BakedBot Full-Width Page Styles */
                .bakedbot-full-width .site-content,
                .bakedbot-full-width .entry-content,
                .bakedbot-full-width .page-content,
                .bakedbot-full-width .post-content,
                .bakedbot-full-width .content-area,
                .bakedbot-full-width main,
                .bakedbot-full-width article {
                    max-width: none !important;
                    width: 100% !important;
                    margin-left: 0 !important;
                    margin-right: 0 !important;
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }
                
                /* Common theme container overrides */
                .bakedbot-full-width .container,
                .bakedbot-full-width .content-container,
                .bakedbot-full-width .site-main,
                .bakedbot-full-width .main-content,
                .bakedbot-full-width .primary-content,
                .bakedbot-full-width .entry-wrapper,
                .bakedbot-full-width .page-wrapper {
                    max-width: none !important;
                    width: 100% !important;
                    margin: 0 !important;
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }
                
                /* Override common theme frameworks */
                .bakedbot-full-width .wrap,
                .bakedbot-full-width .wrapper,
                .bakedbot-full-width .inner,
                .bakedbot-full-width .row,
                .bakedbot-full-width .col,
                .bakedbot-full-width .grid-container {
                    max-width: none !important;
                    width: 100% !important;
                    margin: 0 !important;
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }
                
                /* BakedBot menu and homepage specific styles */
                .bakedbot-full-width .headless-menu,
                .bakedbot-full-width .bakedbot-headless-menu,
                .bakedbot-full-width .headless-homepage,
                .bakedbot-full-width .bakedbot-headless-homepage {
                    width: 100vw !important;
                    max-width: 1400px !important;
                    margin-left: calc(-50vw + 50%) !important;
                    margin-right: calc(-50vw + 50%) !important;
                    position: relative !important;
                    box-sizing: border-box !important;
                    min-height: 100vh !important;
                    height: auto !important;
                    overflow: visible !important;
                }
                
                /* Center the components when viewport is wider than 1400px */
                @media (min-width: 1401px) {
                    .bakedbot-full-width .headless-menu,
                    .bakedbot-full-width .bakedbot-headless-menu,
                    .bakedbot-full-width .headless-homepage,
                    .bakedbot-full-width .bakedbot-headless-homepage {
                        width: 1400px !important;
                        margin-left: calc(-700px + 50%) !important;
                        margin-right: calc(-700px + 50%) !important;
                    }
                }
                
                /* Ensure BakedBot content spans full width and height */
                .bakedbot-full-width .headless-menu .menu-content,
                .bakedbot-full-width .headless-homepage .homepage-main {
                    width: 100% !important;
                    max-width: none !important;
                    padding-left: 20px !important;
                    padding-right: 20px !important;
                    box-sizing: border-box !important;
                    min-height: calc(100vh - 100px) !important;
                    height: auto !important;
                    overflow: visible !important;
                }
                
                /* Homepage hero section should span full width */
                .bakedbot-full-width .headless-homepage .hero-section {
                    width: 100% !important;
                    max-width: none !important;
                    margin-left: 0 !important;
                    margin-right: 0 !important;
                    padding-left: 20px !important;
                    padding-right: 20px !important;
                    box-sizing: border-box !important;
                }
                
                /* Ensure homepage sections work with full width */
                .bakedbot-full-width .headless-homepage section {
                    width: 100% !important;
                    max-width: none !important;
                    padding-left: 20px !important;
                    padding-right: 20px !important;
                    box-sizing: border-box !important;
                }
                
                /* Remove any fixed heights and internal scrolling */
                .bakedbot-full-width .headless-menu,
                .bakedbot-full-width .headless-homepage,
                .bakedbot-full-width .bakedbot-headless-menu,
                .bakedbot-full-width .bakedbot-headless-homepage {
                    overflow-y: visible !important;
                    overflow-x: hidden !important;
                    max-height: none !important;
                }
                
                /* Ensure product grids and sections don't have independent scrolling */
                .bakedbot-full-width .products-area,
                .bakedbot-full-width .products-grid,
                .bakedbot-full-width .homepage-main,
                .bakedbot-full-width .categories-section,
                .bakedbot-full-width .featured-section {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Remove any constraining heights from filters */
                .bakedbot-full-width .filters-column,
                .bakedbot-full-width .filters-content,
                .bakedbot-full-width .filters-scroll-area {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Fix for potential sidebar issues */
                .bakedbot-full-width .sidebar,
                .bakedbot-full-width .widget-area,
                .bakedbot-full-width .secondary {
                    display: none !important;
                }
                
                /* Override any theme grid layouts that might constrain width */
                .bakedbot-full-width {
                    display: block !important;
                }
                
                /* Responsive considerations */
                @media (max-width: 768px) {
                    .bakedbot-full-width .headless-menu .menu-content,
                    .bakedbot-full-width .headless-homepage .homepage-main {
                        padding-left: 10px !important;
                        padding-right: 10px !important;
                        min-height: calc(100vh - 60px) !important;
                    }
                    
                    .bakedbot-full-width .headless-homepage .hero-section,
                    .bakedbot-full-width .headless-homepage section {
                        padding-left: 10px !important;
                        padding-right: 10px !important;
                    }
                    
                    /* Ensure mobile doesn't have independent scrolling */
                    .headless-menu,
                    .headless-homepage,
                    .bakedbot-headless-menu,
                    .bakedbot-headless-homepage {
                        height: auto !important;
                        min-height: auto !important;
                        overflow: visible !important;
                    }
                }
                
                /* Theme-specific overrides for popular themes */
                
                /* Astra Theme */
                .bakedbot-full-width.ast-page-builder-template .site-content > .ast-container,
                .bakedbot-full-width .ast-separate-container .ast-article-post,
                .bakedbot-full-width .ast-separate-container .ast-article-single {
                    max-width: none !important;
                    padding: 0 !important;
                }
                
                /* GeneratePress Theme */
                .bakedbot-full-width .site-content .content-area {
                    width: 100% !important;
                    float: none !important;
                }
                
                /* Kadence Theme */
                .bakedbot-full-width .content-container .entry-content .alignfull {
                    width: 100vw !important;
                    margin-left: calc(-50vw + 50%) !important;
                }
                
                /* OceanWP Theme */
                .bakedbot-full-width #main #content-wrap,
                .bakedbot-full-width .container {
                    max-width: none !important;
                    width: 100% !important;
                    padding: 0 !important;
                }
                
                /* Twenty Twenty-One and similar block themes */
                .bakedbot-full-width .wp-site-blocks,
                .bakedbot-full-width .wp-block-group {
                    padding-left: 0 !important;
                    padding-right: 0 !important;
                }
                
                /* Global BakedBot components - remove independent scrolling for ALL instances */
                .headless-menu,
                .bakedbot-headless-menu,
                .headless-homepage,
                .bakedbot-headless-homepage {
                    height: auto !important;
                    min-height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    position: relative !important;
                    max-width: 1400px !important;
                    margin: 0 auto !important;
                }
                
                /* Remove scrolling from main content areas */
                .headless-menu .menu-content,
                .headless-homepage .homepage-main,
                .bakedbot-headless-menu .menu-content,
                .bakedbot-headless-homepage .homepage-main {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Product areas should not scroll independently */
                .headless-menu .products-area,
                .headless-menu .products-grid,
                .headless-homepage .categories-section,
                .headless-homepage .featured-section,
                .headless-homepage .category-products-section {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Filter areas should not have fixed heights */
                .headless-menu .filters-column,
                .headless-menu .filters-content,
                .headless-menu .filters-scroll-area {
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                }
                
                /* Horizontal scrolling containers are allowed, but no vertical constraints */
                .headless-homepage .products-scroll,
                .headless-homepage .categories-scroll,
                .headless-homepage .brands-scroll {
                    height: auto !important;
                    max-height: none !important;
                    overflow-y: visible !important;
                    overflow-x: auto !important;
                }
                
                /* Remove any viewport height constraints */
                .headless-menu[style*="height"],
                .headless-homepage[style*="height"],
                .bakedbot-headless-menu[style*="height"],
                .bakedbot-headless-homepage[style*="height"] {
                    height: auto !important;
                    min-height: auto !important;
                }
            </style>
            <?php
        }
    }
    
    /**
     * Add full-width body class when needed
     */
    public function add_full_width_body_class($classes) {
        global $post;
        
        if (is_singular('page') && $post) {
            $is_full_width = get_post_meta($post->ID, '_bakedbot_full_width', true);
            
            if ($is_full_width) {
                $classes[] = 'bakedbot-full-width';
                $classes[] = 'bakedbot-page';
            }
        }
        
        return $classes;
    }
}

// Initialize the plugin
$bakedbot_chatbot = new BakedBotChatbot();

// Register activation and deactivation hooks
register_activation_hook(__FILE__, array('BakedBotChatbot', 'activate'));
register_deactivation_hook(__FILE__, array('BakedBotChatbot', 'deactivate'));

