/**
 * BakedBot WordPress Initialization Script
 * Simplified version with essential functionality
 */

(function() {
  console.log("BakedBot Init: Loading...");
  
  // Set up global config
  window.BakedBotConfig = window.BakedBotConfig || {};
  window.BakedBotConfig.embedded = true;
  window.BakedBotConfig.wpEmbedded = true;
  
  /**
   * Simple error handler for React Router issues
   */
  window.addEventListener('error', function(event) {
    if (event.message && (
      event.message.includes('Router') || 
      event.message.includes('invariant') ||
      event.message.includes('Cannot read properties of undefined')
    )) {
      console.log("BakedBot Init: Detected Router error, forcing embedded mode");
      window.BakedBotConfig.skipRouterCheck = true;
      window.BakedBotConfig.disableRouter = true;
    }
  });
  
  /**
   * Initialize a BakedBot widget in the specified container
   */
  function initializeWidget(containerId) {
    console.log("BakedBot Init: Initializing widget for container:", containerId);
    const container = document.getElementById(containerId);
    
    if (!container) {
      console.error("BakedBot Init: Container not found:", containerId);
      return false;
    }
    
    try {
      // Try the preferred initialization method
      if (window.NoRouterApp && window.ReactDOM && window.React) {
        const root = window.ReactDOM.createRoot(container);
        
        root.render(window.React.createElement(window.NoRouterApp, {
          config: window.BakedBotConfig || {},
          theme: window.BakedBotTheme || {},
          user: window.BakedBotUser || {}
        }));
        
        return true;
      } 
      // Fallback to legacy method
      else if (window.createBakedBotWidget) {
        window.createBakedBotWidget(containerId);
        return true;
      }
      else {
        console.error("BakedBot Init: No initialization method available");
        return false;
      }
    } catch (error) {
      console.error("BakedBot Init: Failed to initialize widget:", error);
      
      // Simple fallback for errors
      container.innerHTML = '<div style="padding: 20px; background: #22AD85; color: white; border-radius: 8px; text-align: center;">' +
        '<h3 style="margin: 0 0 10px;">BakedBot</h3>' +
        '<p style="margin: 0 0 15px;">Widget failed to load. Please try refreshing the page.</p>' +
        '<button style="background: #24504A; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;" ' +
        'onclick="location.reload()">Refresh</button></div>';
      
      return false;
    }
  }
  
  /**
   * Initialize all BakedBot containers on the page
   */
  function initializeAllContainers() {
    const containers = document.querySelectorAll('[id^="bakedbot-widget-root-"], [id^="bakedbot-chatbot-"]');
    
    if (containers.length > 0) {
      console.log(`BakedBot Init: Found ${containers.length} container(s)`);
      
      for (let i = 0; i < containers.length; i++) {
        initializeWidget(containers[i].id);
      }
    } else {
      console.log("BakedBot Init: No containers found");
    }
  }
  
  // Export minimal set of functions to global scope
  window.BakedBot = window.BakedBot || {};
  window.BakedBot.initialize = initializeWidget;
  window.BakedBot.initializeAll = initializeAllContainers;
  window.BakedBot.version = "1.0.3";
  
  // For backwards compatibility
  window.BakedBotInitializeAllContainers = initializeAllContainers;
  
  // Initialize when DOM is ready
  if (document.readyState === "complete" || document.readyState === "interactive") {
    setTimeout(initializeAllContainers, 100);
  } else {
    document.addEventListener("DOMContentLoaded", function() {
      setTimeout(initializeAllContainers, 100);
    });
  }
  
  console.log("BakedBot Init: Ready");
})();
