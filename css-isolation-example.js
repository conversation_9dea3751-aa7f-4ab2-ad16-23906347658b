/**
 * WordPress Plugin CSS Isolation Techniques
 * 
 * This file demonstrates approaches to isolate CSS in WordPress plugins
 * to prevent theme styles from overriding plugin styles.
 */

/**
 * 1. Enqueue styles with higher priority
 * 
 * In your plugin's register_assets function:
 */
function register_assets() {
    // Enqueue styles with very high priority (999)
    wp_enqueue_style(
        'bakedbot-chatbot-styles',
        plugin_dir_url(__FILE__) . 'dist/assets/styles.css',
        array(),
        $this->version,
        'all'
    );
    
    // Add inline CSS with !important rules for critical styles
    $critical_css = "
        #bakedbot-container * {
            box-sizing: border-box !important;
        }
        .bakedbot-widget {
            font-family: 'Roboto', sans-serif !important;
            font-size: 16px !important;
            line-height: 1.5 !important;
            color: #333333 !important;
        }
        .bakedbot-button {
            background-color: #22AD85 !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
        }
    ";
    wp_add_inline_style('bakedbot-chatbot-styles', $critical_css);
}

/**
 * 2. Use an iframe for complete isolation
 * 
 * This approach loads the chatbot in an iframe, completely isolating it from the parent page's CSS.
 */
function iframe_isolation_approach() {
    // In your shortcode or widget output:
    $output = '<div class="bakedbot-iframe-container">';
    $output .= '<iframe src="' . esc_url(admin_url('admin-ajax.php?action=bakedbot_iframe_content')) . '" ';
    $output .= 'style="width:100%; height:600px; border:none;" allowtransparency="true"></iframe>';
    $output .= '</div>';
    
    return $output;
}

/**
 * 3. Use Shadow DOM for modern browsers
 * 
 * This approach uses Shadow DOM to create an encapsulated DOM tree with isolated CSS.
 */
function shadow_dom_approach() {
    // JavaScript to create Shadow DOM
    $script = "
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.getElementById('bakedbot-container');
        if (container) {
            // Create shadow root
            const shadowRoot = container.attachShadow({mode: 'open'});
            
            // Add styles to shadow DOM
            const style = document.createElement('style');
            style.textContent = `
                .chat-widget {
                    font-family: 'Roboto', sans-serif;
                    font-size: 16px;
                    line-height: 1.5;
                    color: #333333;
                }
                .chat-button {
                    background-color: #22AD85;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                }
            `;
            shadowRoot.appendChild(style);
            
            // Add content to shadow DOM
            const chatWidget = document.createElement('div');
            chatWidget.className = 'chat-widget';
            chatWidget.innerHTML = `
                <div class='chat-header'>BakedBot Chat</div>
                <div class='chat-messages'></div>
                <div class='chat-input'>
                    <input type='text' placeholder='Type your message...' />
                    <button class='chat-button'>Send</button>
                </div>
            `;
            shadowRoot.appendChild(chatWidget);
            
            // Initialize chat functionality
            initializeChat(shadowRoot);
        }
    });
    ";
    
    // Output container and script
    $output = '<div id="bakedbot-container"></div>';
    $output .= '<script>' . $script . '</script>';
    
    return $output;
}

/**
 * 4. Use CSS reset within container
 * 
 * This approach applies a CSS reset only to elements within the chatbot container.
 */
function css_reset_approach() {
    // CSS reset for chatbot container
    $reset_css = "
        #bakedbot-container * {
            all: initial;
            box-sizing: border-box;
        }
        #bakedbot-container {
            font-family: 'Roboto', sans-serif;
            font-size: 16px;
            line-height: 1.5;
            color: #333333;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        #bakedbot-container div,
        #bakedbot-container span,
        #bakedbot-container button,
        #bakedbot-container input,
        #bakedbot-container textarea {
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            color: inherit;
        }
    ";
    
    wp_add_inline_style('bakedbot-chatbot-styles', $reset_css);
}

/**
 * 5. Use highly specific selectors
 * 
 * This approach uses very specific selectors to override theme styles.
 */
function specific_selectors_approach() {
    // Highly specific CSS
    $specific_css = "
        body #page #content #primary #main article .entry-content #bakedbot-container .bakedbot-button {
            background-color: #22AD85 !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            font-family: 'Roboto', sans-serif !important;
            font-size: 16px !important;
            line-height: 1.5 !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            box-shadow: none !important;
        }
    ";
    
    wp_add_inline_style('bakedbot-chatbot-styles', $specific_css);
}

/**
 * 6. Load styles in the footer
 * 
 * This approach loads styles in the footer to override any theme styles.
 */
function footer_styles_approach() {
    // In your plugin's constructor or init function:
    add_action('wp_footer', array($this, 'add_footer_styles'), 999);
    
    // Footer styles function
    function add_footer_styles() {
        echo '<style>
            #bakedbot-container .bakedbot-button {
                background-color: #22AD85 !important;
                color: white !important;
                border: none !important;
            }
        </style>';
    }
}

/**
 * 7. Use attribute selectors for extra specificity
 * 
 * This approach uses attribute selectors for extra specificity.
 */
function attribute_selectors_approach() {
    // Attribute selectors CSS
    $attribute_css = "
        div[id='bakedbot-container'] button[class='bakedbot-button'][data-role='send'] {
            background-color: #22AD85 !important;
            color: white !important;
            border: none !important;
        }
    ";
    
    wp_add_inline_style('bakedbot-chatbot-styles', $attribute_css);
}

/**
 * 8. Use CSS variables with !important
 * 
 * This approach uses CSS variables with !important to ensure consistent styling.
 */
function css_variables_approach() {
    // CSS variables
    $variables_css = "
        #bakedbot-container {
            --bakedbot-primary-color: #22AD85 !important;
            --bakedbot-text-color: #333333 !important;
            --bakedbot-font-family: 'Roboto', sans-serif !important;
            --bakedbot-font-size: 16px !important;
            --bakedbot-line-height: 1.5 !important;
            --bakedbot-border-radius: 4px !important;
        }
        #bakedbot-container .bakedbot-button {
            background-color: var(--bakedbot-primary-color) !important;
            color: white !important;
            font-family: var(--bakedbot-font-family) !important;
            font-size: var(--bakedbot-font-size) !important;
            line-height: var(--bakedbot-line-height) !important;
            border-radius: var(--bakedbot-border-radius) !important;
        }
    ";
    
    wp_add_inline_style('bakedbot-chatbot-styles', $variables_css);
}
