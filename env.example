# BakedBot Environment Variables Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# BAKEDBOT API CONFIGURATION
# ===========================================

# BakedBot API Base URL
VITE_BAKED_BOT_API=https://beta.bakedbot.ai/api

# BakedBot API Key (required for authentication)
VITE_BAKED_BOT_API_KEY=your-bakedbot-api-key-here

# ===========================================
# CHAT API CONFIGURATION
# ===========================================

# Chat API Base URL
VITE_BASE_URL=https://your-chat-api-domain.com

# ===========================================
# FIREBASE CONFIGURATION
# ===========================================

# Firebase API Key
VITE_FIREBASE_API_KEY=your-firebase-api-key

# Firebase Auth Domain
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com

# Firebase Project ID
VITE_FIREBASE_PROJECT_ID=your-project-id

# Firebase Storage Bucket
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com

# Firebase Messaging Sender ID
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789

# Firebase Measurement ID (for Analytics)
VITE_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX

# Firebase App ID
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# ===========================================
# OPTIONAL CONFIGURATION
# ===========================================

# Environment mode (development, production, etc.)
NODE_ENV=development

# Enable debug logging (true/false)
VITE_DEBUG=false 