{"name": "bakedbot", "private": true, "version": "1.5.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "tsc && vite build --mode production", "build:secure": "tsc && vite build --mode secure", "heroku-postbuild": "npm install --no-shrinkwrap --no-package-lock typescript @vitejs/plugin-react-swc vite terser && npm run build:prod", "analyze": "vite build --mode production --sourcemap", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "serve -s dist", "wp:prepare": "node scripts/prepare-wordpress-plugin.js", "wp:build": "npm run build && npm run wp:prepare", "wp:package": "cd wordpress-plugin && zip -r ../bakedbot-chatbot.zip .", "wp:package:secure": "cd wordpress-plugin-protected/yakpro-po/obfuscated && zip -r ../../../bakedbot-chatbot-secure.zip .", "wp:dev": "npm run build && npm run wp:prepare && npm run wp:package", "wp:prod": "npm run build:secure && npm run wp:prepare && npm run wp:package", "wp:prod:secure": "npm run build:secure && npm run wp:prepare && npm run php:protect && npm run wp:package:secure", "wp:secure": "npm run build:secure && npm run wp:prepare && npm run wp:package", "php:setup": "bash scripts/protect-php.sh", "php:protect": "cd php-protection && php protect.php && cd .. && php scripts/fix-plugin-header.php", "wp:protect": "npm run build:secure && npm run wp:prepare && npm run php:protect"}, "dependencies": {"@emotion/react": "^11.14.0", "@headlessui/react": "^2.1.9", "@kinde-oss/kinde-auth-react": "^4.0.1", "axios": "^1.6.8", "chart.js": "^4.4.8", "firebase": "^10.13.0", "iconsax-react": "^0.0.8", "moment": "^2.30.1", "papaparse": "^5.4.1", "react": "^18.2.0", "react-burger-menu": "^3.0.9", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.2.0", "react-markdown": "^9.0.1", "react-modern-drawer": "^1.3.1", "react-router-dom": "^6.23.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "serve": "^14.2.3", "simplebar-react": "^3.2.4", "sweetalert2": "^11.12.4", "swiper": "^11.2.8", "web-vitals": "^4.2.4"}, "devDependencies": {"@types/papaparse": "^5.3.14", "@types/react": "^18.2.66", "@types/react-burger-menu": "^2.8.7", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "fs-extra": "^11.2.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "terser": "^5.39.0", "typescript": "^5.2.2", "vite": "^5.2.0"}, "engines": {"node": "20.x", "npm": "10.x"}, "heroku-run-build-script": true}