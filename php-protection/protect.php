<?php

echo "🔒 Starting PHP obfuscation process...\n";

// Change to yakpro-po directory
chdir(__DIR__ . '/yakpro-po');

// Run obfuscation
$command = 'php yakpro-po.php --config-file ../yakpro-po.cnf';
echo "📝 Running: $command\n";

$output = [];
$return_code = 0;
exec($command . ' 2>&1', $output, $return_code);

if ($return_code === 0) {
    echo "✅ PHP obfuscation completed successfully!\n";
    echo "📁 Protected files saved to: wordpress-plugin-protected/\n";
    
    // Calculate and display file sizes
    $originalSize = getDirSize('../wordpress-plugin');
    $protectedSize = getDirSize('../wordpress-plugin-protected');
    
    echo "\n📊 Obfuscation Statistics:\n";
    echo "   Original size: " . formatBytes($originalSize) . "\n";
    echo "   Obfuscated size: " . formatBytes($protectedSize) . "\n";
    echo "   Size change: " . (($protectedSize - $originalSize) > 0 ? '+' : '') . formatBytes($protectedSize - $originalSize) . "\n";
    echo "   Protection level: MAXIMUM 🔒\n";
    
} else {
    echo "❌ PHP obfuscation failed!\n";
    echo "Error output:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
    exit(1);
}

function getDirSize($dir) {
    if (!is_dir($dir)) return 0;
    
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

?>
