<?php
// YAK Pro - Php Obfuscator: Config File
// Do not delete the previous line! it's a magic string for config file!
//===========================================================================
// BakedBot PHP Protection Configuration
// Custom configuration for WordPress plugin obfuscation
//===========================================================================

// Source and target directories
$conf->source_directory = '../../wordpress-plugin';
$conf->target_directory = '../../wordpress-plugin-protected';

// General obfuscation settings
$conf->scramble_mode = 'identifier';  // identifier, hexa, numeric
$conf->scramble_length = 8;           // length of scrambled names
$conf->strip_indentation = true;     // single line output
$conf->silent = false;               // show progress messages

// What to obfuscate
$conf->obfuscate_class_name = false;
$conf->obfuscate_method_name = true;
$conf->obfuscate_property_name = true;
$conf->obfuscate_variable_name = true;
$conf->obfuscate_constant_name = true;
$conf->obfuscate_function_name = true;
$conf->obfuscate_namespace_name = true;
$conf->obfuscate_label_name = true;

// Preserve WordPress plugin header by not stripping all comments
$conf->strip_comment = false;

// --- WordPress Compatibility ---

// Do not rename the main plugin class or its methods, which are used by WordPress hooks
$conf->t_ignore_classes = ['BakedBotChatbot'];
$conf->t_ignore_methods = [
    'register_assets', 'add_seo_features', 'conditionally_add_chatbot_container',
    'add_admin_menu', 'register_settings', 'ajax_get_user_info', 'ajax_sync_user',
    'ajax_verify_token', 'ajax_create_page', 'ajax_add_to_pages',
    'ajax_remove_from_pages', 'ajax_toggle_frontpage', 'ajax_sync_products',
    'ajax_get_import_status', 'ajax_export_products', 'ajax_get_export_status',
    'add_user_profile_fields', 'save_user_profile_fields', 'admin_notices',
    'remove_admin_notices', 'activate', 'deactivate'
];

// Ignore common WordPress functions
$conf->t_ignore_functions = array(
    'add_action', 'add_filter', 'remove_action', 'remove_filter',
    'wp_enqueue_script', 'wp_enqueue_style', 'wp_localize_script',
    'wp_die', 'wp_redirect', 'wp_send_json', 'wp_send_json_error', 'wp_send_json_success',
    'get_option', 'update_option', 'delete_option', 'add_option',
    'current_user_can', 'is_admin', 'is_user_logged_in',
    'wp_verify_nonce', 'wp_create_nonce', 'check_admin_referer',
    'sanitize_text_field', 'sanitize_email', 'esc_html', 'esc_attr', 'esc_url',
    'register_activation_hook', 'register_deactivation_hook', 'register_uninstall_hook',
    'plugin_dir_path', 'plugin_dir_url', 'plugins_url', 'add_shortcode'
);

// Ignore WordPress global variables
$conf->t_ignore_variables = array(
    'wpdb', 'wp_query', 'post', 'current_user'
);

// --- Advanced Obfuscation ---

$conf->obfuscate_if_statement = true;
$conf->obfuscate_loop_statement = true;
$conf->obfuscate_string_literal = true;
$conf->shuffle_stmts = true;

// Error handling
$conf->abort_on_error = true;
$conf->confirm = true;

// Use all pre-defined PHP classes to prevent conflicts
$conf->t_ignore_pre_defined_classes = 'all';

// PHP file extensions to process
$conf->t_obfuscate_php_extension = array('php');

?>

