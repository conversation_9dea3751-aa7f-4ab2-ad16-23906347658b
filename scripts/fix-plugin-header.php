<?php
/**
 * Fix WordPress Plugin Header in Obfuscated File
 * This script extracts the plugin header from the original file
 * and adds it to the obfuscated version.
 */

echo "🔧 Fixing WordPress Plugin Header...\n";

$originalFile = __DIR__ . '/../wordpress-plugin/bakedbot-chatbot.php';
$obfuscatedFile = __DIR__ . '/../wordpress-plugin-protected/yakpro-po/obfuscated/bakedbot-chatbot.php';

if (!file_exists($originalFile)) {
    echo "❌ Original file not found: $originalFile\n";
    exit(1);
}

if (!file_exists($obfuscatedFile)) {
    echo "❌ Obfuscated file not found: $obfuscatedFile\n";
    exit(1);
}

// Read original file and extract plugin header (first 25 lines)
$originalLines = file($originalFile);
$pluginHeader = implode('', array_slice($originalLines, 0, 25));
echo "✅ Found plugin header (" . strlen($pluginHeader) . " chars)\n";

// Read obfuscated content
$obfuscatedContent = file_get_contents($obfuscatedFile);

// Remove any existing <?php from obfuscated content
$obfuscatedContent = preg_replace('/^<\?php\s*/', '', $obfuscatedContent);

// Combine header + obfuscated code
$fixedContent = $pluginHeader . "\n// --- OBFUSCATED CODE BELOW ---\n" . $obfuscatedContent;

// Write the fixed file
if (file_put_contents($obfuscatedFile, $fixedContent)) {
    echo "✅ Plugin header restored successfully!\n";
    echo "📄 File: $obfuscatedFile\n";
    
    // Verify PHP syntax
    $syntaxCheck = shell_exec("php -l " . escapeshellarg($obfuscatedFile) . " 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        echo "✅ PHP syntax is valid\n";
    } else {
        echo "⚠️  PHP syntax check: $syntaxCheck\n";
    }
} else {
    echo "❌ Failed to write fixed file\n";
    exit(1);
}

echo "🎉 Plugin header fix completed!\n";
?> 