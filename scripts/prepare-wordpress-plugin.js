// This script prepares a WordPress plugin from the built React app
import fs from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";
import process from "process";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Source and destination paths
const rootDir = path.resolve(__dirname, "..");
const distDir = path.join(rootDir, "dist");
const pluginDir = path.join(rootDir, "wordpress-plugin");
const pluginDistDir = path.join(pluginDir, "dist");
const pluginAssetsDir = path.join(pluginDistDir, "assets");
const existingPluginDir = path.join(rootDir, "bakedbot-chatbot");

/**
 * Main function to prepare the WordPress plugin
 */
async function prepareWordPressPlugin() {
  try {
    console.log("Preparing WordPress plugin...");

    // Ensure the plugin directory exists and is empty
    await fs.ensureDir(pluginDir);
    await fs.emptyDir(pluginDir);

    // Check if we have existing plugin files to copy
    if (await fs.pathExists(existingPluginDir)) {
      console.log("Copying existing plugin files...");

      // Copy PHP files
      const existingFiles = await fs.readdir(existingPluginDir);
      for (const file of existingFiles) {
        // Skip the dist directory, we'll copy our fresh build there
        if (file !== "dist") {
          await fs.copy(
            path.join(existingPluginDir, file),
            path.join(pluginDir, file)
          );
        }
      }
    } else {
      console.log("No existing plugin files found, creating basic files...");
      await createBasicPluginFiles();
    }

    // Copy the dist folder contents
    console.log("Copying build files...");
    await fs.ensureDir(pluginDistDir);
    await fs.copy(distDir, pluginDistDir);

    // Create/ensure index.php in dist folder for security
    const indexPhpContent = "<?php\n// Silence is golden.";
    await fs.writeFile(path.join(pluginDistDir, "index.php"), indexPhpContent);

    // Create the assets directory if it doesn't exist
    await fs.ensureDir(pluginAssetsDir);

    // Create initialization script for WordPress
    console.log("Creating initialization script...");
    await createInitScript();

    console.log("WordPress plugin preparation completed!");
  } catch (error) {
    console.error("Error preparing WordPress plugin:", error);
    process.exit(1);
  }
}

/**
 * Create the initialization script for WordPress
 */
async function createInitScript() {
  const initScriptPath = path.join(pluginAssetsDir, "bakedbot-init.js");

  const initScriptContent = `/**
 * BakedBot WordPress Initialization Script
 * Simplified version with essential functionality
 */

(function() {
  console.log("BakedBot Init: Loading...");
  
  // Set up global config
  window.BakedBotConfig = window.BakedBotConfig || {};
  window.BakedBotConfig.embedded = true;
  window.BakedBotConfig.wpEmbedded = true;
  
  /**
   * Simple error handler for React Router issues
   */
  window.addEventListener('error', function(event) {
    if (event.message && (
      event.message.includes('Router') || 
      event.message.includes('invariant') ||
      event.message.includes('Cannot read properties of undefined')
    )) {
      console.log("BakedBot Init: Detected Router error, forcing embedded mode");
      window.BakedBotConfig.skipRouterCheck = true;
      window.BakedBotConfig.disableRouter = true;
    }
  });
  
  /**
   * Initialize a BakedBot widget in the specified container
   */
  function initializeWidget(containerId) {
    console.log("BakedBot Init: Initializing widget for container:", containerId);
    const container = document.getElementById(containerId);
    
    if (!container) {
      console.error("BakedBot Init: Container not found:", containerId);
      return false;
    }
    
    try {
      // Try the preferred initialization method
      if (window.NoRouterApp && window.ReactDOM && window.React) {
        const root = window.ReactDOM.createRoot(container);
        
        root.render(window.React.createElement(window.NoRouterApp, {
          config: window.BakedBotConfig || {},
          theme: window.BakedBotTheme || {},
          user: window.BakedBotUser || {}
        }));
        
        return true;
      } 
      // Fallback to legacy method
      else if (window.createBakedBotWidget) {
        window.createBakedBotWidget(containerId);
        return true;
      }
      else {
        console.error("BakedBot Init: No initialization method available");
        return false;
      }
    } catch (error) {
      console.error("BakedBot Init: Failed to initialize widget:", error);
      
      // Simple fallback for errors
      container.innerHTML = '<div style="padding: 20px; background: #22AD85; color: white; border-radius: 8px; text-align: center;">' +
        '<h3 style="margin: 0 0 10px;">BakedBot</h3>' +
        '<p style="margin: 0 0 15px;">Widget failed to load. Please try refreshing the page.</p>' +
        '<button style="background: #24504A; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;" ' +
        'onclick="location.reload()">Refresh</button></div>';
      
      return false;
    }
  }
  
  /**
   * Initialize all BakedBot containers on the page
   */
  function initializeAllContainers() {
    const containers = document.querySelectorAll('[id^="bakedbot-widget-root-"], [id^="bakedbot-chatbot-"]');
    
    if (containers.length > 0) {
      console.log(\`BakedBot Init: Found \${containers.length} container(s)\`);
      
      for (let i = 0; i < containers.length; i++) {
        initializeWidget(containers[i].id);
      }
    } else {
      console.log("BakedBot Init: No containers found");
    }
  }
  
  // Export minimal set of functions to global scope
  window.BakedBot = window.BakedBot || {};
  window.BakedBot.initialize = initializeWidget;
  window.BakedBot.initializeAll = initializeAllContainers;
  window.BakedBot.version = "1.5.0";
  
  // For backwards compatibility
  window.BakedBotInitializeAllContainers = initializeAllContainers;
  
  // Initialize when DOM is ready
  if (document.readyState === "complete" || document.readyState === "interactive") {
    setTimeout(initializeAllContainers, 100);
  } else {
    document.addEventListener("DOMContentLoaded", function() {
      setTimeout(initializeAllContainers, 100);
    });
  }
  
  console.log("BakedBot Init: Ready");
})();
`;

  await fs.writeFile(initScriptPath, initScriptContent);
  console.log(`Initialization script written to ${initScriptPath}`);
}

/**
 * Create basic plugin files if no existing files found
 * This is a fallback but we should normally use the existing files
 */
async function createBasicPluginFiles() {
  console.log("Creating minimal plugin files...");

  // Create simple index.php
  const indexPhpContent = "<?php\n// Silence is golden.";
  await fs.writeFile(path.join(pluginDir, "index.php"), indexPhpContent);

  // Create minimal README.txt
  const readmeTxtContent = `=== BakedBot Chatbot ===
Contributors: bakedbot
Tags: chatbot, budtender, ai, cannabis
Requires at least: 5.0
Tested up to: 6.4
Stable tag: 1.5.0
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Add the BakedBot AI budtender chatbot to your WordPress site with cross-dispensary user authentication.

== Description ==

BakedBot is an AI-powered budtender chatbot for cannabis websites.

WARNING: This is a minimal plugin file created automatically. Please copy the proper plugin files to the 'bakedbot-chatbot' directory before packaging.`;

  await fs.writeFile(path.join(pluginDir, "README.txt"), readmeTxtContent);

  // Create minimal plugin file
  const pluginPhpContent = `<?php
/**
 * Plugin Name: BakedBot Chatbot
 * Plugin URI: https://bakedbot.ai
 * Description: A budtender AI chatbot powered by BakedBot
 * Version: 1.5.0
 * Author: BakedBot AI
 * Author URI: https://bakedbot.ai
 * Text Domain: bakedbot-chatbot
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// WARNING: This is a minimal plugin file created automatically. 
// Please copy the proper plugin files to the 'bakedbot-chatbot' directory before packaging.

class BakedBotChatbot {
    public function __construct() {
        // This is a minimal implementation
    }
}

// Initialize the plugin
$bakedbot_chatbot = new BakedBotChatbot();`;

  await fs.writeFile(
    path.join(pluginDir, "bakedbot-chatbot.php"),
    pluginPhpContent
  );
}

// Run the script
prepareWordPressPlugin();
