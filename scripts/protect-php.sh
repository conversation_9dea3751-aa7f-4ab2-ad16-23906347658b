#!/bin/bash

# BakedBot PHP Protection Script
# This script implements PHP obfuscation using YakPro-PO

echo "🔒 BakedBot PHP Protection Script"
echo "=================================="

# Check if composer is installed
if ! command -v composer &> /dev/null; then
    echo "❌ Composer is not installed. Please install composer first."
    echo "   Visit: https://getcomposer.org/download/"
    exit 1
fi

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP first."
    exit 1
fi

echo "✅ PHP and Composer detected"

# Create protection directory
PROTECTION_DIR="php-protection"
mkdir -p $PROTECTION_DIR
cd $PROTECTION_DIR

echo "📥 Setting up YakPro-PO obfuscator..."

# Clone YakPro-PO if not exists
if [ ! -d "yakpro-po" ]; then
    echo "📥 Downloading YakPro-PO..."
    git clone https://github.com/pk-fr/yakpro-po.git
    if [ $? -ne 0 ]; then
        echo "❌ Failed to download YakPro-PO"
        exit 1
    fi
fi

cd yakpro-po

# Clone PHP-Parser dependency if not exists
if [ ! -d "PHP-Parser" ]; then
    echo "📥 Downloading PHP-Parser dependency..."
    git clone https://github.com/nikic/PHP-Parser.git --branch 4.x
    if [ $? -ne 0 ]; then
        echo "❌ Failed to download PHP-Parser"
        exit 1
    fi
fi

# Make yakpro-po.php executable
chmod +x yakpro-po.php

# Test if it works
echo "🧪 Testing YakPro-PO installation..."
php yakpro-po.php --help 2>&1 | grep -q "yakpro-po version"
if [ $? -eq 0 ]; then
    echo "✅ YakPro-PO installed successfully!"
else
    echo "❌ YakPro-PO installation failed"
    exit 1
fi

# Go back to protection directory
cd ..

# Create configuration file
echo "📝 Creating obfuscation configuration..."
cat > yakpro-po.cnf << 'EOF'
<?php
//===========================================================================
// YAK Pro - Php Obfuscator: Config File Template
//===========================================================================

// Do not modify this file directly!
// Copy it and adapt to your needs.
// Read the "Configuration file loading algorithm" section of this document for more details.

// IMPORTANT NOTE:
//      If you are using yakpro-po version 2.x, you MUST ensure that
//      the PHP-Parser subdirectory contains version 4.x of PHP-Parser!

// Source directory to obfuscate (required)
$conf->source_directory = '../wordpress-plugin';

// Target directory (required)
$conf->target_directory = '../wordpress-plugin-protected';

// Temporary directory for obfuscation process
$conf->scratch_directory = '/tmp/yakpro-po';

// Silent mode
$conf->silent = false;

// ASCII only output
$conf->ascii_only = true;

// Remove all comments
$conf->strip_indentation = true;

// Scramble names settings
$conf->scramble_mode = 'identifier';  // identifier, hexa, numeric
$conf->scramble_length = 8;

// What to obfuscate
$conf->obfuscate_constant_name = true;
$conf->obfuscate_variable_name = true;
$conf->obfuscate_function_name = true;
$conf->obfuscate_class_name = true;
$conf->obfuscate_interface_name = true;
$conf->obfuscate_trait_name = true;
$conf->obfuscate_property_name = true;
$conf->obfuscate_method_name = true;
$conf->obfuscate_namespace_name = true;
$conf->obfuscate_label_name = true;

// String literals obfuscation
$conf->obfuscate_string_literal = true;

// Control flow obfuscation
$conf->obfuscate_loop_statement = true;
$conf->obfuscate_if_statement = true;

// Statement shuffling
$conf->shuffle_stmts = true;
$conf->shuffle_stmts_chunk_length = 20;
$conf->shuffle_stmts_freq = 0.5;

// WordPress specific ignores (keep these names readable)
$conf->t_ignore_functions = array(
    // WordPress core functions
    'add_action', 'add_filter', 'remove_action', 'remove_filter',
    'wp_enqueue_script', 'wp_enqueue_style', 'wp_localize_script',
    'wp_die', 'wp_redirect', 'wp_send_json', 'wp_send_json_error', 'wp_send_json_success',
    'get_option', 'update_option', 'delete_option', 'add_option',
    'current_user_can', 'is_admin', 'is_user_logged_in',
    'wp_verify_nonce', 'wp_create_nonce', 'check_admin_referer',
    'sanitize_text_field', 'sanitize_email', 'esc_html', 'esc_attr', 'esc_url',
    'register_activation_hook', 'register_deactivation_hook', 'register_uninstall_hook',
    'plugin_dir_path', 'plugin_dir_url', 'plugins_url',
    // Standard PHP functions used by WordPress
    'json_encode', 'json_decode', 'array_merge', 'array_keys', 'array_values',
    'strlen', 'substr', 'strpos', 'str_replace', 'preg_match', 'preg_replace',
    'file_get_contents', 'file_put_contents', 'unlink', 'mkdir', 'rmdir'
);

// WordPress specific class ignores
$conf->t_ignore_classes = array(
    'WP_Error', 'WP_Query', 'WP_User', 'WP_Post', 'WP_Term',
    'WP_REST_Request', 'WP_REST_Response', 'WP_REST_Server',
    'WP_Widget', 'WP_Customize_Control', 'WP_List_Table'
);

// WordPress hooks and methods to preserve
$conf->t_ignore_methods = array(
    '__construct', '__destruct', '__call', '__callStatic',
    '__get', '__set', '__isset', '__unset', '__toString',
    'init', 'admin_init', 'wp_loaded', 'admin_menu', 'admin_enqueue_scripts',
    'wp_enqueue_scripts', 'wp_ajax_', 'wp_ajax_nopriv_'
);

// Files to exclude from obfuscation
$conf->t_skip_files = array(
    // Keep these files readable for debugging
    'debug.php', 'test.php', 'tests.php'
);

// Directories to exclude
$conf->t_skip_dirs = array(
    'tests', 'test', '.git', '.svn', 'node_modules'
);

EOF

# Create protection script
echo "📝 Creating protection execution script..."
cat > protect.php << 'EOF'
<?php

echo "🔒 Starting PHP obfuscation process...\n";

// Change to yakpro-po directory
chdir(__DIR__ . '/yakpro-po');

// Run obfuscation
$command = 'php yakpro-po.php --config-file ../yakpro-po.cnf';
echo "📝 Running: $command\n";

$output = [];
$return_code = 0;
exec($command . ' 2>&1', $output, $return_code);

if ($return_code === 0) {
    echo "✅ PHP obfuscation completed successfully!\n";
    echo "📁 Protected files saved to: wordpress-plugin-protected/\n";
    
    // Calculate and display file sizes
    $originalSize = getDirSize('../wordpress-plugin');
    $protectedSize = getDirSize('../wordpress-plugin-protected');
    
    echo "\n📊 Obfuscation Statistics:\n";
    echo "   Original size: " . formatBytes($originalSize) . "\n";
    echo "   Obfuscated size: " . formatBytes($protectedSize) . "\n";
    echo "   Size change: " . (($protectedSize - $originalSize) > 0 ? '+' : '') . formatBytes($protectedSize - $originalSize) . "\n";
    echo "   Protection level: MAXIMUM 🔒\n";
    
} else {
    echo "❌ PHP obfuscation failed!\n";
    echo "Error output:\n";
    foreach ($output as $line) {
        echo "   $line\n";
    }
    exit(1);
}

function getDirSize($dir) {
    if (!is_dir($dir)) return 0;
    
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

?>
EOF

echo ""
echo "🎉 PHP Protection Setup Complete!"
echo ""
echo "Next steps:"
echo "1. Review the configuration in: php-protection/yakpro-po.cnf"
echo "2. Run protection with: npm run php:protect"
echo "3. Your protected PHP files will be in: wordpress-plugin-protected/"
echo ""
echo "⚠️  IMPORTANT:"
echo "   - Test the obfuscated code thoroughly before deployment"
echo "   - Keep your original source code safe"
echo "   - The obfuscated code is for distribution only"
echo "" 