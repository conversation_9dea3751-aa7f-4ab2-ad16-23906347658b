import { lazy, Suspense, useEffect, useState } from "react";
import { Route, Routes, useLocation } from "react-router-dom";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth } from "./config/firebase-config";
import "./styles/main.css";
import { startTrace, stopTrace } from "./utils/performanceMonitoring";
import { withAsyncErrorHandling } from "./utils/errorHandling";
import { ConfigProvider } from "./contexts/ConfigContext";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { PreferencesProvider } from "./contexts/PreferencesContext";
import { CartProvider } from "./views/ChatWidget/CartContext";

// Lazy load components
const ChatWidget = lazy(() => import("./views/ChatWidget"));
const HeadlessShop = lazy(() => import("./views/HeadlessShop"));
const ProductDetailsPage = lazy(
  () => import("./views/ProductDetails/ProductDetailsPage")
);

// Simple loading component
const Loading = () => (
  <div className="flex items-center justify-center h-screen">Loading...</div>
);

// Define interface for props passed from WordPress
interface WordPressAppProps {
  config?: any;
  theme?: any;
  user?: any;
}

// Determine if app is running in embedded mode before the component renders
const isEmbeddedMode = () => {
  if (typeof window !== "undefined") {
    const config = (window as any).BakedBotConfig || {};
    // Check all possible flags for embedded mode
    return (
      config.wpEmbedded || config.embedded || config.skipRouterCheck || false
    );
  }
  return false;
};

// Create a wrapper component that doesn't use any React Router hooks
const EmbeddedContent = () => {
  console.log(
    "BakedBot Debug: Rendering embedded content without React Router"
  );

  return (
    <Suspense fallback={<Loading />}>
      <CartProvider>
        <ChatWidget view="chat" skipVerify={true} />
      </CartProvider>
    </Suspense>
  );
};

// Create a component that handles route monitoring with the useLocation hook
// This is a separate component to avoid conditional hook calls
const RouteMonitor = () => {
  // Safe to use useLocation here because this component only renders when not in embedded mode
  const location = useLocation();

  useEffect(() => {
    console.log(
      "BakedBot Debug: Monitoring route changes for",
      location.pathname
    );
    const routeTrace = startTrace(`route_change_${location.pathname}`);
    return () => {
      stopTrace(routeTrace);
    };
  }, [location.pathname]);

  return null; // This component doesn't render anything
};

// Create a wrapper component that does use React Router
const RouterContent = () => {
  console.log("BakedBot Debug: Rendering RouterContent with Routes");

  return (
    <>
      {/* Route monitor component that uses the location hook */}
      <RouteMonitor />

      {/* Normal Routes */}
      <Routes>
        <Route
          path="/"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <ChatWidget view="chat" skipVerify />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="widget/:customerID"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <ChatWidget view="chat" skipVerify />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="widget/deals/:customerID"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <ChatWidget view="chat" skipVerify />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="widget/events/:customerID"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <ChatWidget view="events" skipVerify />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="/shop"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <HeadlessShop
                  apiKey={import.meta.env.VITE_BAKED_BOT_API_KEY}
                  enableHomepage={true}
                  defaultView="homepage"
                  theme="light"
                  primaryColor="#065f46"
                  secondaryColor="#10b981"
                  showPromotion={true}
                  promotionTitle="Premium Cannabis Marketplace"
                  promotionSubtitle="Discover our curated selection of premium cannabis products"
                />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="/shop/:customerID"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <HeadlessShop
                  apiKey={import.meta.env.VITE_BAKED_BOT_API_KEY}
                  enableHomepage={true}
                  defaultView="homepage"
                  theme="light"
                  primaryColor="#065f46"
                  secondaryColor="#10b981"
                  showPromotion={true}
                  promotionTitle="Premium Cannabis Marketplace"
                  promotionSubtitle="Discover our curated selection of premium cannabis products"
                />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="/menu"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <HeadlessShop
                  apiKey={import.meta.env.VITE_BAKED_BOT_API_KEY}
                  enableHomepage={false}
                  theme="light"
                  primaryColor="#065f46"
                  secondaryColor="#10b981"
                />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="/menu/:customerID"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <HeadlessShop
                  apiKey={import.meta.env.VITE_BAKED_BOT_API_KEY}
                  enableHomepage={false}
                  theme="light"
                  primaryColor="#065f46"
                  secondaryColor="#10b981"
                />
              </CartProvider>
            </Suspense>
          }
        />
        <Route
          path="/product"
          element={
            <Suspense fallback={<Loading />}>
              <CartProvider>
                <ProductDetailsPage />
              </CartProvider>
            </Suspense>
          }
        />
      </Routes>
    </>
  );
};

function App(props?: WordPressAppProps) {
  console.log("BakedBot Debug: App component rendering with props:", props);

  // Detect embedded mode using a state variable and immediately check
  const [isEmbedded, setIsEmbedded] = useState(() => {
    // Check props first
    if (
      props?.config?.wpEmbedded ||
      props?.config?.embedded ||
      props?.config?.skipRouterCheck
    ) {
      return true;
    }
    // Then check window.BakedBotConfig
    return isEmbeddedMode();
  });

  console.log("BakedBot Debug: Initial embedded mode state:", isEmbedded);

  // Update embedded state from props or window object
  useEffect(() => {
    console.log("BakedBot Debug: App useEffect for props injection running");

    // First check props if available
    if (props) {
      if (props.config && typeof window !== "undefined") {
        console.log(
          "BakedBot Debug: Setting BakedBotConfig from props:",
          props.config
        );
        (window as any).BakedBotConfig = props.config;

        // Check if we're in embedded mode from the config
        if (
          props.config.wpEmbedded ||
          props.config.embedded ||
          props.config.skipRouterCheck
        ) {
          console.log(
            "BakedBot Debug: Setting embedded mode to true from props"
          );
          setIsEmbedded(true);
        }
      }
      if (props.theme && typeof window !== "undefined") {
        console.log(
          "BakedBot Debug: Setting BakedBotTheme from props:",
          props.theme
        );
        (window as any).BakedBotTheme = props.theme;
      }
      if (props.user && typeof window !== "undefined") {
        console.log(
          "BakedBot Debug: Setting BakedBotUser from props:",
          props.user
        );
        (window as any).BakedBotUser = props.user;
      }
    } else {
      console.log("BakedBot Debug: No props provided to App component");
    }

    // Next check window.BakedBotConfig directly (in case it was set outside)
    const config = (window as any).BakedBotConfig || {};
    if (config.wpEmbedded || config.embedded || config.skipRouterCheck) {
      console.log(
        "BakedBot Debug: Setting embedded mode to true from window.BakedBotConfig"
      );
      setIsEmbedded(true);
    }
  }, [props]);

  // Use error boundary to catch any React Router errors and switch to embedded mode
  useEffect(() => {
    // Set up error listener
    const handleError = (event: ErrorEvent) => {
      // Check if this is a React Router error
      if (
        event.message &&
        (event.message.includes("invariant") ||
          event.message.includes("Router") ||
          event.message.includes("Context"))
      ) {
        console.log(
          "BakedBot Debug: Caught possible Router error, switching to embedded mode"
        );
        setIsEmbedded(true);
      }
    };

    window.addEventListener("error", handleError);

    return () => {
      window.removeEventListener("error", handleError);
    };
  }, []);

  return (
    <ConfigProvider>
      <ThemeProvider>
        <AuthProvider>
          <PreferencesProvider>
            {isEmbedded ? (
              // Use the component that doesn't have any React Router dependencies
              <EmbeddedContent />
            ) : (
              // Only use React Router content when not in embedded mode
              <RouterContent />
            )}
          </PreferencesProvider>
        </AuthProvider>
      </ThemeProvider>
    </ConfigProvider>
  );
}

export default App;
