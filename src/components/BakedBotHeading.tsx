import React from "react";

interface BakedBotHeadingProps {
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * A custom heading component that renders as a div instead of an h1-h6 element
 * to avoid theme CSS conflicts while maintaining semantic hierarchy.
 */
const BakedBotHeading: React.FC<BakedBotHeadingProps> = ({
  level = 2,
  children,
  className = "",
  style = {},
}) => {
  // Map heading level to appropriate font size and style
  const headingStyles: Record<number, React.CSSProperties> = {
    1: {
      fontSize: "2em",
      fontWeight: 600,
    },
    2: {
      fontSize: "1.5em",
      fontWeight: 500,
    },
    3: {
      fontSize: "1.17em",
      fontWeight: 500,
    },
    4: {
      fontSize: "1em",
      fontWeight: 500,
    },
    5: {
      fontSize: "0.83em",
      fontWeight: 500,
    },
    6: {
      fontSize: "0.67em",
      fontWeight: 500,
    },
  };

  return (
    <div
      className={`bakedbot-heading h${level} ${className}`}
      style={{
        display: "block",
        lineHeight: "1.2",
        ...headingStyles[level],
        ...style,
      }}
      role="heading"
      aria-level={level}
    >
      {children}
    </div>
  );
};

export default BakedBotHeading;
