import React from "react";
import BakedBotHeading from "./BakedBotHeading";

interface ChartRendererProps {
  chartData: {
    title?: string;
    type: "bar" | "line" | "pie" | "doughnut";
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      fill?: boolean;
    }[];
  };
  className?: string;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({
  chartData,
  className = "",
}) => {
  // This is a placeholder component that will render a message about needing Chart.js
  // In a real implementation, you would use react-chartjs-2 to render actual charts

  return (
    <div className={`chart-container p-4 border rounded-lg my-4 ${className}`}>
      <div className="text-center">
        <div className="w-full">
          {chartData.title && (
            <BakedBotHeading level={3} className="font-bold text-lg mb-2">
              {chartData.title}
            </BakedBotHeading>
          )}
        </div>
        <div className="bg-gray-100 p-4 rounded-lg">
          <p className="text-gray-700 mb-2">
            To implement this chart, please install Chart.js and
            react-chartjs-2:
          </p>
          <code className="bg-gray-200 p-2 rounded block text-sm mb-4">
            npm install chart.js react-chartjs-2
          </code>

          <div className="text-left mb-4">
            <p className="font-medium mb-1">Chart Type: {chartData.type}</p>
            <p className="font-medium mb-1">
              Labels: {chartData.labels.join(", ")}
            </p>
            <p className="font-medium">Datasets:</p>
            <ul className="list-disc pl-5">
              {chartData.datasets.map((dataset, index) => (
                <li key={index}>
                  {dataset.label}: {dataset.data.join(", ")}
                </li>
              ))}
            </ul>
          </div>

          <p className="text-sm text-gray-600">
            Once Chart.js is installed, replace this component with a proper
            chart implementation.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChartRenderer;
