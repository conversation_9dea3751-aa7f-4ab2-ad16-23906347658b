.bakedbot-floating-cart-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  max-width: 100vw;
  z-index: 999;
  background: var(--background-color, #ffffff);
  border-top: 1px solid var(--border-color, #e5e7eb);
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1rem;
  transform: translateY(100%);
  animation: slideUpFooter 0.3s ease-out forwards;
  box-sizing: border-box;
  overflow: hidden;
}

@keyframes slideUpFooter {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.bakedbot-cart-summary-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 1rem;
}

.bakedbot-cart-info-footer {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.bakedbot-cart-items {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color, #374151);
  font-weight: 500;
}

.bakedbot-cart-items svg {
  color: var(--primary-color, #10b981);
  font-size: 1.25rem;
}

.bakedbot-cart-total {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--primary-color, #10b981);
}

.bakedbot-show-checkout-btn {
  background: var(--primary-color, #10b981);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
  white-space: nowrap;
}

.bakedbot-show-checkout-btn:hover {
  background: var(--secondary-color, #059669);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.bakedbot-show-checkout-btn:active {
  transform: translateY(0);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .bakedbot-floating-cart-footer {
    padding: 0.75rem 1rem;
    max-width: 100vw;
    width: 100%;
    box-sizing: border-box;
    left: 0;
    right: 0;
  }
  
  .bakedbot-cart-summary-footer {
    gap: 0.75rem;
    max-width: 100%;
    width: 100%;
    box-sizing: border-box;
  }
  
  .bakedbot-cart-info-footer {
    gap: 0.75rem;
    min-width: 0;
    flex: 1;
    max-width: 100%;
    overflow: hidden;
  }
  
  .bakedbot-cart-items {
    min-width: 0;
    flex-shrink: 1;
  }
  
  .bakedbot-cart-items span {
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .bakedbot-cart-total {
    font-size: 1rem;
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  .bakedbot-show-checkout-btn {
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    flex-shrink: 0;
    white-space: nowrap;
  }
}

@media (max-width: 480px) {
  .bakedbot-floating-cart-footer {
    padding: 0.5rem 0.75rem;
    left: 0;
    right: 0;
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  .bakedbot-cart-summary-footer {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
  }
  
  .bakedbot-cart-info-footer {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
  }
  
  .bakedbot-cart-items {
    font-size: 0.8125rem;
    min-width: 0;
    flex: 1;
    overflow: hidden;
  }
  
  .bakedbot-cart-items span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .bakedbot-cart-total {
    font-size: 0.9375rem;
    white-space: nowrap;
    flex-shrink: 0;
  }
  
  .bakedbot-show-checkout-btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.875rem;
    min-height: 44px; /* iOS touch target minimum */
    box-sizing: border-box;
    border-radius: 0.375rem;
  }
}

@media (max-width: 320px) {
  .bakedbot-floating-cart-footer {
    padding: 0.5rem;
  }
  
  .bakedbot-cart-items {
    font-size: 0.75rem;
  }
  
  .bakedbot-cart-total {
    font-size: 0.875rem;
  }
  
  .bakedbot-show-checkout-btn {
    padding: 0.625rem 0.5rem;
    font-size: 0.8125rem;
  }
}

/* Add bottom padding to content areas when floating footer is present and cart is not open */
body:not(.bakedbot-cart-open) .bakedbot-headless-homepage.enhanced,
body:not(.bakedbot-cart-open) .bakedbot-headless-menu.enhanced {
  padding-bottom: 120px;
}

@media (max-width: 768px) {
  body:not(.bakedbot-cart-open) .bakedbot-headless-homepage.enhanced,
  body:not(.bakedbot-cart-open) .bakedbot-headless-menu.enhanced {
    padding-bottom: 100px;
  }
}

@media (max-width: 480px) {
  body:not(.bakedbot-cart-open) .bakedbot-headless-homepage.enhanced,
  body:not(.bakedbot-cart-open) .bakedbot-headless-menu.enhanced {
    padding-bottom: 140px;
  }
}

/* Custom toast styles for the add to cart notification */
.bakedbot-toast {
  font-family: inherit !important;
}

.bakedbot-toast .swal2-title {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
}

.bakedbot-toast .swal2-content {
  font-size: 0.75rem !important;
} 