import React from "react";
import { FaShoppingCart } from "react-icons/fa";
import { useCart } from "../../views/ChatWidget/CartContext";
import "./FloatingCartFooter.css";

interface FloatingCartFooterProps {
  onShowCheckout: () => void;
  isCartOpen?: boolean;
}

export const FloatingCartFooter: React.FC<FloatingCartFooterProps> = ({
  onShowCheckout,
  isCartOpen = false,
}) => {
  const { cart } = useCart();

  // Calculate total items in cart
  const totalItems = Object.values(cart).reduce(
    (sum, { quantity }) => sum + quantity,
    0
  );

  // Calculate total price
  const totalPrice = Object.values(cart).reduce(
    (sum, { product, quantity }) => sum + product.latest_price * quantity,
    0
  );

  // Don't show if cart is empty OR if cart is open
  if (totalItems === 0 || isCartOpen) {
    return null;
  }

  return (
    <div className="bakedbot-floating-cart-footer">
      <div className="bakedbot-cart-summary-footer">
        <div className="bakedbot-cart-info-footer">
          <div className="bakedbot-cart-items">
            <FaShoppingCart />
            <span>
              {totalItems} item{totalItems !== 1 ? "s" : ""}
            </span>
          </div>
          <div className="bakedbot-cart-total">${totalPrice.toFixed(2)}</div>
        </div>
        <button className="bakedbot-show-checkout-btn" onClick={onShowCheckout}>
          Show Checkout
        </button>
      </div>
    </div>
  );
};

export default FloatingCartFooter;
