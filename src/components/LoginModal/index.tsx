import { useState } from "react";
import { signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { auth } from "../../config/firebase-config";
import { FaTimes, FaUser } from "react-icons/fa";

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLogin?: () => void;
  title?: string;
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onLogin,
  title = "Sign In",
}) => {
  const [showEmailForm, setShowEmailForm] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // In a real implementation, we would validate credentials here
      // For now we just call onLogin to simulate successful login
      onLogin?.();
      onClose();
    } catch (error) {
      console.error("Error signing in:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);

      onLogin?.();
      onClose();
    } catch (error) {
      console.error("Error signing in with Google:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
            <FaUser className="text-primary-color" />
            {title}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            aria-label="Close login modal"
          >
            <FaTimes className="text-gray-500" />
          </button>
        </div>

        <div className="flex flex-col items-center w-full">
          {!showEmailForm ? (
            <div className="w-full space-y-4">
              {/* Google Sign In */}
              <button
                type="button"
                onClick={handleGoogleSignIn}
                className="w-full py-3 px-4 rounded-md flex items-center justify-center bg-white text-gray-800 hover:bg-gray-100 transition-colors shadow-md border border-gray-300"
                disabled={isLoading}
              >
                <img
                  src="https://bakedbot-3e8a803d7a8a.herokuapp.com/images/google-icon.png"
                  alt="Google"
                  className="w-5 h-5 mr-3"
                />
                <span className="font-medium">
                  {isLoading ? "Signing in..." : "Continue with Google"}
                </span>
              </button>

              {/* Email form toggle */}
              <button
                onClick={() => setShowEmailForm(true)}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Sign in with email instead
              </button>
            </div>
          ) : (
            <form
              onSubmit={handleSubmit}
              className="flex flex-col gap-4 w-full"
            >
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Email"
                className="p-3 rounded-md bg-gray-100 text-sm border border-gray-300 text-gray-800 focus:ring-2 focus:ring-primary-color focus:border-primary-color"
                required
              />
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                className="p-3 rounded-md bg-gray-100 text-sm border border-gray-300 text-gray-800 focus:ring-2 focus:ring-primary-color focus:border-primary-color"
                required
              />
              <div className="flex justify-between gap-3 mt-2">
                <button
                  type="button"
                  onClick={() => setShowEmailForm(false)}
                  className="text-gray-500 text-sm py-2 px-4 hover:bg-gray-100 rounded-md transition-colors"
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="bg-primary-color text-white rounded-md py-2 px-6 text-sm hover:bg-secondary-color transition-colors flex-1 font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? "Signing in..." : "Sign In"}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginModal;
