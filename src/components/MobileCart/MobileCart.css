/* Mobile Cart Overlay */
.mobile-cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-cart-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Backdrop */
.mobile-cart-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

/* Cart Content */
.mobile-cart-content {
  position: absolute;
  bottom: 0;
  color: #111827;

  left: 0;
  right: 0;
  background: #ffffff;
  border-radius: 20px 20px 0 0;
  max-height: 85vh;
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
}

.mobile-cart-content.open {
  transform: translateY(0);
}

/* Header */
.mobile-cart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  border-radius: 20px 20px 0 0;
  position: relative;
}

.mobile-cart-header::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 4px;
  background: #d1d5db;
  border-radius: 2px;
}

.mobile-cart-title {
  display: flex;
  color: #111827;
  align-items: center;
  gap: 12px;
}

.mobile-cart-title svg {
  color: var(--secondary-color, #10b981);
  font-size: 20px;
}

.mobile-cart-title p {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #111827;
}

.mobile-cart-close {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.mobile-cart-close:hover,
.mobile-cart-close:active {
  background: #e5e7eb;
  color: #374151;
  transform: scale(0.95);
}

/* Body */
.mobile-cart-body {
  flex: 1;
  color: #111827;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 24px;
}

/* Empty State */
.mobile-cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.mobile-cart-empty svg {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.mobile-cart-empty p {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.mobile-cart-empty p {
  margin: 0;
  font-size: 14px;
}

/* Cart Items */
.mobile-cart-items {
  padding: 16px 0;
}

.mobile-cart-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-cart-item:last-child {
  border-bottom: none;
}

.mobile-cart-item-image {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: 12px;
  overflow: hidden;
  background: var(--hover-color, #ffffff);
  border: 1px solid #e5e7eb;
}

.mobile-cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-cart-item-details {
  flex: 1;
  min-width: 0;
}

.mobile-cart-item-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.mobile-cart-item-price {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-color, #065f46);
}

.mobile-cart-item-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* Quantity Controls */
.mobile-quantity-controls {
  display: flex;
  align-items: center;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.mobile-quantity-btn {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--secondary-color, #10b981);
  font-size: 14px;
}

.mobile-quantity-btn:hover:not(:disabled),
.mobile-quantity-btn:active:not(:disabled) {
  background: var(--secondary-color, #10b981);
  color: white;
}

.mobile-quantity-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.mobile-quantity-display {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 36px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  background: white;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

/* Remove Button */
.mobile-remove-btn {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #dc2626;
  font-size: 12px;
}

.mobile-remove-btn:hover,
.mobile-remove-btn:active {
  background: #fee2e2;
  border-color: #fca5a5;
  transform: scale(0.95);
}

/* Footer */
.mobile-cart-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
  position: sticky;
  bottom: 0;
}

.mobile-cart-summary {
  margin-bottom: 20px;
}

.mobile-cart-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.mobile-cart-total-label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.mobile-cart-total-price {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color, #065f46);
}

/* Checkout Button */
.mobile-checkout-btn {
  width: 100%;
  padding: 16px;
  background: var(--primary-color, #065f46);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(6, 95, 70, 0.2);
  min-height: 54px; /* iOS touch target minimum */
  touch-action: manipulation;
}

.mobile-checkout-btn:hover,
.mobile-checkout-btn:active {
  background: var(--secondary-color, #10b981);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.mobile-checkout-btn:active {
  transform: translateY(0);
}

/* Desktop Styles */
@media (min-width: 769px) {
  .mobile-cart-content {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: auto;
    width: 400px;
    max-height: 100vh;
    min-height: 100vh;
    border-radius: 0;
    transform: translateX(100%);
  }

  .mobile-cart-content.open {
    transform: translateX(0);
  }

  .mobile-cart-header {
    border-radius: 0;
  }

  .mobile-cart-header::before {
    display: none;
  }

  .mobile-cart-footer {
    position: static;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-cart-overlay,
  .mobile-cart-content,
  .mobile-cart-close,
  .mobile-quantity-btn,
  .mobile-remove-btn,
  .mobile-checkout-btn {
    transition: none;
  }
}

