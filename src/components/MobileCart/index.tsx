import React, { useState, useEffect } from "react";
import {
  FaTimes,
  FaPlus,
  FaMinus,
  FaShoppingCart,
  FaTrash,
} from "react-icons/fa";
import { useCart } from "../../views/ChatWidget/CartContext";
import "./MobileCart.css";

interface MobileCartProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckout: () => void;
}

export const MobileCart: React.FC<MobileCartProps> = ({
  isOpen,
  onClose,
  onCheckout,
}) => {
  const { cart, updateQuantity, removeFromCart } = useCart();
  const [isAnimating, setIsAnimating] = useState(false);

  // Calculate totals
  const totalItems = Object.values(cart).reduce(
    (sum, { quantity }) => sum + quantity,
    0
  );
  const totalPrice = Object.values(cart).reduce(
    (sum, { product, quantity }) => sum + product.latest_price * quantity,
    0
  );

  // Handle body scroll prevention
  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
      document.body.style.top = `-${window.scrollY}px`;

      setIsAnimating(true);
    } else {
      // Restore body scroll
      const scrollY = document.body.style.top;
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";

      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || "0") * -1);
      }

      setIsAnimating(false);
    }

    return () => {
      // Cleanup on unmount
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
    };
  }, [isOpen]);

  if (!isOpen && !isAnimating) return null;

  return (
    <div className={`mobile-cart-overlay ${isOpen ? "open" : ""}`}>
      {/* Backdrop */}
      <div className="mobile-cart-backdrop" onClick={onClose} />

      {/* Cart Content */}
      <div className={`mobile-cart-content ${isOpen ? "open" : ""}`}>
        {/* Header */}
        <div className="mobile-cart-header">
          <div className="mobile-cart-title">
            <FaShoppingCart />
            <p>Your Cart {totalItems > 0 && `(${totalItems})`}</p>
          </div>
          <button className="mobile-cart-close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        {/* Body */}
        <div className="mobile-cart-body">
          {totalItems === 0 ? (
            <div className="mobile-cart-empty">
              <FaShoppingCart />
              <p>Your cart is empty</p>
              <p>Add some products to get started</p>
            </div>
          ) : (
            <div className="mobile-cart-items">
              {Object.entries(cart).map(
                ([productId, { product, quantity }]) => (
                  <div key={productId} className="mobile-cart-item">
                    <div className="mobile-cart-item-image">
                      <img
                        src={product.image_url}
                        alt={product.product_name}
                        loading="lazy"
                      />
                    </div>

                    <div className="mobile-cart-item-details">
                      <p className="mobile-cart-item-name">
                        {product.product_name}
                      </p>
                      <p className="mobile-cart-item-price">
                        ${product.latest_price}
                      </p>
                    </div>

                    <div className="mobile-cart-item-actions">
                      <div className="mobile-quantity-controls">
                        <button
                          className="mobile-quantity-btn"
                          onClick={() => updateQuantity(productId, -1)}
                        >
                          <FaMinus />
                        </button>
                        <span className="mobile-quantity-display">
                          {quantity}
                        </span>
                        <button
                          className="mobile-quantity-btn"
                          onClick={() => updateQuantity(productId, 1)}
                        >
                          <FaPlus />
                        </button>
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {totalItems > 0 && (
          <div className="mobile-cart-footer">
            <div className="mobile-cart-summary">
              <div className="mobile-cart-total">
                <span className="mobile-cart-total-label">Total:</span>
                <span className="mobile-cart-total-price">
                  ${totalPrice.toFixed(2)}
                </span>
              </div>
            </div>

            <button className="mobile-checkout-btn" onClick={onCheckout}>
              Proceed to Checkout
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileCart;
