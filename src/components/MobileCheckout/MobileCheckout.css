/* Mobile Checkout Overlay */
.mobile-checkout-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

.mobile-checkout-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Backdrop */
.mobile-checkout-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

/* Checkout Content */
.mobile-checkout-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-checkout-content.open {
  transform: translateY(0);
}

/* Header */
.mobile-checkout-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.mobile-checkout-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-checkout-back {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.mobile-checkout-back:hover,
.mobile-checkout-back:active {
  background: #e5e7eb;
  color: var(--background-color, #ffffff);
  transform: scale(0.95);
}

.mobile-checkout-title p {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  text-align: center;
}

.mobile-checkout-close {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.mobile-checkout-close:hover,
.mobile-checkout-close:active {
  background: #e5e7eb;
  color: var(--background-color, #ffffff);
  transform: scale(0.95);
}

/* Progress Indicator */
.mobile-checkout-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 300px;
  margin: 0 auto;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.progress-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f3f4f6;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.progress-step.active .progress-circle {
  background: var(--secondary-color, #10b981);
  color: white;
}

.progress-step.completed .progress-circle {
  background: var(--secondary-color, #10b981);
  color: white;
}

.progress-step span {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  transition: color 0.3s ease;
}

.progress-step.active span,
.progress-step.completed span {
  color: var(--secondary-color, #10b981);
}

.progress-line {
  flex: 1;
  height: 2px;
  background: #e5e7eb;
  margin: 0 8px;
  position: relative;
  top: -16px;
}

/* Body */
.mobile-checkout-body {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 24px;
}

/* Cart Step */
.mobile-checkout-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.mobile-checkout-empty svg {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.mobile-checkout-empty p {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--background-color, #ffffff);
}

.mobile-checkout-empty p {
  margin: 0;
  font-size: 14px;
}

.mobile-checkout-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobile-checkout-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.mobile-checkout-item-image {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.mobile-checkout-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mobile-checkout-item-details {
  flex: 1;
  min-width: 0;
}

.mobile-checkout-item-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.mobile-checkout-item-price {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.mobile-checkout-item-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* Quantity Controls */
.mobile-checkout-quantity-controls {
  display: flex;
  align-items: center;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  overflow: hidden;
}

.mobile-checkout-quantity-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--secondary-color, #10b981);
  font-size: 12px;
}

.mobile-checkout-quantity-btn:hover:not(:disabled),
.mobile-checkout-quantity-btn:active:not(:disabled) {
  background: var(--secondary-color, #10b981);
  color: white;
}

.mobile-checkout-quantity-btn:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.mobile-checkout-quantity-display {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  background: white;
  border-left: 1px solid #d1d5db;
  border-right: 1px solid #d1d5db;
}

.mobile-checkout-remove-btn {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #dc2626;
  font-size: 10px;
}

.mobile-checkout-remove-btn:hover,
.mobile-checkout-remove-btn:active {
  background: #fee2e2;
  border-color: #fca5a5;
  transform: scale(0.95);
}

/* Form Step */
.mobile-checkout-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mobile-form-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mobile-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.mobile-form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-form-field label {
  font-size: 14px;
  font-weight: 600;
  color: var(--background-color, #ffffff);
}

.mobile-form-field input {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px; /* Prevent zoom on iOS */
  background: #ffffff;
  transition: border-color 0.2s ease;
}

.mobile-form-field input:focus {
  outline: none;
  border-color: var(--primary-color, #065f46);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.mobile-form-field input:invalid {
  border-color: #dc2626;
}

/* Loading Step */
.mobile-checkout-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.mobile-loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid var(--primary-color, #065f46);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.mobile-checkout-loading p {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.mobile-checkout-loading p {
  margin: 0;
  font-size: 14px;
}

/* Footer */
.mobile-checkout-footer {
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: #ffffff;
  position: sticky;
  bottom: 0;
}

.mobile-checkout-summary {
  margin-bottom: 20px;
}

.mobile-checkout-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.mobile-checkout-total-label {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.mobile-checkout-total-price {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color, #065f46);
}

/* Buttons */
.mobile-checkout-continue-btn,
.mobile-checkout-place-order-btn {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 54px; /* iOS touch target minimum */
  touch-action: manipulation;
}

.mobile-checkout-continue-btn {
  background: var(--primary-color, #065f46);
  color: white;
  box-shadow: 0 4px 12px rgba(6, 95, 70, 0.2);
}

.mobile-checkout-continue-btn:hover,
.mobile-checkout-continue-btn:active {
  background: var(--secondary-color, #10b981);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.mobile-checkout-place-order-btn {
  background: var(--primary-color, #065f46);
  color: white;
  box-shadow: 0 4px 12px rgba(6, 95, 70, 0.2);
}

.mobile-checkout-place-order-btn:hover:not(:disabled),
.mobile-checkout-place-order-btn:active:not(:disabled) {
  background: var(--secondary-color, #10b981);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.mobile-checkout-place-order-btn:disabled {
  background: #9ca3af;
  color: #ffffff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.mobile-checkout-place-order-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Desktop Modal Styles */
.bakedbot-checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.bakedbot-checkout-modal.open {
  opacity: 1;
  visibility: visible;
}

.bakedbot-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.bakedbot-modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 64px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.bakedbot-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
}

.bakedbot-modal-header p {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

.modal-close-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6b7280;
}

.modal-close-btn:hover {
  background: var(--hover-color);
  color: var(--background-color, #ffffff);
}

.bakedbot-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.bakedbot-checkout-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.bakedbot-order-summary {
  background: #f9fafb;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.bakedbot-order-summary p {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.bakedbot-order-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.bakedbot-order-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.bakedbot-order-item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #e5e7eb;
}

.bakedbot-item-details {
  flex: 1;
  min-width: 0;
}

.bakedbot-item-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
  line-height: 1.4;
}

.bakedbot-item-price {
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-color, #065f46);
}

.bakedbot-item-quantity {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.bakedbot-quantity-controls {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

.bakedbot-quantity-controls button {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--secondary-color, #10b981);
  font-size: 12px;
}

.bakedbot-quantity-controls button:hover {
  background: var(--secondary-color, #10b981);
  color: white;
}

.bakedbot-quantity-controls span {
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  background: white;
  border-left: 1px solid #d1d5db;
  border-right: 1px solid #d1d5db;
}

.bakedbot-remove-item {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #dc2626;
  font-size: 10px;
}

.bakedbot-remove-item:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.bakedbot-order-total {
  padding-top: 20px;
  border-top: 2px solid #e5e7eb;
}

.bakedbot-total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color, #065f46);
}

.bakedbot-customer-info {
  display: flex;
  flex-direction: column;
}

.bakedbot-checkout-form {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.bakedbot-checkout-form p {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.bakedbot-form-title {
  margin-bottom: 0.25rem;
}

.bakedbot-form-row {
  display: flex;
  gap: 1rem;
}

.bakedbot-form-field {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.bakedbot-checkout-form input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-bottom: 0.5rem; /* Reduced from a larger value or default */
}

.bakedbot-checkout-form input:focus {
  outline: none;
  border-color: var(--primary-color, #065f46);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.bakedbot-checkout-form label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.bakedbot-place-order-btn {
  width: 100%;
  padding: 16px;
  background: var(--primary-color, #065f46);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(6, 95, 70, 0.2);
  min-height: 54px;
}

.bakedbot-place-order-btn:hover:not(:disabled) {
  background: var(--secondary-color, #10b981);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.bakedbot-place-order-btn:disabled {
  background: #9ca3af;
  color: #ffffff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Mobile specific styles for the stepped flow */
@media (max-width: 768px) {
  .mobile-checkout-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-checkout-content.open {
    transform: translateY(0);
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .mobile-checkout-header {
    padding: 16px 20px;
  }

  .mobile-checkout-body {
    padding: 20px;
  }

  .mobile-checkout-footer {
    padding: 20px;
  }

  .mobile-form-row {
    grid-template-columns: 1fr;
  }

  .mobile-checkout-nav {
    margin-bottom: 0px;
  }

  .mobile-checkout-title p {
    font-size: 16px;
  }

  .progress-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .progress-step span {
    font-size: 11px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .mobile-checkout-overlay,
  .mobile-checkout-content,
  .mobile-checkout-back,
  .mobile-checkout-close,
  .mobile-checkout-quantity-btn,
  .mobile-checkout-remove-btn,
  .mobile-checkout-continue-btn,
  .mobile-checkout-place-order-btn,
  .progress-circle,
  .progress-step span {
    transition: none;
  }

  .mobile-loading-spinner {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .mobile-checkout-content {
    background: var(--background-color, #ffffff);
    color: #f9fafb;
  }

  .mobile-checkout-header {
    background: var(--background-color, #ffffff);
    border-bottom-color: #374151;
  }

  .mobile-checkout-title p {
    color: #f9fafb;
  }

  .mobile-checkout-back,
  .mobile-checkout-close {
    background: var(--background-color, #ffffff);
    color: #9ca3af;
  }

  .mobile-checkout-back:hover,
  .mobile-checkout-close:hover {
    background: #4b5563;
    color: #d1d5db;
  }

  .progress-circle {
    background: var(--background-color, #ffffff);
    color: #9ca3af;
  }

  .progress-step span {
    color: #9ca3af;
  }

  .mobile-checkout-empty p {
    color: #f3f4f6;
  }

  .mobile-checkout-empty {
    color: #9ca3af;
  }

  .mobile-checkout-item {
    background: var(--hover-color);
    border-color: #4b5563;
  }

  .mobile-checkout-item-name {
    color: #f9fafb;
  }

  .mobile-checkout-item-price {
    color: #9ca3af;
  }

  .mobile-checkout-item-image {
    background: var(--background-color, #ffffff);
    border-color: #4b5563;
  }

  .mobile-checkout-quantity-controls {
    background: var(--background-color, #ffffff);
    border-color: #4b5563;
  }

  .mobile-checkout-quantity-display {
    background: var(--background-color, #ffffff);
    border-left-color: #4b5563;
    border-right-color: #4b5563;
    color: #f9fafb;
  }

  .mobile-form-field label {
    color: #d1d5db;
  }

  .mobile-form-field input {
    background: var(--background-color, #ffffff);
    border-color: #4b5563;
    color: #f9fafb;
  }

  .mobile-form-field input:focus {
    border-color: var(--primary-color, #065f46);
    box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.2);
  }

  .mobile-checkout-total {
    background: var(--hover-color);
    border-color: #4b5563;
  }

  .mobile-checkout-total-label {
    color: #d1d5db;
  }

  .mobile-checkout-footer {
    background: var(--background-color, #ffffff);
    border-top-color: var(--background-color, #ffffff);
  }

  .mobile-checkout-loading p {
    color: #f3f4f6;
  }

  .mobile-checkout-loading {
    color: #9ca3af;
  }

  .mobile-loading-spinner {
    border-color: #4b5563;
    border-top-color: var(--primary-color, #065f46);
  }
}
