import React, { useState, useEffect } from "react";
import {
  FaTimes,
  FaArrowLeft,
  FaCheck,
  FaPlus,
  FaMinus,
  FaTrash,
  FaShoppingCart,
} from "react-icons/fa";
import { useCart } from "../../views/ChatWidget/CartContext";
import { useAuth } from "../../contexts/AuthContext";
import { fetchCustomerProfile } from "../../utils/api";
import "./MobileCheckout.css";

interface CheckoutFormData {
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  birth_date: string;
  couponCode: string;
}

interface MobileCheckoutProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const MobileCheckout: React.FC<MobileCheckoutProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { cart, updateQuantity, removeFromCart, handleCheckout } = useCart();
  const { user, isAuthenticated } = useAuth();

  const [currentStep, setCurrentStep] = useState<"form" | "loading">("form");
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [formData, setFormData] = useState<CheckoutFormData>({
    email: "",
    phone: "",
    firstName: "",
    lastName: "",
    birth_date: "",
    couponCode: "",
  });

  // Calculate totals
  const totalItems = Object.values(cart).reduce(
    (sum, { quantity }) => sum + quantity,
    0
  );
  const totalPrice = Object.values(cart).reduce(
    (sum, { product, quantity }) => sum + product.latest_price * quantity,
    0
  );

  // Handle mobile detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Handle body scroll prevention
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
      document.body.style.top = `-${window.scrollY}px`;
      setIsAnimating(true);
    } else {
      const scrollY = document.body.style.top;
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";

      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || "0") * -1);
      }

      setIsAnimating(false);
      setCurrentStep("form");
    }

    return () => {
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
      document.body.style.top = "";
    };
  }, [isOpen]);

  // Pre-populate form with user data
  useEffect(() => {
    const populateForm = async () => {
      if (!isAuthenticated || !user) return;

      try {
        // Get Firebase token instead of authToken property
        const userToken = await user.getIdToken();
        if (userToken) {
          const customerProfile = await fetchCustomerProfile(userToken);
          if (customerProfile) {
            setFormData({
              email: customerProfile.email || user.email || "",
              phone: customerProfile.phone || "",
              firstName:
                customerProfile.data?.first_name ||
                user.displayName?.split(" ")[0] ||
                "",
              lastName:
                customerProfile.data?.last_name ||
                user.displayName?.split(" ").slice(1).join(" ") ||
                "",
              birth_date: customerProfile.birth_date
                ? customerProfile.birth_date.split("T")[0]
                : "",
              couponCode: "",
            });
          }
        } else {
          setFormData({
            email: user.email || "",
            phone: "",
            firstName: user.displayName?.split(" ")[0] || "",
            lastName: user.displayName?.split(" ").slice(1).join(" ") || "",
            birth_date: "",
            couponCode: "",
          });
        }
      } catch (error) {
        console.log("Error fetching customer profile:", error);
        setFormData({
          email: user.email || "",
          phone: "",
          firstName: user.displayName?.split(" ")[0] || "",
          lastName: user.displayName?.split(" ").slice(1).join(" ") || "",
          birth_date: "",
          couponCode: "",
        });
      }
    };

    if (isOpen && isAuthenticated && user) {
      populateForm();
    }
  }, [isOpen, isAuthenticated, user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (totalItems === 0) return;

    setIsSubmitting(true);
    setCurrentStep("loading");

    try {
      const success = await handleCheckout({
        email: formData.email,
        phone: formData.phone,
        firstName: formData.firstName,
        lastName: formData.lastName,
        name: `${formData.firstName} ${formData.lastName}`.trim(),
        birth_date: formData.birth_date,
        couponCode: formData.couponCode,
      });

      if (success) {
        onSuccess();
        onClose();
        // Clear cart after successful order
        Object.keys(cart).forEach((productId) => removeFromCart(productId));
        // Reset form
        setFormData({
          email: "",
          phone: "",
          firstName: "",
          lastName: "",
          birth_date: "",
          couponCode: "",
        });
      } else {
        alert("Failed to place order. Please try again.");
        setCurrentStep("form");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      alert("An error occurred during checkout. Please try again.");
      setCurrentStep("form");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid =
    formData.email.includes("@") &&
    formData.firstName.trim() &&
    formData.lastName.trim() &&
    formData.phone.trim() &&
    formData.birth_date.trim();

  if (!isOpen && !isAnimating) return null;

  // Desktop: Show traditional checkout modal (single step)
  if (!isMobile) {
    return (
      <div className={`bakedbot-checkout-modal ${isOpen ? "open" : ""}`}>
        <div className="bakedbot-modal-overlay" onClick={onClose} />
        <div className="bakedbot-modal-content">
          <div className="bakedbot-modal-header">
            <p>Checkout</p>
            <button
              className="modal-close-btn"
              onClick={onClose}
              aria-label="Close checkout modal"
            >
              <FaTimes />
            </button>
          </div>
          <div className="bakedbot-modal-body">
            <div className="bakedbot-checkout-layout">
              {/* Order Summary */}
              <div className="bakedbot-order-summary">
                <p>Order Summary</p>
                <div className="bakedbot-order-items">
                  {Object.entries(cart).map(
                    ([productId, { product, quantity }]) => (
                      <div key={productId} className="bakedbot-order-item">
                        <img
                          src={product.image_url}
                          alt={product.product_name}
                          className="bakedbot-order-item-image"
                        />
                        <div className="bakedbot-item-details">
                          <div className="bakedbot-item-name">
                            {product.product_name}
                          </div>
                          <div className="bakedbot-item-price">
                            ${product.latest_price}
                          </div>
                        </div>
                        <div className="bakedbot-item-quantity">
                          <div className="bakedbot-quantity-controls">
                            <button
                              onClick={() => updateQuantity(productId, -1)}
                            >
                              <FaMinus />
                            </button>
                            <span>{quantity}</span>
                            <button
                              onClick={() => updateQuantity(productId, 1)}
                            >
                              <FaPlus />
                            </button>
                          </div>
                        </div>
                      </div>
                    )
                  )}
                </div>
                <div className="bakedbot-order-total">
                  <div className="bakedbot-total-line">
                    <span>Total:</span>
                    <span>${totalPrice.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div className="bakedbot-customer-info">
                <form
                  onSubmit={handleSubmit}
                  className="bakedbot-checkout-form"
                >
                  <div className="bakedbot-form-title">
                    <p>Customer Information</p>
                  </div>
                  <div className="bakedbot-form-row">
                    <div className="bakedbot-form-field">
                      <label htmlFor="firstName">First Name</label>
                      <input
                        id="firstName"
                        type="text"
                        value={formData.firstName}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            firstName: e.target.value,
                          })
                        }
                        required
                      />
                    </div>
                    <div className="bakedbot-form-field">
                      <label htmlFor="lastName">Last Name</label>
                      <input
                        id="lastName"
                        type="text"
                        value={formData.lastName}
                        onChange={(e) =>
                          setFormData({ ...formData, lastName: e.target.value })
                        }
                        required
                      />
                    </div>
                  </div>
                  <div className="bakedbot-form-field">
                    <label htmlFor="email">Email</label>
                    <input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        setFormData({ ...formData, email: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="bakedbot-form-field">
                    <label htmlFor="phone">Phone</label>
                    <input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) =>
                        setFormData({ ...formData, phone: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="bakedbot-form-row">
                    <div className="bakedbot-form-field">
                      <label htmlFor="birth_date">Date of Birth</label>
                      <input
                        id="birth_date"
                        type="date"
                        value={formData.birth_date}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            birth_date: e.target.value,
                          })
                        }
                        required
                      />
                    </div>
                  </div>
                  <div className="bakedbot-form-field">
                    <label htmlFor="couponCode">Coupon Code (optional)</label>
                    <input
                      id="couponCode"
                      type="text"
                      value={formData.couponCode}
                      onChange={(e) =>
                        setFormData({ ...formData, couponCode: e.target.value })
                      }
                    />
                  </div>
                  <button
                    type="submit"
                    className="bakedbot-place-order-btn"
                    disabled={isSubmitting || !isFormValid}
                  >
                    {isSubmitting
                      ? "Placing Order..."
                      : `Place Order - $${totalPrice.toFixed(2)}`}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Mobile: Show stepped checkout flow
  return (
    <div className={`mobile-checkout-overlay ${isOpen ? "open" : ""}`}>
      {/* Backdrop */}
      <div className="mobile-checkout-backdrop" onClick={onClose} />

      {/* Checkout Content */}
      <div className={`mobile-checkout-content ${isOpen ? "open" : ""}`}>
        {/* Header */}
        <div className="mobile-checkout-header">
          <div className="mobile-checkout-nav">
            <button className="mobile-checkout-back" onClick={onClose}>
              <FaArrowLeft />
            </button>
            <div className="mobile-checkout-title">
              <p>
                {currentStep === "form" && "Customer Information"}
                {currentStep === "loading" && "Processing Order..."}
              </p>
            </div>
            <div className="mobile-checkout-close" onClick={onClose}>
              <FaTimes />
            </div>
          </div>
        </div>

        {/* Body */}
        <div className="mobile-checkout-body">
          {currentStep === "form" && (
            <form onSubmit={handleSubmit} className="mobile-checkout-form">
              <div className="mobile-form-group">
                <div className="mobile-form-row">
                  <div className="mobile-form-field">
                    <label htmlFor="firstName">First Name</label>
                    <input
                      id="firstName"
                      type="text"
                      value={formData.firstName}
                      onChange={(e) =>
                        setFormData({ ...formData, firstName: e.target.value })
                      }
                      required
                    />
                  </div>
                  <div className="mobile-form-field">
                    <label htmlFor="lastName">Last Name</label>
                    <input
                      id="lastName"
                      type="text"
                      value={formData.lastName}
                      onChange={(e) =>
                        setFormData({ ...formData, lastName: e.target.value })
                      }
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="mobile-form-group">
                <div className="mobile-form-field">
                  <label htmlFor="email">Email Address</label>
                  <input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) =>
                      setFormData({ ...formData, email: e.target.value })
                    }
                    required
                  />
                </div>
              </div>

              <div className="mobile-form-group">
                <div className="mobile-form-field">
                  <label htmlFor="phone">Phone Number</label>
                  <input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    required
                  />
                </div>
              </div>

              <div className="mobile-form-group">
                <div className="mobile-form-field">
                  <label htmlFor="birth_date">Date of Birth</label>
                  <input
                    id="birth_date"
                    type="date"
                    value={formData.birth_date}
                    onChange={(e) =>
                      setFormData({ ...formData, birth_date: e.target.value })
                    }
                    required
                  />
                </div>
              </div>

              <div className="mobile-form-group">
                <div className="mobile-form-field">
                  <label htmlFor="couponCode">Coupon Code (Optional)</label>
                  <input
                    id="couponCode"
                    type="text"
                    value={formData.couponCode}
                    onChange={(e) =>
                      setFormData({ ...formData, couponCode: e.target.value })
                    }
                  />
                </div>
              </div>
            </form>
          )}

          {currentStep === "loading" && (
            <div className="mobile-checkout-loading">
              <div className="mobile-loading-spinner"></div>
              <p>Processing your order...</p>
              <p>Please wait while we process your order</p>
            </div>
          )}
        </div>

        {/* Footer */}
        {currentStep !== "loading" && totalItems > 0 && (
          <div className="mobile-checkout-footer">
            {currentStep === "form" && (
              <button
                className="mobile-checkout-place-order-btn"
                onClick={handleSubmit}
                disabled={isSubmitting || !isFormValid}
              >
                {isSubmitting
                  ? "Placing Order..."
                  : `Place Order - $${totalPrice.toFixed(2)}`}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileCheckout;
