/* Orders Modal Overlay */
.bakedbot-orders-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Main Modal Container */
.bakedbot-orders-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.bakedbot-order-detail-modal {
  max-width: 900px;
}

/* Modal Header */
.bakedbot-orders-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.bakedbot-orders-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.bakedbot-close-button,
.bakedbot-back-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.bakedbot-close-button:hover,
.bakedbot-back-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* Modal Content */
.bakedbot-orders-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

/* Loading State */
.bakedbot-orders-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  color: #6b7280;
}

.bakedbot-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.bakedbot-small-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f4f6;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.bakedbot-orders-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
}

.bakedbot-orders-error p {
  color: #ef4444;
  margin-bottom: 16px;
  font-size: 16px;
}

.bakedbot-retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.bakedbot-retry-button:hover {
  background-color: #2563eb;
}

/* Welcome State for New Users */
.bakedbot-orders-welcome {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.bakedbot-orders-welcome .bakedbot-welcome-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.bakedbot-orders-welcome h3 {
  color: #111827;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
}

.bakedbot-orders-welcome p {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.bakedbot-orders-welcome p:last-of-type {
  margin-bottom: 24px;
}

.bakedbot-start-shopping-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.bakedbot-start-shopping-button:hover {
  background-color: #2563eb;
}

/* Empty State */
.bakedbot-orders-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  text-align: center;
}

.bakedbot-empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.bakedbot-orders-empty h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 18px;
  font-weight: 600;
}

.bakedbot-orders-empty p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Orders List */
.bakedbot-orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bakedbot-order-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.bakedbot-order-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.bakedbot-order-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bakedbot-order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.bakedbot-order-id {
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.bakedbot-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bakedbot-order-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bakedbot-order-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.bakedbot-order-meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6b7280;
  font-size: 14px;
}

.bakedbot-order-meta-item svg {
  width: 14px;
  height: 14px;
}

.bakedbot-order-items-preview {
  color: #374151;
  font-size: 14px;
  line-height: 1.4;
}

.bakedbot-item-preview {
  color: #374151;
}

.bakedbot-more-items {
  color: #6b7280;
  font-weight: 500;
}

.bakedbot-order-actions {
  color: #9ca3af;
  font-size: 18px;
  margin-left: auto;
}

/* Order Detail View */
.bakedbot-order-detail-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.bakedbot-order-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.bakedbot-order-detail-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bakedbot-order-status {
  margin-bottom: 8px;
}

.bakedbot-order-items h3,
.bakedbot-order-shipping h3 {
  margin: 0 0 16px 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
}

.bakedbot-order-item-details {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.bakedbot-order-item-details:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.bakedbot-order-item-details h4 {
  margin: 0 0 4px 0;
  color: #111827;
  font-size: 16px;
  font-weight: 600;
}

.bakedbot-order-item-details p {
  margin: 0 0 8px 0;
  color: #6b7280;
  font-size: 14px;
}

.bakedbot-order-item-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  font-size: 14px;
  color: #374151;
}

.bakedbot-order-item-total {
  font-weight: 600;
  color: #111827;
}

.bakedbot-shipping-address {
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.bakedbot-shipping-address p {
  margin: 0 0 4px 0;
  color: #374151;
}

.bakedbot-shipping-address p:last-child {
  margin-bottom: 0;
}

/* Pagination */
.bakedbot-orders-pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.bakedbot-load-more-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bakedbot-load-more-button:hover:not(:disabled) {
  background-color: #2563eb;
}

.bakedbot-load-more-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .bakedbot-orders-overlay {
    padding: 16px;
  }

  .bakedbot-orders-modal {
    max-height: 95vh;
  }

  .bakedbot-orders-header {
    padding: 16px 20px;
  }

  .bakedbot-orders-header h2 {
    font-size: 1.25rem;
  }

  .bakedbot-orders-content {
    padding: 20px;
  }

  .bakedbot-order-detail-content {
    padding: 20px;
  }

  .bakedbot-order-meta {
    flex-direction: column;
    gap: 8px;
  }

  .bakedbot-order-item-meta {
    flex-direction: column;
    gap: 8px;
  }

  .bakedbot-orders-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .bakedbot-back-button {
    order: -1;
    width: 100%;
    justify-content: flex-start;
  }

  .bakedbot-close-button {
    margin-left: auto;
  }
} 