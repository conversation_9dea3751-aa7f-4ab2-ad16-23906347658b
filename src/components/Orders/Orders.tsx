import React, { useState, useEffect } from "react";
import {
  FaBox,
  FaCalendar,
  FaDollarSign,
  FaMapMarkerAlt,
  FaTag,
  FaShoppingBag,
  FaExclamationTriangle,
  FaSpinner,
  FaTimes,
  FaCheckCircle,
  FaChevronLeft,
  FaChevronRight,
} from "react-icons/fa";
import {
  fetchCustomerOrders,
  CustomerOrder,
  CustomerOrdersResponse,
} from "../../utils/api";
import { useAuth } from "../../contexts/AuthContext";
import "./Orders.css";

interface OrdersProps {
  onClose: () => void;
}

export const Orders: React.FC<OrdersProps> = ({ onClose }) => {
  const [orders, setOrders] = useState<CustomerOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<CustomerOrder | null>(
    null
  );
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [prevCursor, setPrevCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadOrders();
    } else {
      setError("Please log in to view your orders");
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  const loadOrders = async (cursor?: string) => {
    try {
      if (cursor) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      // Get Firebase token using getIdToken()
      const userToken = await user?.getIdToken();

      if (!userToken) {
        throw new Error("Unable to get authentication token");
      }

      const response: CustomerOrdersResponse = await fetchCustomerOrders(
        userToken,
        cursor
      );

      if (cursor) {
        // Append to existing orders for pagination
        setOrders((prev) => [...prev, ...response.results]);
      } else {
        // Replace orders for initial load
        setOrders(response.results);
      }

      setNextCursor(response.nextCursor);
      setPrevCursor(response.prevCursor);
      setHasMore(!!response.nextCursor);
      setError(null);
    } catch (err: any) {
      console.error("Error loading orders:", err);

      // Handle specific case where user hasn't been created at this location yet
      if (
        err.response?.status === 404 &&
        err.response?.data?.error?.includes("User not found in this location")
      ) {
        setError("location_not_found");
      } else {
        setError(err.message || "Failed to load orders");
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreOrders = () => {
    if (nextCursor && !loadingMore) {
      loadOrders(nextCursor);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "#10B981";
      case "processing":
        return "#F59E0B";
      case "cancelled":
        return "#EF4444";
      case "pending":
        return "#6B7280";
      default:
        return "#6B7280";
    }
  };

  const getStatusLabel = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  if (loading) {
    return (
      <div className="bakedbot-orders-overlay">
        <div className="bakedbot-orders-modal">
          <div className="bakedbot-orders-header">
            <h2>My Orders</h2>
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>
          <div className="bakedbot-orders-loading">
            <div className="bakedbot-spinner"></div>
            <p>Loading your orders...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bakedbot-orders-overlay">
        <div className="bakedbot-orders-modal">
          <div className="bakedbot-orders-header">
            <h2>My Orders</h2>
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>
          <div className="bakedbot-orders-error">
            {error === "location_not_found" ? (
              <div className="bakedbot-orders-welcome">
                <div className="bakedbot-welcome-icon">🛍️</div>
                <h3>No Orders Yet</h3>
                <p>
                  You haven't placed any orders at this location yet. Your order
                  history will appear here once you make your first purchase.
                </p>
                <p>
                  <strong>Ready to place your first order?</strong> Browse our
                  menu and add some items to your cart!
                </p>
                <button
                  onClick={onClose}
                  className="bakedbot-start-shopping-button"
                >
                  Start Shopping
                </button>
              </div>
            ) : (
              <>
                <p>{error}</p>
                {isAuthenticated && (
                  <button
                    onClick={() => loadOrders()}
                    className="bakedbot-retry-button"
                  >
                    Try Again
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (selectedOrder) {
    return (
      <div className="bakedbot-orders-overlay">
        <div className="bakedbot-orders-modal bakedbot-order-detail-modal">
          <div className="bakedbot-orders-header">
            <button
              onClick={() => setSelectedOrder(null)}
              className="bakedbot-back-button"
            >
              <FaChevronLeft /> Back
            </button>
            <h2>Order #{selectedOrder.id}</h2>
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>

          <div className="bakedbot-order-detail-content">
            <div className="bakedbot-order-detail-header">
              <div className="bakedbot-order-detail-info">
                <div className="bakedbot-order-status">
                  <span
                    className="bakedbot-status-badge"
                    style={{
                      backgroundColor: getStatusColor(selectedOrder.status),
                    }}
                  >
                    {getStatusLabel(selectedOrder.status)}
                  </span>
                </div>
                <div className="bakedbot-order-meta">
                  <div className="bakedbot-order-meta-item">
                    <FaCalendar />
                    <span>{formatDate(selectedOrder.created_at)}</span>
                  </div>
                  <div className="bakedbot-order-meta-item">
                    <FaDollarSign />
                    <span>{formatCurrency(selectedOrder.total_amount)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bakedbot-order-items">
              <h3>Items Ordered</h3>
              {selectedOrder.items.map((item, index) => (
                <div key={index} className="bakedbot-order-item">
                  <div className="bakedbot-order-item-details">
                    <h4>{item.product_data.name}</h4>
                    <p>{item.product_data.description}</p>
                    <div className="bakedbot-order-item-meta">
                      <span>Quantity: {item.quantity}</span>
                      <span>Unit Price: {formatCurrency(item.unit_price)}</span>
                      <span className="bakedbot-order-item-total">
                        Total: {formatCurrency(item.total_price)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="bakedbot-order-shipping">
              <h3>Shipping Address</h3>
              <div className="bakedbot-shipping-address">
                <p>{selectedOrder.shipping_address.name}</p>
                <p>{selectedOrder.shipping_address.line1}</p>
                <p>
                  {selectedOrder.shipping_address.city},{" "}
                  {selectedOrder.shipping_address.state}{" "}
                  {selectedOrder.shipping_address.postal_code}
                </p>
                <p>{selectedOrder.shipping_address.country}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bakedbot-orders-overlay">
      <div className="bakedbot-orders-modal">
        <div className="bakedbot-orders-header">
          <h2>My Orders</h2>
          <button onClick={onClose} className="bakedbot-close-button">
            <FaTimes />
          </button>
        </div>

        <div className="bakedbot-orders-content">
          {orders.length === 0 ? (
            <div className="bakedbot-orders-empty">
              <FaBox className="bakedbot-empty-icon" />
              <h3>No Orders Yet</h3>
              <p>
                You haven't placed any orders yet. Start shopping to see your
                orders here!
              </p>
            </div>
          ) : (
            <>
              <div className="bakedbot-orders-list">
                {orders.map((order) => (
                  <div
                    key={order.id}
                    className="bakedbot-order-item"
                    onClick={() => setSelectedOrder(order)}
                  >
                    <div className="bakedbot-order-summary">
                      <div className="bakedbot-order-header">
                        <span className="bakedbot-order-id">
                          Order #{order.id}
                        </span>
                        <span
                          className="bakedbot-status-badge"
                          style={{
                            backgroundColor: getStatusColor(order.status),
                          }}
                        >
                          {getStatusLabel(order.status)}
                        </span>
                      </div>

                      <div className="bakedbot-order-details">
                        <div className="bakedbot-order-meta">
                          <div className="bakedbot-order-meta-item">
                            <FaCalendar />
                            <span>{formatDate(order.created_at)}</span>
                          </div>
                          <div className="bakedbot-order-meta-item">
                            <FaDollarSign />
                            <span>{formatCurrency(order.total_amount)}</span>
                          </div>
                          <div className="bakedbot-order-meta-item">
                            <FaBox />
                            <span>
                              {order.items.length} item
                              {order.items.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </div>

                        <div className="bakedbot-order-items-preview">
                          {order.items.slice(0, 2).map((item, index) => (
                            <span key={index} className="bakedbot-item-preview">
                              {item.product_data.name}
                              {index < Math.min(order.items.length, 2) - 1 &&
                                ", "}
                            </span>
                          ))}
                          {order.items.length > 2 && (
                            <span className="bakedbot-more-items">
                              +{order.items.length - 2} more
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="bakedbot-order-actions">
                      <FaChevronRight />
                    </div>
                  </div>
                ))}
              </div>

              {hasMore && (
                <div className="bakedbot-orders-pagination">
                  <button
                    onClick={loadMoreOrders}
                    disabled={loadingMore}
                    className="bakedbot-load-more-button"
                  >
                    {loadingMore ? (
                      <>
                        <div className="bakedbot-small-spinner"></div>
                        Loading...
                      </>
                    ) : (
                      "Load More Orders"
                    )}
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
