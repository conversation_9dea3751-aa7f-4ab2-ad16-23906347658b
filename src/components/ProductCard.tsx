import React from "react";
import { FaPlus, FaMinus, FaTag } from "react-icons/fa";
import { Product } from "../utils/api";

interface ProductCardProps {
  product: Product;
  allowCart?: boolean;
  onAddToCart: (product: Product) => void;
  cart: { [key: string]: { quantity: number } };
  updateQuantity: (productId: string, quantity: number) => void;
  onProductClick?: (product: Product) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  allowCart,
  onAddToCart,
  cart,
  updateQuantity,
  onProductClick,
}) => {
  // Calculate discount percentage if discount or original_price is available
  const hasDiscount =
    product.discount ||
    (product.latest_price &&
      product.original_price &&
      product.latest_price < product.original_price);
  const discountPercentage =
    product.original_price && product.latest_price
      ? Math.round(
          ((product.original_price - product.latest_price) /
            product.original_price) *
            100
        )
      : product.discount_percentage || 0;

  // Flag for products that are featured, best value, or have a discount
  const isSpecialOffer = product.featured || product.best_value || hasDiscount;

  return (
    <div
      className={`bb-sm-product-item p-[5px] flex flex-col rounded-lg overflow-hidden ${
        isSpecialOffer ? "border-2 border-[#65715F]" : ""
      }`}
    >
      <div className="relative">
        <img
          src={product.image_url}
          alt={product.product_name}
          onClick={() => onProductClick?.(product)}
          className="w-full h-full object-cover cursor-pointer"
        />
      </div>
      <div className="bb-sm-product-item-content">
        {/* {product.brand_name && (
          <p className="bb-sm-product-brand">{product.brand_name}</p>
        )} */}
        <p
          className="font-medium text-md cursor-pointer mb-1 line-clamp-2"
          onClick={() => onProductClick?.(product)}
        >
          {product.product_name}
        </p>
        <p className="mb-1 flex justify-between">
          <span>{product.category}</span>
          {product.display_weight && (
            <span className="text-xs bg-gray-100 rounded-full px-2 py-0.5">
              {product.display_weight}
            </span>
          )}
        </p>
        <div className="font-medium text-md mb-2 flex items-center">
          <span className="text-[#65715F] font-bold">
            ${(product.price || product.latest_price)?.toFixed(2)}
          </span>
          {hasDiscount && product.original_price && (
            <span className="text-gray-500 text-sm line-through ml-2">
              ${product.original_price.toFixed(2)}
            </span>
          )}
        </div>
        <p className="text-sm mb-3 line-clamp-2">{product.description}</p>

        {allowCart && (
          <>
            {cart[product.product_id || product.id] ? (
              <div className="py-2 bb-sm-quantity-selector flex items-center justify-between gap-3 mt-auto rounded">
                <button
                  onClick={() =>
                    updateQuantity(product.product_id || product.id, -1)
                  }
                  className="bb-sm-quantity-button w-8 h-8 rounded-full flex items-center justify-center"
                >
                  <FaMinus size={10} />
                </button>
                <span className="text-lg">
                  {String(
                    cart[product.product_id || product.id].quantity
                  )?.padStart(2, "0")}
                </span>
                <button
                  onClick={() =>
                    updateQuantity(product.product_id || product.id, 1)
                  }
                  className="bb-sm-quantity-button w-8 h-8 rounded-full flex items-center justify-center"
                >
                  <FaPlus size={10} />
                </button>
              </div>
            ) : (
              <button
                className={`w-full py-1 ${
                  isSpecialOffer
                    ? "bb-sm-special-add-button bg-[#65715F] text-white"
                    : "bb-sm-add-to-cart-button"
                }`}
                onClick={() => onAddToCart(product)}
              >
                {isSpecialOffer ? "GRAB DEAL" : "Add to cart"}
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
