/* Profile Modal Overlay */
.bakedbot-profile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* Main Modal Container */
.bakedbot-profile-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Modal Header */
.bakedbot-profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.bakedbot-profile-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.bakedbot-profile-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bakedbot-close-button,
.bakedbot-edit-button,
.bakedbot-save-button,
.bakedbot-cancel-button {
  background: none;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.bakedbot-edit-button {
  background-color: #3b82f6;
  color: white;
}

.bakedbot-edit-button:hover {
  background-color: #2563eb;
}

.bakedbot-save-button {
  background-color: #10b981;
  color: white;
}

.bakedbot-save-button:hover {
  background-color: #059669;
}

.bakedbot-cancel-button {
  background-color: #6b7280;
  color: white;
}

.bakedbot-cancel-button:hover {
  background-color: #4b5563;
}

.bakedbot-close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* Modal Content */
.bakedbot-profile-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Loading State */
.bakedbot-profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 24px;
  color: #6b7280;
}

.bakedbot-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.bakedbot-profile-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.bakedbot-profile-error p {
  color: #ef4444;
  margin-bottom: 16px;
  font-size: 16px;
}

.bakedbot-retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.bakedbot-retry-button:hover {
  background-color: #2563eb;
}

/* Welcome State for New Users */
.bakedbot-profile-welcome {
  text-align: center;
  padding: 40px 20px;
  max-width: 500px;
  margin: 0 auto;
}

.bakedbot-welcome-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.bakedbot-profile-welcome h3 {
  color: #111827;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  border: none;
  padding: 0;
}

.bakedbot-profile-welcome p {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.bakedbot-profile-welcome p:last-of-type {
  margin-bottom: 24px;
}

.bakedbot-continue-button {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.bakedbot-continue-button:hover {
  background-color: #059669;
}

/* Profile Sections */
.bakedbot-profile-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.bakedbot-profile-section h3 {
  margin: 0;
  color: #111827;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

/* Profile Grid */
.bakedbot-profile-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

/* Profile Fields */
.bakedbot-profile-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bakedbot-profile-field label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
}

.bakedbot-profile-field label svg {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.bakedbot-profile-value {
  padding: 12px 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  color: #111827;
  font-size: 14px;
  line-height: 1.4;
}

.bakedbot-profile-id {
  font-family: 'Courier New', monospace;
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
  font-weight: 600;
}

.bakedbot-profile-external-id {
  font-family: 'Courier New', monospace;
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
  font-weight: 600;
  word-break: break-all;
}

/* Profile Input */
.bakedbot-profile-input {
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #111827;
  background: white;
  transition: border-color 0.2s;
}

.bakedbot-profile-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.bakedbot-profile-input::placeholder {
  color: #9ca3af;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .bakedbot-profile-overlay {
    padding: 16px;
  }

  .bakedbot-profile-modal {
    max-height: 95vh;
  }

  .bakedbot-profile-header {
    padding: 16px 20px;
    flex-wrap: wrap;
    gap: 12px;
  }

  .bakedbot-profile-header h2 {
    font-size: 1.25rem;
    width: 100%;
  }

  .bakedbot-profile-actions {
    width: 100%;
    justify-content: space-between;
  }

  .bakedbot-profile-content {
    padding: 20px;
    gap: 24px;
  }

  .bakedbot-profile-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .bakedbot-profile-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .bakedbot-edit-button,
  .bakedbot-save-button,
  .bakedbot-cancel-button {
    padding: 8px 16px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .bakedbot-profile-overlay {
    padding: 12px;
  }

  .bakedbot-profile-header {
    padding: 12px 16px;
  }

  .bakedbot-profile-content {
    padding: 16px;
  }

  .bakedbot-profile-field label {
    font-size: 13px;
  }

  .bakedbot-profile-value,
  .bakedbot-profile-input {
    font-size: 13px;
    padding: 10px 12px;
  }

  .bakedbot-profile-actions {
    flex-direction: column;
    width: 100%;
  }

  .bakedbot-edit-button,
  .bakedbot-save-button,
  .bakedbot-cancel-button,
  .bakedbot-close-button {
    width: 100%;
    justify-content: center;
    padding: 10px;
  }
} 