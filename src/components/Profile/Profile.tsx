import React, { useState, useEffect } from "react";
import {
  <PERSON>a<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaTimes,
  FaEdit,
  FaSave,
  FaCalendar,
  FaGlobe,
  FaClock,
} from "react-icons/fa";
import { fetchCustomerProfile, CustomerProfile } from "../../utils/api";
import { useAuth } from "../../contexts/AuthContext";
import "./Profile.css";

interface ProfileProps {
  onClose: () => void;
}

export const Profile: React.FC<ProfileProps> = ({ onClose }) => {
  const [profile, setProfile] = useState<CustomerProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    birth_date: "",
  });

  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadProfile();
    } else {
      setError("Please log in to view your profile");
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  const loadProfile = async () => {
    try {
      setLoading(true);

      // Get Firebase token using getIdToken()
      const userToken = await user?.getIdToken();

      if (!userToken) {
        throw new Error("Unable to get authentication token");
      }

      const profileData = await fetchCustomerProfile(userToken);
      setProfile(profileData);

      // Initialize edit form with current data
      setEditForm({
        first_name: profileData.data.first_name || "",
        last_name: profileData.data.last_name || "",
        email: profileData.email || "",
        phone: profileData.phone || "",
        birth_date: profileData.birth_date || "",
      });

      setError(null);
    } catch (err: any) {
      console.error("Error loading profile:", err);

      // Handle specific case where user hasn't been created at this location yet
      if (
        err.response?.status === 404 &&
        err.response?.data?.error?.includes("User not found in this location")
      ) {
        setError("location_not_found");
      } else {
        setError(err.message || "Failed to load profile");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Cancel editing - reset form to original values
      if (profile) {
        setEditForm({
          first_name: profile.data.first_name || "",
          last_name: profile.data.last_name || "",
          email: profile.email || "",
          phone: profile.phone || "",
          birth_date: profile.birth_date || "",
        });
      }
    }
    setIsEditing(!isEditing);
  };

  const handleInputChange = (field: string, value: string) => {
    setEditForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      // Note: This would require an API endpoint for updating profile
      // For now, we'll just show a message that the feature is coming soon
      alert("Profile editing feature coming soon!");
      setIsEditing(false);
    } catch (err: any) {
      console.error("Error saving profile:", err);
      alert("Failed to save profile changes");
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Not provided";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="bakedbot-profile-overlay">
        <div className="bakedbot-profile-modal">
          <div className="bakedbot-profile-header">
            <h2>My Profile</h2>
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>
          <div className="bakedbot-profile-loading">
            <div className="bakedbot-spinner"></div>
            <p>Loading your profile...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bakedbot-profile-overlay">
        <div className="bakedbot-profile-modal">
          <div className="bakedbot-profile-header">
            <h2>My Profile</h2>
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>
          <div className="bakedbot-profile-error">
            {error === "location_not_found" ? (
              <div className="bakedbot-profile-welcome">
                <div className="bakedbot-welcome-icon">👋</div>
                <h3>Welcome!</h3>
                <p>
                  It looks like this is your first time visiting this location.
                  Your customer profile will be created automatically when you
                  place your first order.
                </p>
                <p>
                  <strong>Ready to get started?</strong> Browse our menu and
                  place an order to create your profile and start earning
                  rewards!
                </p>
                <button onClick={onClose} className="bakedbot-continue-button">
                  Continue Shopping
                </button>
              </div>
            ) : (
              <>
                <p>{error}</p>
                {isAuthenticated && (
                  <button
                    onClick={loadProfile}
                    className="bakedbot-retry-button"
                  >
                    Try Again
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return null;
  }

  return (
    <div className="bakedbot-profile-overlay">
      <div className="bakedbot-profile-modal">
        <div className="bakedbot-profile-header">
          <h2>My Profile</h2>
          <div className="bakedbot-profile-actions">
            {/* {isEditing ? (
              <>
                <button onClick={handleSave} className="bakedbot-save-button">
                  <FaSave /> Save
                </button>
                <button
                  onClick={handleEditToggle}
                  className="bakedbot-cancel-button"
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                onClick={handleEditToggle}
                className="bakedbot-edit-button"
              >
                <FaEdit /> Edit
              </button>
            )} */}
            <button onClick={onClose} className="bakedbot-close-button">
              <FaTimes />
            </button>
          </div>
        </div>

        <div className="bakedbot-profile-content">
          <div className="bakedbot-profile-section">
            <h3>Personal Information</h3>

            <div className="bakedbot-profile-grid">
              <div className="bakedbot-profile-field">
                <label>
                  <FaUser />
                  <span>First Name</span>
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.first_name}
                    onChange={(e) =>
                      handleInputChange("first_name", e.target.value)
                    }
                    className="bakedbot-profile-input"
                    placeholder="Enter first name"
                  />
                ) : (
                  <div className="bakedbot-profile-value">
                    {profile.data.first_name || "Not provided"}
                  </div>
                )}
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaUser />
                  <span>Last Name</span>
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editForm.last_name}
                    onChange={(e) =>
                      handleInputChange("last_name", e.target.value)
                    }
                    className="bakedbot-profile-input"
                    placeholder="Enter last name"
                  />
                ) : (
                  <div className="bakedbot-profile-value">
                    {profile.data.last_name || "Not provided"}
                  </div>
                )}
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaEnvelope />
                  <span>Email</span>
                </label>
                {isEditing ? (
                  <input
                    type="email"
                    value={editForm.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="bakedbot-profile-input"
                    placeholder="Enter email address"
                  />
                ) : (
                  <div className="bakedbot-profile-value">
                    {profile.email || "Not provided"}
                  </div>
                )}
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaPhone />
                  <span>Phone</span>
                </label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={editForm.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="bakedbot-profile-input"
                    placeholder="Enter phone number"
                  />
                ) : (
                  <div className="bakedbot-profile-value">
                    {profile.phone || "Not provided"}
                  </div>
                )}
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaCalendar />
                  <span>Birth Date</span>
                </label>
                {isEditing ? (
                  <input
                    type="date"
                    value={editForm.birth_date}
                    onChange={(e) =>
                      handleInputChange("birth_date", e.target.value)
                    }
                    className="bakedbot-profile-input"
                  />
                ) : (
                  <div className="bakedbot-profile-value">
                    {formatDate(profile.birth_date)}
                  </div>
                )}
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaGlobe />
                  <span>Locale</span>
                </label>
                <div className="bakedbot-profile-value">
                  {profile.locale || "Not set"}
                </div>
              </div>
            </div>
          </div>

          <div className="bakedbot-profile-section">
            <h3>Account Information</h3>

            <div className="bakedbot-profile-grid">
              <div className="bakedbot-profile-field">
                <label>
                  <span>Customer ID</span>
                </label>
                <div className="bakedbot-profile-value bakedbot-profile-id">
                  #{profile.id}
                </div>
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <FaClock />
                  <span>Timezone</span>
                </label>
                <div className="bakedbot-profile-value">
                  {profile.timezone || "Not set"}
                </div>
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <span>Member Since</span>
                </label>
                <div className="bakedbot-profile-value">
                  {formatDateTime(profile.created_at)}
                </div>
              </div>

              <div className="bakedbot-profile-field">
                <label>
                  <span>Last Updated</span>
                </label>
                <div className="bakedbot-profile-value">
                  {formatDateTime(profile.updated_at)}
                </div>
              </div>
            </div>
          </div>

          {profile.external_id && (
            <div className="bakedbot-profile-section">
              <h3>External Accounts</h3>
              <div className="bakedbot-profile-field">
                <label>
                  <span>External ID</span>
                </label>
                <div className="bakedbot-profile-value bakedbot-profile-external-id">
                  {profile.external_id}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
