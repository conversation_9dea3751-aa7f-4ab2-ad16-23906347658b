import React from "react";
import { useAuth } from "../../contexts/AuthContext";
import BakedBotHeading from "../BakedBotHeading";

interface ProfileCardProps {
  onClose?: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ onClose }) => {
  const { user, logout } = useAuth();

  if (!user) {
    return (
      <div className="p-4 text-center">Please log in to view your profile.</div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header with user info */}
      <div className="bg-gradient-to-r from-primary-color to-secondary-color p-4">
        <div className="flex items-center">
          <div className="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center mr-4">
            <span className="text-2xl">
              {(user.displayName || user.email || "U")[0].toUpperCase()}
            </span>
          </div>
          <div>
            <BakedBotHeading level={2} className="text-xl font-bold">
              {user.displayName || "User"}
            </BakedBotHeading>
            <p className="text-sm opacity-90">
              {user.email || "No email provided"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileCard;
