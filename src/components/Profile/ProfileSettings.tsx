import React, { useState } from "react";
import { useAuth } from "../../contexts/AuthContext";
import { usePreferences } from "../../contexts/PreferencesContext";
import ProfileCard from "./ProfileCard";
import BakedBotHeading from "../BakedBotHeading";

interface ProfileSettingsProps {
  onClose?: () => void;
}

const ProfileSettings: React.FC<ProfileSettingsProps> = ({ onClose }) => {
  const { user, logout } = useAuth();
  const { preferences, updatePreferences, syncPreferences, resetPreferences } =
    usePreferences();

  const handleSyncPreferences = async () => {
    try {
      const success = await syncPreferences();
      if (success) {
        console.log("Preferences synced successfully!");
      } else {
        console.error("Failed to sync preferences.");
      }
    } catch (error) {
      console.error("Error syncing preferences:", error);
    }
  };

  // Helper to update specific preferences
  const updateSetting = (category: string, setting: string, value: any) => {
    updatePreferences({
      [category]: {
        ...(preferences[category] || {}),
        [setting]: value,
      },
    });
  };

  return (
    <div className="w-full max-w-lg mx-auto">
      <div className="mb-6">
        <ProfileCard />
      </div>

      {/* Preferences Settings */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="bg-secondary-color p-3 text-white">
          <BakedBotHeading level={3} className="text-lg font-semibold">
            Preferences
          </BakedBotHeading>
        </div>

        <div className="p-4">
          {/* Display Settings */}
          <div className="mb-4">
            <BakedBotHeading
              level={4}
              className="text-md font-medium text-gray-800 mb-2"
            >
              Display Settings
            </BakedBotHeading>

            <div className="mb-3">
              <label className="block text-sm text-gray-600 mb-1">
                Font Size
              </label>
              <select
                value={preferences.display?.fontSize || "medium"}
                onChange={(e) =>
                  updateSetting("display", "fontSize", e.target.value)
                }
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </select>
            </div>

            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="compact-mode"
                checked={preferences.display?.compactMode || false}
                onChange={(e) =>
                  updateSetting("display", "compactMode", e.target.checked)
                }
                className="mr-2"
              />
              <label htmlFor="compact-mode" className="text-sm text-gray-600">
                Compact Mode
              </label>
            </div>
          </div>

          {/* Chat Settings */}
          <div className="mb-4">
            <BakedBotHeading
              level={4}
              className="text-md font-medium text-gray-800 mb-2"
            >
              Chat Settings
            </BakedBotHeading>

            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="show-timestamps"
                checked={preferences.chat?.showTimestamps || false}
                onChange={(e) =>
                  updateSetting("chat", "showTimestamps", e.target.checked)
                }
                className="mr-2"
              />
              <label
                htmlFor="show-timestamps"
                className="text-sm text-gray-600"
              >
                Show Message Timestamps
              </label>
            </div>

            <div className="mb-3">
              <label className="block text-sm text-gray-600 mb-1">
                Message Size
              </label>
              <select
                value={preferences.chat?.messageSize || "medium"}
                onChange={(e) =>
                  updateSetting("chat", "messageSize", e.target.value)
                }
                className="w-full p-2 border border-gray-300 rounded"
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </select>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="mb-4">
            <BakedBotHeading
              level={4}
              className="text-md font-medium text-gray-800 mb-2"
            >
              Notification Settings
            </BakedBotHeading>

            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="notifications-enabled"
                checked={preferences.notifications?.enabled || false}
                onChange={(e) =>
                  updateSetting("notifications", "enabled", e.target.checked)
                }
                className="mr-2"
              />
              <label
                htmlFor="notifications-enabled"
                className="text-sm text-gray-600"
              >
                Enable Notifications
              </label>
            </div>

            {preferences.notifications?.enabled && (
              <div className="flex items-center mb-3 ml-5">
                <input
                  type="checkbox"
                  id="email-notifications"
                  checked={
                    preferences.notifications?.emailNotifications || false
                  }
                  onChange={(e) =>
                    updateSetting(
                      "notifications",
                      "emailNotifications",
                      e.target.checked
                    )
                  }
                  className="mr-2"
                />
                <label
                  htmlFor="email-notifications"
                  className="text-sm text-gray-600"
                >
                  Receive Email Notifications
                </label>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Sync Actions */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="bg-secondary-color p-3 text-white">
          <BakedBotHeading level={3} className="text-lg font-semibold">
            Synchronization
          </BakedBotHeading>
        </div>

        <div className="p-4">
          <div className="flex flex-col gap-3">
            <button
              onClick={handleSyncPreferences}
              className="bg-primary-color text-white rounded-md py-2 px-4 hover:bg-opacity-90 transition-colors"
            >
              Sync Preferences
            </button>

            <button
              onClick={resetPreferences}
              className="bg-gray-200 text-gray-800 rounded-md py-2 px-4 hover:bg-gray-300 transition-colors"
            >
              Reset to Default Settings
            </button>
          </div>
        </div>
      </div>

      {/* Logout Button */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div className="p-4">
          <button
            onClick={() => {
              logout && logout();
              onClose && onClose();
            }}
            className="w-full bg-red-600 text-white rounded-md py-2 px-4 hover:bg-red-700 transition-colors"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
