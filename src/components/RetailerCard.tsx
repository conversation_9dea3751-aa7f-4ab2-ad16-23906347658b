import React from "react";
import { FaMapMarkerAlt } from "react-icons/fa";
import BakedBotHeading from "./BakedBotHeading";

interface RetailerProps {
  name: string;
  city: string;
  state: string;
  latitude?: string;
  longitude?: string;
  logo?: string;
  notLoggedInIcon?: string;
  address: string;
}

const RetailerCard: React.FC<RetailerProps> = ({
  name,
  city,
  state,
  latitude,
  longitude,
  logo,
  notLoggedInIcon,
  address,
}) => {
  const handleMapClick = () => {
    if (latitude && longitude) {
      window.open(
        `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`,
        "_blank"
      );
    } else {
      // If no coordinates, search by name and location
      window.open(
        `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
          `${name} ${city} ${state}`
        )}`,
        "_blank"
      );
    }
  };

  return (
    <div className="bb-sm-retailer-card">
      <div className="bb-sm-retailer-info">
        <div className="flex flex-col items-center justify-center">
          <img
            src={logo || notLoggedInIcon}
            alt={name}
            className="w-16 h-16 mb-2 object-contain"
          />
          <BakedBotHeading level={3} className="bb-sm-retailer-name">
            {name}
          </BakedBotHeading>
          <p className="text-sm text-gray-700 text-center">{address}</p>
        </div>
      </div>
      <button
        onClick={handleMapClick}
        className="bb-sm-map-button"
        aria-label="View on map"
      >
        <FaMapMarkerAlt />
      </button>
    </div>
  );
};

export default RetailerCard;
