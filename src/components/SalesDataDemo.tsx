import React, { useState } from "react";
import TableRenderer from "./TableRenderer";
import ChartRenderer from "./ChartRenderer";
import BakedBotHeading from "./BakedBotHeading";
import {
  formatSalesRecordsAsTable,
  formatSalesDataAsBarChart,
  formatSalesDataAsLineChart,
  formatSalesDataAsPieChart,
} from "../utils/salesDataFormatter";

const SalesDataDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"table" | "bar" | "line" | "pie">(
    "table"
  );

  // Sample sales records for table
  const sampleSalesRecords = [
    {
      product_name: "Blue Dream",
      quantity: 42,
      revenue: 1260.0,
      date: "2023-04-15",
      customer: "John Smith",
      location: "San Francisco",
    },
    {
      product_name: "OG Kush",
      quantity: 28,
      revenue: 840.0,
      date: "2023-04-16",
      customer: "<PERSON>",
      location: "Los Angeles",
    },
    {
      product_name: "Sour Diesel",
      quantity: 35,
      revenue: 1050.0,
      date: "2023-04-17",
      customer: "<PERSON> Johnson",
      location: "San Diego",
    },
    {
      product_name: "Girl Scout Cookies",
      quantity: 20,
      revenue: 600.0,
      date: "2023-04-18",
      customer: "<PERSON>",
      location: "Oakland",
    },
    {
      product_name: "Purple Haze",
      quantity: 15,
      revenue: 450.0,
      date: "2023-04-19",
      customer: "Charlie Wilson",
      location: "Sacramento",
    },
  ];

  // Sample data for bar chart
  const sampleBarData = [
    { label: "Blue Dream", value: 1260 },
    { label: "OG Kush", value: 840 },
    { label: "Sour Diesel", value: 1050 },
    { label: "Girl Scout Cookies", value: 600 },
    { label: "Purple Haze", value: 450 },
  ];

  // Sample data for line chart
  const sampleLineData = [
    { date: "Jan 2023", value: 5200 },
    { date: "Feb 2023", value: 4800 },
    { date: "Mar 2023", value: 6100 },
    { date: "Apr 2023", value: 7500 },
    { date: "May 2023", value: 8200 },
    { date: "Jun 2023", value: 9100 },
  ];

  // Sample data for pie chart
  const samplePieData = [
    { label: "Flower", value: 45 },
    { label: "Pre-Rolls", value: 20 },
    { label: "Vapes", value: 15 },
    { label: "Edibles", value: 12 },
    { label: "Concentrates", value: 8 },
  ];

  // Format the data using our utility functions
  const tableData = formatSalesRecordsAsTable(
    sampleSalesRecords,
    "Recent Sales Transactions"
  );
  const barChartData = formatSalesDataAsBarChart(
    sampleBarData,
    "Revenue by Product"
  );
  const lineChartData = formatSalesDataAsLineChart(
    sampleLineData,
    "Monthly Revenue Trend"
  );
  const pieChartData = formatSalesDataAsPieChart(
    samplePieData,
    "Sales by Product Category"
  );

  // Type assertion to fix the type errors
  const getTypedChartData = (data: any) => {
    if (!data) return null;
    return {
      chartData: {
        ...data.chartData,
        type: data.chartData.type as "bar" | "line" | "pie" | "doughnut",
      },
    };
  };

  const typedBarChartData = getTypedChartData(barChartData);
  const typedLineChartData = getTypedChartData(lineChartData);
  const typedPieChartData = getTypedChartData(pieChartData);

  return (
    <div className="p-4 max-w-4xl mx-auto">
      <BakedBotHeading level={1} className="text-2xl font-bold mb-4">
        Sales Data Visualization Demo
      </BakedBotHeading>

      <div className="mb-4">
        <div className="flex border-b">
          <button
            className={`px-4 py-2 ${
              activeTab === "table"
                ? "border-b-2 border-[#65715F] text-[#65715F] font-medium"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("table")}
          >
            Table View
          </button>
          <button
            className={`px-4 py-2 ${
              activeTab === "bar"
                ? "border-b-2 border-[#65715F] text-[#65715F] font-medium"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("bar")}
          >
            Bar Chart
          </button>
          <button
            className={`px-4 py-2 ${
              activeTab === "line"
                ? "border-b-2 border-[#65715F] text-[#65715F] font-medium"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("line")}
          >
            Line Chart
          </button>
          <button
            className={`px-4 py-2 ${
              activeTab === "pie"
                ? "border-b-2 border-[#65715F] text-[#65715F] font-medium"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("pie")}
          >
            Pie Chart
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-4">
        {activeTab === "table" && tableData && (
          <TableRenderer
            headers={tableData.tableData.headers}
            rows={tableData.tableData.rows}
            title={tableData.tableData.title}
          />
        )}

        {activeTab === "bar" && typedBarChartData && (
          <ChartRenderer chartData={typedBarChartData.chartData} />
        )}

        {activeTab === "line" && typedLineChartData && (
          <ChartRenderer chartData={typedLineChartData.chartData} />
        )}

        {activeTab === "pie" && typedPieChartData && (
          <ChartRenderer chartData={typedPieChartData.chartData} />
        )}
      </div>

      <div className="mt-8 bg-gray-100 p-4 rounded-lg">
        <BakedBotHeading level={2} className="text-lg font-semibold mb-2">
          How to Use Sales Data in Your Chat
        </BakedBotHeading>
        <p className="mb-2">
          The backend should format sales data in one of these formats:
        </p>
        <pre className="bg-gray-800 text-white p-4 rounded-lg overflow-x-auto text-sm">
          {`// Table format
{
  "type": "salesData",
  "salesData": {
    "tableData": {
      "headers": ["Product", "Quantity", "Revenue", "Date"],
      "rows": [
        ["Blue Dream", "42", "$1,260.00", "2023-04-15"],
        ["OG Kush", "28", "$840.00", "2023-04-16"]
      ],
      "title": "Recent Sales"
    }
  }
}

// Chart format
{
  "type": "salesData",
  "salesData": {
    "chartData": {
      "type": "bar",
      "title": "Revenue by Product",
      "labels": ["Blue Dream", "OG Kush", "Sour Diesel"],
      "datasets": [{
        "label": "Revenue",
        "data": [1260, 840, 1050],
        "backgroundColor": "rgba(101, 113, 95, 0.6)"
      }]
    }
  }
}`}
        </pre>
      </div>
    </div>
  );
};

export default SalesDataDemo;
