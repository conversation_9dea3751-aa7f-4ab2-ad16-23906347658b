/* Base styles for the header */
.bakedbot-shared-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  position: relative;
  top: 0;
  width: 100%;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
  /* Ensure positioning context for cart sidebar */
  z-index: 2;
}

.bakedbot-header-container {
  max-width: 1400px;
  padding: 0 1rem; /* Adjusted padding for better spacing */
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* Header Left & Right Containers */
.bakedbot-header-left,
.bakedbot-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* General Icon Button Style */
.bakedbot-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: #4a5568;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.bakedbot-icon-btn:hover {
  background-color: #f7fafc;
  color: #065f46;
}

.bakedbot-icon-btn svg {
  font-size: 1.25rem;
}

.bakedbot-icon-label {
  display: none; /* Hide labels by default on mobile */
}

/* Badges for Cart and Filters */
.bakedbot-cart-badge,
.bakedbot-filter-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #10b981;
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.bakedbot-filter-badge {
  background: #10b981;
}

/* Search Overlay */
.bakedbot-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  z-index: 1100;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 15vh;
}

.bakedbot-search-overlay-content {
  position: relative;
  width: 100%;
  max-width: 600px;
  padding: 0 1rem;
}

.bakedbot-search-bar-overlay {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 100%;
}

.bakedbot-search-input-overlay {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 1.125rem;
  color: #2d3748;
  padding: 1rem 1.5rem;
}

.bakedbot-search-input-overlay::placeholder {
  color: #a0aec0;
}

.bakedbot-search-submit-overlay {
  background: transparent;
  border: none;
  color: #4a5568;
  padding: 1rem;
  font-size: 1.25rem;
  cursor: pointer;
  margin-right: 0.5rem;
  transition: color 0.2s ease;
}

.bakedbot-search-submit-overlay:hover {
  color: #065f46;
}

.bakedbot-search-close-overlay {
  position: absolute;
  top: -50px;
  right: 1rem;
  background: transparent;
  border: none;
  color: #4a5568;
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.bakedbot-search-close-overlay:hover {
  color: #1a202c;
}

/* Responsive styles */
@media (min-width: 769px) {
  .bakedbot-icon-btn {
    width: auto;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    gap: 0.5rem;
  }
  
  .bakedbot-icon-label {
    display: inline;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .bakedbot-header-left .bakedbot-icon-btn {
    border: 1px solid #e2e8f0;
  }
}

@media (max-width: 768px) {
  .bakedbot-header-container {
    padding: 0 1rem;
  }
  
  .bakedbot-site-title {
    font-size: 1.25rem;
  }

  .bakedbot-header-search-container {
    display: none;
  }

  .bakedbot-header-right {
    gap: 0.25rem;
  }
  
  .bakedbot-icon-btn {
    width: 36px;
    height: 36px;
  }
  
  .bakedbot-icon-btn svg {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
    .bakedbot-header-right {
        gap: 0;
    }
}

/* Header Left (Logo/Title) */
.bakedbot-header-left {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.bakedbot-site-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1a202c;
  margin: 0;
}

/* Header Search Container - Homepage Desktop/Tablet Only */
.bakedbot-header-search-container {
  flex: 1;
  max-width: 500px;
}

.bakedbot-header-search-form {
  display: flex;
  align-items: center;
  background: #f9fafb;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.bakedbot-header-search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.95rem;
  color: #2d3748;
  padding: 0.75rem 1rem;
}

.bakedbot-header-search-input::placeholder {
  color: #a0aec0;
}

.bakedbot-header-search-submit {
  background: #065f46;
  border: none;
  color: white;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bakedbot-header-search-submit:hover {
  background: #047857;
}

.bakedbot-header-search-submit svg {
  font-size: 1rem;
}

/* Header Right (Action Icons) */
.bakedbot-header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* --- Cart Sidebar & Cart Modal Styles (migrated from HeadlessHomepage & HeadlessMenu) --- */

.bakedbot-cart-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  width: 350px;
  height: 100vh;
  max-height: 100%;
  background: var(--background-color);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
  overflow-x: hidden;
}

.bakedbot-cart-sidebar + .bakedbot-sidebar-overlay,
.bakedbot-sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  max-height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.bakedbot-cart-sidebar.open {
  transform: translateX(0);
}

.bakedbot-sidebar-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.bakedbot-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  color: #000;
  border-bottom: 2px solid var(--primary-color);
  background: var(--hover-color);
}

.bakedbot-sidebar-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.bakedbot-sidebar-header button {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.5rem;
  line-height: 1;
  transition: color 0.2s;
}

.bakedbot-sidebar-header button:hover {
  color: var(--primary-color);
}

.bakedbot-sidebar-body {
  padding: 1.5rem 1.5rem 0 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.bakedbot-cart-items-scroll {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.bakedbot-cart-footer {
  padding: 1rem 1.5rem;
  border-top: 2px solid var(--border-color);
  background: var(--background-color);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex-shrink: 0;
}

.bakedbot-empty-cart {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-style: italic;
}

.bakedbot-cart-item-compact {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  transition: all 0.2s;
}

.bakedbot-cart-item-compact:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.bakedbot-cart-item-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.375rem;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.bakedbot-item-details-compact {
  flex: 1;
  min-width: 0;
}

.bakedbot-item-name-compact {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bakedbot-item-price-compact {
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 500;
}

.bakedbot-item-controls-compact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bakedbot-quantity-controls-compact {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.125rem;
}

.bakedbot-quantity-controls-compact button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.bakedbot-quantity-controls-compact button:hover {
  background: var(--primary-color);
  color: white !important;
}

.bakedbot-quantity-controls-compact span {
  font-size: 0.8rem;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
  color: var(--text-color);
  padding: 0 0.25rem;
}

.bakedbot-remove-item-compact {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.bakedbot-remove-item-compact:hover {
  background: #fee2e2;
}

.bakedbot-cart-footer-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.bakedbot-cart-total-compact {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
  flex-shrink: 0;
}

.bakedbot-checkout-btn-compact {
  flex: 1;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 120px;
}

.bakedbot-checkout-btn-compact:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

/* Cart open state styles - prevent background scrolling and hide floating footer */
.bakedbot-cart-open {
  overflow: hidden !important;
  height: 100vh !important;
  position: fixed !important;
  width: 100% !important;
  max-width: 100vw !important;
  top: 0 !important;
  left: 0 !important;
  box-sizing: border-box !important;
}

/* Hide floating footer when cart is open */
.bakedbot-cart-open .bakedbot-floating-cart-footer {
  display: none !important;
}

/* Ensure cart sidebar content can scroll when cart is open */
.bakedbot-cart-open .bakedbot-cart-sidebar {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  -webkit-overflow-scrolling: touch !important;
  box-sizing: border-box !important;
}

/* Ensure overlay covers full viewport when cart is open */
.bakedbot-cart-open .bakedbot-sidebar-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  max-width: 100vw !important;
  height: 100vh !important;
  z-index: 999 !important;
  box-sizing: border-box !important;
}

/* Fix for cart sidebar positioning when body is fixed */
.bakedbot-cart-open .bakedbot-cart-sidebar {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  height: 100vh !important;
}

/* --- Checkout Modal Styles (migrated from HeadlessHomepage & HeadlessMenu) --- */

.bakedbot-checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.bakedbot-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.bakedbot-modal-content {
  position: relative;
  background: var(--background-color);
  border-radius: 0.75rem;
  max-width: 900px;
  width: 95%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  -webkit-overflow-scrolling: touch;
  touch-action: manipulation;
}

.bakedbot-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.bakedbot-modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.bakedbot-modal-header button {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: background 0.2s;
}

.bakedbot-modal-header button:hover {
  background: var(--hover-color);
}

.bakedbot-modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  max-height: calc(90vh - 100px);
}

.bakedbot-checkout-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
  min-height: 400px;
}

.bakedbot-order-summary {
  background: var(--hover-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--border-color);
  min-width: 300px;
}

.bakedbot-order-summary h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.bakedbot-order-items {
  margin-bottom: 1rem;
}

.bakedbot-order-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  margin-bottom: 0.75rem;
  transition: all 0.2s;
}

.bakedbot-order-item:last-child {
  margin-bottom: 0;
}

.bakedbot-order-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.bakedbot-order-item-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.375rem;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.bakedbot-item-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.bakedbot-item-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 0.25rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bakedbot-item-price {
  font-size: 0.8rem;
  color: var(--primary-color);
  font-weight: 500;
}

.bakedbot-item-quantity {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.bakedbot-quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.125rem;
}

.bakedbot-quantity-controls button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.bakedbot-quantity-controls button:hover {
  background: var(--primary-color);
  color: white !important;
}

.bakedbot-quantity-controls span {
  font-size: 0.8rem;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
  color: var(--text-color);
  padding: 0 0.25rem;
}

.bakedbot-remove-item {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.bakedbot-remove-item:hover {
  background: #fee2e2;
}

.bakedbot-order-total {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.bakedbot-total-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-color);
}

.bakedbot-total-line:last-child {
  margin-bottom: 0;
}

.bakedbot-final-total {
  border-top: 1px solid var(--border-color);
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  color: var(--primary-color);
}

.bakedbot-customer-info {
  background: var(--background-color);
}

.bakedbot-customer-info h3 {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.bakedbot-checkout-form {
  border-top: 1px solid var(--border-color);
  padding-top: 1.5rem;
}

.bakedbot-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.bakedbot-dob-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.bakedbot-dob-label {
  font-size: 0.875rem;
  font-weight: 400;
  color: #a0aec0;
  white-space: nowrap;
  min-width: fit-content;
  margin-bottom: 11px !important;
}

.bakedbot-dob-input {
  width: 180px !important;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: var(--background-color);
  color: var(--text-color);
  margin-bottom: 0 !important;
}

.bakedbot-checkout-form input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: var(--background-color);
  color: var(--text-color);
  margin-bottom: 0.75rem;
}

.bakedbot-checkout-form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.bakedbot-place-order-btn {
  width: 100%;
  padding: 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.bakedbot-place-order-btn:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-place-order-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* --- Age Gate Modal Styles (migrated from HeadlessHomepage & HeadlessMenu) --- */

.bakedbot-age-gate-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.95);
}

.bakedbot-age-gate-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
}

.bakedbot-age-gate-content {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  animation: ageGateSlideIn 0.3s ease-out;
}

@keyframes ageGateSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.bakedbot-age-gate-header h2 {
  color: var(--primary-color, #065f46);
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.bakedbot-age-gate-body p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
}

.bakedbot-age-gate-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.bakedbot-age-gate-btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bakedbot-age-gate-yes {
  background: var(--primary-color, #065f46);
  color: white;
}

.bakedbot-age-gate-yes:hover {
  background: var(--secondary-color, #10b981);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.bakedbot-age-gate-no {
  background: #f3f4f6;
  color: #6b7280;
  border: 2px solid #e5e7eb;
}

.bakedbot-age-gate-no:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

@media (max-width: 768px) {
  .bakedbot-cart-sidebar {
    width: 90%;
    max-width: 350px;
  }
  .bakedbot-cart-footer-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  .bakedbot-cart-total-compact {
    text-align: center;
    font-size: 1rem;
  }
  .bakedbot-modal-content {
    margin: 0.5rem;
    max-height: calc(100vh - 1rem);
    border-radius: 0.75rem;
  }
  .bakedbot-form-row {
    grid-template-columns: 1fr;
  }
  .bakedbot-dob-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  .bakedbot-dob-input {
    width: 100% !important;
  }
  .bakedbot-checkout-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  .bakedbot-age-gate-content {
    padding: 1.5rem;
    margin: 1rem;
  }
  .bakedbot-age-gate-header h2 {
    font-size: 1.75rem;
  }
  .bakedbot-age-gate-body p {
    font-size: 1rem;
  }
  .bakedbot-age-gate-buttons {
    flex-direction: column;
  }
  .bakedbot-age-gate-btn {
    width: 100%;
    padding: 1.25rem;
  }

  /* Mobile cart open state adjustments */
  .bakedbot-cart-open .bakedbot-cart-sidebar {
    width: 100vw !important;
    max-width: 100vw !important;
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: 0 !important;
    height: 100vh !important;
    height: 100dvh !important; /* Use dynamic viewport height when available */
    transform: translateX(0) !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
    border-radius: 0 !important; /* Remove any border radius on mobile */
  }
  
  /* Ensure mobile viewport is handled correctly */
  @supports (height: 100dvh) {
    .bakedbot-cart-open .bakedbot-cart-sidebar {
      height: 100dvh !important;
    }
    
    .bakedbot-cart-open .bakedbot-sidebar-content {
      height: 100dvh !important;
    }
  }
  
  /* iOS Safari specific fixes */
  @supports (-webkit-touch-callout: none) {
    .bakedbot-cart-open .bakedbot-cart-sidebar {
      height: 100vh !important;
      height: -webkit-fill-available !important;
    }
    
    .bakedbot-cart-open .bakedbot-sidebar-content {
      height: 100vh !important;
      height: -webkit-fill-available !important;
    }
  }

  /* Ensure mobile cart content is properly contained */
  .bakedbot-cart-open .bakedbot-sidebar-content {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    margin: 0 !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
  }

  /* Mobile cart header */
  .bakedbot-cart-open .bakedbot-sidebar-header {
    flex-shrink: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 16px 20px !important;
    border-bottom: 1px solid #e5e7eb !important;
    background: var(--background-color, #ffffff) !important;
  }

  /* Mobile cart body with proper scrolling */
  .bakedbot-cart-open .bakedbot-sidebar-body {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 16px 20px !important;
  }

  /* Mobile cart footer */
  .bakedbot-cart-open .bakedbot-sidebar-footer {
    flex-shrink: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 16px 20px !important;
    border-top: 1px solid #e5e7eb !important;
    background: var(--background-color, #ffffff) !important;
  }

  /* Ensure all cart items are mobile-friendly */
  .bakedbot-cart-open .bakedbot-cart-item {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    padding: 12px 0 !important;
    border-bottom: 1px solid #f3f4f6 !important;
    display: flex !important;
    gap: 12px !important;
  }

  .bakedbot-cart-open .bakedbot-cart-item-image {
    flex-shrink: 0 !important;
    width: 60px !important;
    height: 60px !important;
  }

  .bakedbot-cart-open .bakedbot-cart-item-details {
    flex: 1 !important;
    min-width: 0 !important; /* Allow text to shrink and wrap */
    overflow: hidden !important;
  }

  .bakedbot-cart-open .bakedbot-cart-item-name {
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    margin-bottom: 4px !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
  }

  .bakedbot-cart-open .bakedbot-cart-item-price {
    font-size: 14px !important;
    color: var(--primary-color, #065f46) !important;
    font-weight: 600 !important;
  }

  .bakedbot-cart-open .bakedbot-cart-item-quantity {
    flex-shrink: 0 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .bakedbot-cart-open .bakedbot-quantity-btn-mobile {
    width: 32px !important;
    height: 32px !important;
    border: 1px solid #d1d5db !important;
    background: #ffffff !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 12px !important;
    color: #374151 !important;
  }

  .bakedbot-cart-open .bakedbot-quantity-display-mobile {
    font-size: 14px !important;
    font-weight: 600 !important;
    min-width: 20px !important;
    text-align: center !important;
  }

  /* Mobile floating footer should be hidden when cart is open */
  .bakedbot-cart-open .bakedbot-floating-cart-footer {
    display: none !important;
  }

     /* Ensure checkout button is mobile-friendly */
   .bakedbot-cart-open .bakedbot-checkout-btn-mobile {
     width: 100% !important;
     padding: 16px !important;
     font-size: 16px !important;
     font-weight: 600 !important;
     border-radius: 8px !important;
     background: var(--primary-color, #065f46) !important;
     color: white !important;
     border: none !important;
     margin-top: 16px !important;
     box-sizing: border-box !important;
     min-height: 44px !important; /* iOS touch target minimum */
     touch-action: manipulation !important; /* Improve touch responsiveness */
     -webkit-tap-highlight-color: transparent !important; /* Remove tap highlight */
   }
   
   /* Improve touch interactions for all mobile cart buttons */
   .bakedbot-cart-open .bakedbot-quantity-btn-mobile,
   .bakedbot-cart-open .bakedbot-close-btn {
     touch-action: manipulation !important;
     -webkit-tap-highlight-color: transparent !important;
     cursor: pointer !important;
   }
   
   /* Disable text selection in cart on mobile */
   .bakedbot-cart-open .bakedbot-cart-sidebar {
     -webkit-user-select: none !important;
     -moz-user-select: none !important;
     -ms-user-select: none !important;
     user-select: none !important;
   }
   
   /* Allow text selection for cart item names */
   .bakedbot-cart-open .bakedbot-cart-item-name,
   .bakedbot-cart-open .bakedbot-cart-item-details {
     -webkit-user-select: text !important;
     -moz-user-select: text !important;
     -ms-user-select: text !important;
     user-select: text !important;
   }
   
   /* Ensure cart headers and content don't cause horizontal overflow */
   .bakedbot-cart-open .bakedbot-sidebar-header h2,
   .bakedbot-cart-open .bakedbot-sidebar-header h3 {
     margin: 0 !important;
     font-size: 18px !important;
     font-weight: 600 !important;
     word-wrap: break-word !important;
     overflow-wrap: break-word !important;
   }
   
   /* Ensure close button is properly sized for mobile */
   .bakedbot-cart-open .bakedbot-close-btn {
     width: 32px !important;
     height: 32px !important;
     min-width: 32px !important;
     min-height: 32px !important;
     border-radius: 6px !important;
     display: flex !important;
     align-items: center !important;
     justify-content: center !important;
     font-size: 14px !important;
   }
   
   /* Responsive text sizes for very small screens */
   @media (max-width: 360px) {
     .bakedbot-cart-open .bakedbot-cart-item-name {
       font-size: 13px !important;
     }
     
     .bakedbot-cart-open .bakedbot-cart-item-price {
       font-size: 13px !important;
     }
     
     .bakedbot-cart-open .bakedbot-sidebar-header h2,
     .bakedbot-cart-open .bakedbot-sidebar-header h3 {
       font-size: 16px !important;
     }
     
     .bakedbot-cart-open .bakedbot-checkout-btn-mobile {
       font-size: 15px !important;
       padding: 14px !important;
     }
   }

  /* Mobile cart items scroll area height adjustment */
  .bakedbot-cart-open .bakedbot-cart-items-scroll {
    height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch;
  }
}

/* Order Success Modal Styling */
.bakedbot-order-confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

.bakedbot-order-success {
  background: #ffffff;
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.4s ease-out;
  position: relative;
  overflow: hidden;
}

.bakedbot-order-success::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #10b981, #059669, #047857);
  border-radius: 20px 20px 0 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.bakedbot-success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  position: relative;
  animation: successPulse 2s ease-in-out infinite;
}

.bakedbot-success-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981, #059669);
  opacity: 0.3;
  animation: ripple 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

.bakedbot-success-icon svg {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  z-index: 1;
  position: relative;
}

.bakedbot-order-success h2 {
  color: #1f2937;
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0 0 1.5rem;
  background: linear-gradient(135deg, #1f2937, #374151);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
}

.bakedbot-confirmation-message {
  margin: 0 0 2rem;
  line-height: 1.6;
}

.bakedbot-main-message {
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 1rem;
}

.bakedbot-pickup-message {
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem;
  background: #f3f4f6;
  padding: 1rem;
  border-radius: 12px;
  border-left: 4px solid #10b981;
}

.bakedbot-pickup-message strong {
  color: #10b981;
  font-weight: 600;
}

.bakedbot-closing-message {
  color: #6b7280;
  font-size: 0.875rem;
  font-style: italic;
  margin: 0;
}

.bakedbot-confirmation-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.bakedbot-continue-shopping-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  position: relative;
  overflow: hidden;
}

.bakedbot-continue-shopping-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.bakedbot-continue-shopping-btn:hover::before {
  left: 100%;
}

.bakedbot-continue-shopping-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

.bakedbot-continue-shopping-btn:active {
  transform: translateY(0);
}

/* Mobile responsive adjustments */
@media (max-width: 480px) {
  .bakedbot-order-success {
    padding: 2rem 1.5rem;
    margin: 1rem;
    border-radius: 16px;
  }

  .bakedbot-success-icon {
    width: 60px;
    height: 60px;
  }

  .bakedbot-success-icon svg {
    font-size: 1.5rem;
  }

  .bakedbot-order-success h2 {
    font-size: 1.5rem;
  }

  .bakedbot-pickup-message {
    font-size: 0.875rem;
    padding: 0.875rem;
  }

  .bakedbot-continue-shopping-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    min-width: 150px;
  }
}

/* User Authentication Styles */
.bakedbot-user-section {
  position: relative;
}

.bakedbot-user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.bakedbot-user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 200px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.bakedbot-user-info {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.bakedbot-user-name {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
}

.bakedbot-user-email {
  color: #6b7280;
  font-size: 12px;
  margin-top: 2px;
}

.bakedbot-user-actions {
  padding: 8px;
}

.bakedbot-logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #ef4444;
  font-size: 14px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bakedbot-logout-btn:hover {
  background-color: #fef2f2;
}

/* User Menu Button Styles */
.bakedbot-user-menu-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.bakedbot-user-menu-btn:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.bakedbot-user-menu-btn svg {
  width: 16px;
  height: 16px;
}
