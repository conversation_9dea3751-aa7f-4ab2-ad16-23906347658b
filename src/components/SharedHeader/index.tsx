import React, { useState, useEffect } from "react";
import {
  FaSearch,
  FaShoppingCart,
  FaUserCircle,
  FaFilter,
  FaTimes,
  FaCheck,
  FaPlus,
  FaMinus,
  FaSignOutAlt,
  FaUser,
  FaBox,
} from "react-icons/fa";
import { useCart } from "../../views/ChatWidget/CartContext";
import { useAuth } from "../../contexts/AuthContext";
import { fetchCustomerProfile } from "../../utils/api";
import LoginModal from "../LoginModal";
import { Orders } from "../Orders";
import { Profile } from "../Profile";
import { MobileCart } from "../MobileCart";
import { MobileCheckout } from "../MobileCheckout";
import { SimpleFloatingFooter } from "../SimpleFloatingFooter";
import "./SharedHeader.css";

interface SharedHeaderProps {
  cartCount: number;
  onCartClick: () => void;
  onSearch: (query: string) => void;
  isMenuPage?: boolean;
  onFilterClick?: () => void;
  filterCount?: number;
  enableCart?: boolean;
}

// CheckoutFormData interface moved to MobileCheckout component

export const SharedHeader: React.FC<SharedHeaderProps> = ({
  cartCount,
  onCartClick,
  onSearch,
  isMenuPage = false,
  onFilterClick,
  filterCount,
  enableCart = true,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [showCart, setShowCart] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  const [showAgeGate, setShowAgeGate] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showOrders, setShowOrders] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  // Simplified state - MobileCheckout handles its own form state

  const { cart, updateQuantity, removeFromCart, handleCheckout } = useCart();
  const { user, isAuthenticated, logout } = useAuth();

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Check age gate on component mount
  useEffect(() => {
    const ageConfirmed = localStorage.getItem("ageConfirmed");
    if (!ageConfirmed) {
      setShowAgeGate(true);
    }
  }, []);

  // No need for complex body scroll management - MobileCart handles this

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch(searchQuery);
    }
    setIsSearchVisible(false);
  };

  // Age gate handlers
  const handleAgeConfirm = () => {
    localStorage.setItem("ageConfirmed", "true");
    setShowAgeGate(false);
  };

  const handleAgeDeny = () => {
    window.location.href = "https://www.samhsa.gov/find-help/national-helpline";
  };

  // User menu handlers
  const handleUserIconClick = () => {
    if (isAuthenticated) {
      setShowUserMenu(!showUserMenu);
    } else {
      setShowLoginModal(true);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setShowUserMenu(false);
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  const handleShowOrders = () => {
    setShowUserMenu(false);
    setShowOrders(true);
  };

  const handleShowProfile = () => {
    setShowUserMenu(false);
    setShowProfile(true);
  };

  // MobileCheckout component handles its own form and submission logic

  // No need for cart calculations here - components handle their own calculations

  return (
    <>
      <header className="bakedbot-shared-header">
        <div className="bakedbot-header-container">
          {/* Static search input - only on homepage and desktop/tablet */}
          {!isMenuPage && !isMobile && (
            <div className="bakedbot-header-search-container">
              <form
                onSubmit={handleSearchSubmit}
                className="bakedbot-header-search-form"
              >
                <input
                  type="text"
                  placeholder="Search for products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bakedbot-header-search-input"
                />
                <button type="submit" className="bakedbot-header-search-submit">
                  <FaSearch />
                </button>
              </form>
            </div>
          )}

          <div className="bakedbot-header-right">
            {/* Search icon - only on mobile or menu page */}
            {isMobile && (
              <button
                className="bakedbot-icon-btn"
                onClick={() => setIsSearchVisible(true)}
                aria-label="Open search"
              >
                <FaSearch />
                <span className="bakedbot-icon-label">Search</span>
              </button>
            )}
          </div>

          <div className="bakedbot-header-right">
            {isMenuPage && isMobile && (
              <button
                className="bakedbot-icon-btn"
                onClick={onFilterClick}
                aria-label="Open filters"
              >
                <FaFilter />
                <span className="bakedbot-icon-label">Filters</span>
                {filterCount !== undefined && filterCount > 0 && (
                  <span className="bakedbot-filter-badge">{filterCount}</span>
                )}
              </button>
            )}

            {/* User Icon with Authentication */}
            <div className="bakedbot-user-section">
              <button
                className="bakedbot-icon-btn"
                onClick={handleUserIconClick}
                aria-label={isAuthenticated ? "User menu" : "Sign in"}
              >
                {isAuthenticated && user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt="User avatar"
                    className="bakedbot-user-avatar"
                  />
                ) : (
                  <FaUserCircle />
                )}
                <span className="bakedbot-icon-label">
                  {isAuthenticated ? user?.displayName || "Account" : "Sign In"}
                </span>
              </button>

              {/* User dropdown menu */}
              {showUserMenu && isAuthenticated && (
                <div className="bakedbot-user-dropdown">
                  <div className="bakedbot-user-info">
                    <div className="bakedbot-user-name">
                      {user?.displayName || user?.email || "User"}
                    </div>
                    {user?.email && (
                      <div className="bakedbot-user-email">{user.email}</div>
                    )}
                  </div>
                  <div className="bakedbot-user-actions">
                    <button
                      onClick={handleShowProfile}
                      className="bakedbot-user-menu-btn"
                    >
                      <FaUser />
                      My Profile
                    </button>
                    <button
                      onClick={handleShowOrders}
                      className="bakedbot-user-menu-btn"
                    >
                      <FaBox />
                      My Orders
                    </button>
                    <button
                      onClick={handleLogout}
                      className="bakedbot-logout-btn"
                    >
                      <FaSignOutAlt />
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>

            {enableCart && (
              <button
                className="bakedbot-icon-btn"
                onClick={() => setShowCart(true)}
                aria-label="Open cart"
              >
                <FaShoppingCart />
                <span className="bakedbot-icon-label">Cart</span>
                {cartCount > 0 && (
                  <span className="bakedbot-cart-badge">{cartCount}</span>
                )}
              </button>
            )}
          </div>
        </div>

        {isSearchVisible && (
          <div className="bakedbot-search-overlay">
            <div className="bakedbot-search-overlay-content">
              <form
                onSubmit={handleSearchSubmit}
                className="bakedbot-search-bar-overlay"
              >
                <input
                  type="text"
                  placeholder="Search for products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bakedbot-search-input-overlay"
                  autoFocus
                />
                <button
                  type="submit"
                  className="bakedbot-search-submit-overlay"
                  aria-label="Submit search"
                >
                  <FaSearch />
                </button>
              </form>
              <button
                className="bakedbot-search-close-overlay"
                onClick={() => setIsSearchVisible(false)}
                aria-label="Close search"
              >
                <FaTimes />
              </button>
            </div>
          </div>
        )}
      </header>

      {/* Age Gate Modal */}
      {showAgeGate && (
        <div className="bakedbot-age-gate-modal">
          <div className="bakedbot-age-gate-overlay" />
          <div className="bakedbot-age-gate-content">
            <div className="bakedbot-age-gate-header">
              <h2>Are you 21 or older?</h2>
            </div>
            <div className="bakedbot-age-gate-body">
              <p>
                You must be over the age of 21 or have a medical marijuana card
                to enter our website.
              </p>
              <div className="bakedbot-age-gate-buttons">
                <button
                  className="bakedbot-age-gate-btn bakedbot-age-gate-yes"
                  onClick={handleAgeConfirm}
                >
                  YES
                </button>
                <button
                  className="bakedbot-age-gate-btn bakedbot-age-gate-no"
                  onClick={handleAgeDeny}
                >
                  NO
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Mobile Cart */}
      {enableCart && (
        <MobileCart
          isOpen={showCart}
          onClose={() => setShowCart(false)}
          onCheckout={() => {
            setShowCart(false);
            setShowCheckout(true);
          }}
        />
      )}

      {/* Mobile Checkout */}
      {enableCart && (
        <MobileCheckout
          isOpen={showCheckout}
          onClose={() => setShowCheckout(false)}
          onSuccess={() => setShowOrderConfirmation(true)}
        />
      )}

      {/* Order Confirmation Modal */}
      {showOrderConfirmation && (
        <div className="bakedbot-order-confirmation-modal">
          <div
            className="bakedbot-modal-overlay"
            onClick={() => setShowOrderConfirmation(false)}
          />
          <div className="bakedbot-modal-content bakedbot-order-success">
            <div className="bakedbot-success-icon">
              <FaCheck />
            </div>
            <h2>Order Placed Successfully!</h2>
            <div className="bakedbot-confirmation-message">
              <p className="bakedbot-main-message">
                Thank you for your order! We've received your request and you
                should receive a confirmation email shortly.
              </p>
              <p className="bakedbot-pickup-message">
                <strong>Check your email</strong> for order details and pickup
                instructions.
              </p>
              <p className="bakedbot-closing-message">
                See you soon for your pickup! 🌿
              </p>
            </div>
            <div className="bakedbot-confirmation-actions">
              <button
                className="bakedbot-continue-shopping-btn"
                onClick={() => setShowOrderConfirmation(false)}
              >
                Continue Shopping
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLogin={() => {
          setShowLoginModal(false);
          setShowUserMenu(false);
        }}
        title="Sign In to Your Account"
      />

      {/* Orders Modal */}
      {showOrders && <Orders onClose={() => setShowOrders(false)} />}

      {/* Profile Modal */}
      {showProfile && <Profile onClose={() => setShowProfile(false)} />}

      {/* Simple Floating Footer */}
      {enableCart && (
        <SimpleFloatingFooter
          onShowCart={() => setShowCart(true)}
          isCartOpen={showCart || showCheckout}
        />
      )}
    </>
  );
};

export default SharedHeader;
