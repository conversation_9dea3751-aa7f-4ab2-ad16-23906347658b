/* Simple Floating Footer */
.simple-floating-footer {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 999;
  padding: 0;
  background: transparent;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.simple-cart-button {
  width: 100%;
  max-width: 270px;

  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 16px 20px;
  background: var(--primary-color, #065f46);
  color: white;
  border: none;
  border-radius: 99px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 16px rgba(6, 95, 70, 0.2);
  min-height: 56px; /* iOS touch target minimum */
  touch-action: manipulation;
}

.simple-cart-button:hover {
  background: var(--secondary-color, #10b981);
  transform: translateY(-1px);
  box-shadow: 0 6px 24px rgba(16, 185, 129, 0.3);
}

.simple-cart-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.simple-cart-icon {
  position: relative;
  flex-shrink: 0;
}

.simple-cart-icon svg {
  font-size: 20px;
}

.simple-cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc2626;
  color: white;
  font-size: 12px;
  font-weight: 700;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--primary-color, #22AD85);
}

.simple-cart-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.simple-cart-text {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.simple-cart-total {
  font-size: 18px;
  font-weight: 700;
}

.simple-cart-action {
  flex-shrink: 0;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.simple-cart-button:hover .simple-cart-action {
  background: rgba(255, 255, 255, 0.3);
}

/* Mobile Styles */
@media (max-width: 768px) {
  .simple-floating-footer {
    bottom: 16px;
    left: 16px;
  }

  .simple-cart-button {
    padding: 14px 16px;
    gap: 12px;
  }

  .simple-cart-text {
    font-size: 13px;
  }

  .simple-cart-total {
    font-size: 16px;
  }

  .simple-cart-action {
    font-size: 14px;
    padding: 6px 12px;
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .simple-floating-footer {
    bottom: 12px;
    left: 12px;
  }

  .simple-cart-button {
    padding: 12px 14px;
    gap: 10px;
  }

  .simple-cart-icon svg {
    font-size: 18px;
  }

  .simple-cart-badge {
    font-size: 11px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
  }

  .simple-cart-text {
    font-size: 12px;
  }

  .simple-cart-total {
    font-size: 15px;
  }

  .simple-cart-action {
    font-size: 13px;
    padding: 5px 10px;
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .simple-floating-footer {
    bottom: 24px;
    left: 24px;
  }

  .simple-cart-button {
    max-width: 270px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .simple-floating-footer {
    animation: none;
  }

  .simple-cart-button {
    transition: none;
  }
}


/* No padding needed for floating button */

.simple-cart-button-content {
  min-width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--primary-color, #065f46);
}

/* Glassmorphism effect for modern browsers */
