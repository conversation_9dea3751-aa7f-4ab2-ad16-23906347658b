import React from "react";
import { FaShoppingCart } from "react-icons/fa";
import { useCart } from "../../views/ChatWidget/CartContext";
import "./SimpleFloatingFooter.css";

interface SimpleFloatingFooterProps {
  onShowCart: () => void;
  isCartOpen?: boolean;
}

export const SimpleFloatingFooter: React.FC<SimpleFloatingFooterProps> = ({
  onShowCart,
  isCartOpen = false,
}) => {
  const { cart } = useCart();

  // Calculate total items in cart
  const totalItems = Object.values(cart).reduce(
    (sum, { quantity }) => sum + quantity,
    0
  );

  // Calculate total price
  const totalPrice = Object.values(cart).reduce(
    (sum, { product, quantity }) => sum + product.latest_price * quantity,
    0
  );

  // Don't show if cart is empty OR if cart is open
  if (totalItems === 0 || isCartOpen) {
    return null;
  }

  return (
    <div className="simple-floating-footer">
      <button className="simple-cart-button" onClick={onShowCart}>
        <div className="simple-cart-icon">
          <FaShoppingCart />
          <span className="simple-cart-badge">{totalItems}</span>
        </div>
        <div className="simple-cart-info">
          <span className="simple-cart-text">
            {totalItems} item{totalItems !== 1 ? "s" : ""}
          </span>
          <span className="simple-cart-total">${totalPrice.toFixed(2)}</span>
        </div>
        <span className="simple-cart-action">View Cart</span>
      </button>
    </div>
  );
};

export default SimpleFloatingFooter;
