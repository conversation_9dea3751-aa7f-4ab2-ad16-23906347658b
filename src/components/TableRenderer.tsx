import React, { useState } from "react";
import { FaSort, FaSortUp, FaSortDown } from "react-icons/fa";

interface TableRendererProps {
  headers: string[];
  rows: string[][];
  title?: string;
  className?: string;
}

const TableRenderer: React.FC<TableRendererProps> = ({
  headers,
  rows,
  title,
  className = "",
}) => {
  const [sortColumn, setSortColumn] = useState<number | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  const handleSort = (columnIndex: number) => {
    if (sortColumn === columnIndex) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(columnIndex);
      setSortDirection("asc");
    }
  };

  const sortedRows = [...rows];
  if (sortColumn !== null) {
    sortedRows.sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      // Handle numeric sorting (values that look like currency with $ sign or just numbers)
      const aNumeric = aValue.replace(/[$,]/g, "");
      const bNumeric = bValue.replace(/[$,]/g, "");

      if (!isNaN(Number(aNumeric)) && !isNaN(Number(bNumeric))) {
        return sortDirection === "asc"
          ? Number(aNumeric) - Number(bNumeric)
          : Number(bNumeric) - Number(aNumeric);
      }

      // Handle date sorting if values look like dates
      const aDate = new Date(aValue);
      const bDate = new Date(bValue);
      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return sortDirection === "asc"
          ? aDate.getTime() - bDate.getTime()
          : bDate.getTime() - aDate.getTime();
      }

      // Default string sorting
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });
  }

  return (
    <div
      className={`sales-table-wrapper rounded-lg overflow-hidden my-4 border border-gray-200 shadow-sm ${className} block !important visible !important opacity-100 !important`}
    >
      {title && (
        <div className="sales-table-title bg-[#65715F] text-white py-2 px-4 text-sm font-bold">
          {title}
        </div>
      )}
      <div className="overflow-x-auto">
        <table className="w-full sales-data-table border-collapse">
          <thead>
            <tr className="bg-gray-100 border-b border-gray-200">
              {headers.map((header, index) => (
                <th
                  key={index}
                  onClick={() => handleSort(index)}
                  className="px-4 py-2 text-left font-medium text-sm text-gray-600 cursor-pointer hover:bg-gray-200 border border-gray-200"
                >
                  <div className="flex items-center">
                    <span>{header}</span>
                    <div className="ml-1">
                      {sortColumn === index ? (
                        sortDirection === "asc" ? (
                          <FaSortUp className="text-[#65715F]" />
                        ) : (
                          <FaSortDown className="text-[#65715F]" />
                        )
                      ) : (
                        <FaSort className="text-gray-400" size={10} />
                      )}
                    </div>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white">
            {sortedRows.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={`border-b border-gray-200 ${
                  rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"
                } hover:bg-gray-100`}
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="px-4 py-2 text-sm border border-gray-200"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {rows.length === 0 && (
        <div className="py-4 text-center text-gray-500">No data available</div>
      )}
    </div>
  );
};

export default TableRenderer;
