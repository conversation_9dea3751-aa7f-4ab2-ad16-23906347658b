import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getPerformance } from "firebase/performance";
import { initializeAnalytics } from "firebase/analytics";

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY as string,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN as string,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID as string,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET as string,
  messagingSenderId: import.meta.env
    .VITE_FIREBASE_MESSAGING_SENDER_ID as string,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID as string,
  appId: import.meta.env.VITE_FIREBASE_APP_ID as string,
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Initialize Performance Monitoring
const perf = getPerformance(app);

// Initialize Analytics (required for Crashlytics on web)
const analytics = initializeAnalytics(app);

export { app, auth, perf, analytics };
