import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

// Define the structure of our configuration
interface BakedBotConfig {
  // Authentication settings
  authMode: "federated" | "wordpress" | "bakedbot";
  syncUsers: boolean;
  siteIdentifier: string;
  apiUrl: string;
  wordPressAjaxUrl?: string;
  useWordPressAuth?: boolean;

  // Behavior settings
  position: "left" | "right";
  initialOpen: boolean;
  showOnMobile: boolean;
  userModeOnly?: boolean;

  // Theme settings
  disableThemeSettings: boolean;

  // API settings
  apiKey: string;
}

// Default configuration
const defaultConfig: BakedBotConfig = {
  authMode: "bakedbot",
  syncUsers: false,
  siteIdentifier: "",
  apiUrl: "https://beta.bakedbot.ai/api/",
  position: "right",
  initialOpen: false,
  showOnMobile: true,
  disableThemeSettings: true,
  apiKey: "",
};

// Create the context
const ConfigContext = createContext<{
  config: BakedBotConfig;
  isWordPressMode: boolean;
  isFederatedMode: boolean;
  isBakedBotMode: boolean;
}>({
  config: defaultConfig,
  isWordPressMode: false,
  isFederatedMode: false,
  isBakedBotMode: true,
});

// Provider component
export const ConfigProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [config, setConfig] = useState<BakedBotConfig>(defaultConfig);

  useEffect(() => {
    // Check if BakedBotConfig is available in the window object (WordPress integration)
    const windowConfig = (window as any).BakedBotConfig;

    if (windowConfig) {
      // Merge the window config with default values for any missing properties
      setConfig({
        ...defaultConfig,
        ...windowConfig,
      });
    }

    // If no window config, we'll use the defaults (standalone mode)
  }, []);

  // Computed properties for easier checks in components
  const isWordPressMode = config.authMode === "wordpress";
  const isFederatedMode = config.authMode === "federated";
  const isBakedBotMode = config.authMode === "bakedbot";

  return (
    <ConfigContext.Provider
      value={{
        config,
        isWordPressMode,
        isFederatedMode,
        isBakedBotMode,
      }}
    >
      {children}
    </ConfigContext.Provider>
  );
};

// Custom hook for using the config
export const useConfig = () => useContext(ConfigContext);

export default ConfigContext;
