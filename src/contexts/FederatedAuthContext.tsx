import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { onAuthStateChanged, User, signOut } from "firebase/auth";
import { auth } from "../config/firebase-config";
import { useConfig } from "./ConfigContext";
import axios from "axios";
import tokenStorage from "../utils/tokenStorage";

// Define our user interface to account for different authentication sources
interface FederatedUser {
  id: string;
  displayName: string | null;
  email: string | null;
  photoURL: string | null;
  isAnonymous: boolean;
  // Federated auth specific fields
  bakedBotId?: string;
  wordPressId?: number;
  synced: boolean;
  authToken?: string;
  // The source of the user data
  source: "firebase" | "wordpress" | "bakedbot";
}

// WordPress user response from AJAX endpoint
interface WordPressUserResponse {
  data: {
    loggedIn: boolean;
    userId: number;
    displayName: string;
    email: string;
    preferences: any;
    synced: boolean;
    bakedBotId: string;
    authToken?: string;
    authMode: string;
  };
}

// Interface for the auth context
interface FederatedAuthContextType {
  user: FederatedUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  logout: () => Promise<void>;
  syncUserAccounts: () => Promise<boolean>;
  verifyToken: (token: string) => Promise<boolean>;
  refreshToken: () => Promise<boolean>;
  isTokenExpiringSoon: (thresholdMinutes?: number) => boolean;
  setRememberAcrossSites: (remember: boolean) => void;
  isRememberAcrossSitesEnabled: () => boolean;
}

// Create the context
const FederatedAuthContext = createContext<FederatedAuthContextType>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  logout: async () => {},
  syncUserAccounts: async () => false,
  verifyToken: async () => false,
  refreshToken: async () => false,
  isTokenExpiringSoon: () => false,
  setRememberAcrossSites: () => {},
  isRememberAcrossSitesEnabled: () => false,
});

export const FederatedAuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { config, isWordPressMode, isFederatedMode, isBakedBotMode } =
    useConfig();
  const [user, setUser] = useState<FederatedUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Helper to check if token is expiring soon
  const isTokenExpiringSoon = (thresholdMinutes: number = 5): boolean => {
    return tokenStorage.isTokenExpiringSoon(thresholdMinutes);
  };

  // Store the "remember me" preference for federated auth
  const setRememberAcrossSites = (remember: boolean): void => {
    try {
      // Store the preference for next time
      localStorage.setItem("bakedbot_remember_across_sites", String(remember));

      // If we have a token, update its remember status
      const token = tokenStorage.getToken();
      if (token) {
        // Get current expiration and update the token with the new remember status
        const expiration = tokenStorage.getTokenExpiration();
        if (expiration) {
          const expiresInMinutes = Math.max(
            1,
            Math.round((expiration - Date.now()) / (1000 * 60))
          );
          tokenStorage.saveToken(token, expiresInMinutes, remember);
        }
      }
    } catch (error) {
      console.error("Error saving remember preference:", error);
    }
  };

  // Check if "remember me" is enabled
  const isRememberAcrossSitesEnabled = (): boolean => {
    try {
      return localStorage.getItem("bakedbot_remember_across_sites") === "true";
    } catch (error) {
      return false;
    }
  };

  // Map a Firebase User to our FederatedUser format
  const mapFirebaseUser = async (
    firebaseUser: User | null
  ): Promise<FederatedUser | null> => {
    if (!firebaseUser) return null;

    try {
      // Get fresh token for the user
      const idToken = await firebaseUser.getIdToken();

      // Store the token for future use
      const remember = isRememberAcrossSitesEnabled();
      tokenStorage.saveToken(idToken, 60, remember);

      console.log(
        "[FederatedAuthContext] Stored Firebase token in tokenStorage"
      );

      return {
        id: firebaseUser.uid,
        displayName: firebaseUser.displayName,
        email: firebaseUser.email,
        photoURL: firebaseUser.photoURL,
        isAnonymous: firebaseUser.isAnonymous,
        synced: false, // Will be updated if account is synced
        source: "firebase",
        authToken: idToken, // Store token in user object
      };
    } catch (error) {
      console.error(
        "[FederatedAuthContext] Error getting Firebase token:",
        error
      );
      return {
        id: firebaseUser.uid,
        displayName: firebaseUser.displayName,
        email: firebaseUser.email,
        photoURL: firebaseUser.photoURL,
        isAnonymous: firebaseUser.isAnonymous,
        synced: false,
        source: "firebase",
        authToken: undefined,
      };
    }
  };

  // Ensure that the user object always has the latest token
  const ensureUserHasToken = useCallback(
    (currentUser: FederatedUser | null): FederatedUser | null => {
      if (!currentUser) return null;

      // Get the stored token
      const storedToken = tokenStorage.getToken();

      // If we have a stored token and the user doesn't have it (or has a different one)
      if (
        storedToken &&
        (!currentUser.authToken || currentUser.authToken !== storedToken)
      ) {
        console.log("Updating user with stored token");
        return {
          ...currentUser,
          authToken: storedToken,
        };
      }

      return currentUser;
    },
    []
  );

  // Initialize auth state based on Firebase Auth
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      // When Firebase auth state changes
      if (firebaseUser) {
        // User is signed in
        try {
          const mappedUser = await mapFirebaseUser(firebaseUser);
          setUser(ensureUserHasToken(mappedUser));
        } catch (error) {
          console.error("Error mapping Firebase user:", error);
          setUser(null);
        }
      } else {
        // User is signed out
        setUser(null);
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [ensureUserHasToken]);

  // For checkTokenExpiration function, we need to update any code that uses mapFirebaseUser
  useEffect(() => {
    // Check if token is expiring soon and refresh it
    const checkTokenExpiration = async () => {
      if (isTokenExpiringSoon(10) && user) {
        await refreshToken();
      }
    };

    const intervalId = setInterval(checkTokenExpiration, 5 * 60 * 1000); // Check every 5 minutes

    return () => clearInterval(intervalId);
  }, [user]);

  // Fetch WordPress user data
  const fetchWordPressUser = async (): Promise<FederatedUser | null> => {
    if (!config.wordPressAjaxUrl) return null;

    try {
      // Get nonce from window if available (added by WordPress plugin)
      const nonce = (window as any).bakedBotAjax?.nonce || "";

      const response = await axios.post<WordPressUserResponse>(
        config.wordPressAjaxUrl,
        {
          action: "bakedbot_get_user_info",
          nonce,
        }
      );

      // If response indicates user is logged in, return user data
      if (response.data && response.data.data && response.data.data.loggedIn) {
        const wpUser = response.data.data;

        // If the user has an auth token from the response, store it
        if (wpUser.authToken) {
          const remember = isRememberAcrossSitesEnabled();
          tokenStorage.saveToken(wpUser.authToken, 60, remember);
        }

        return {
          id: wpUser.userId.toString(),
          displayName: wpUser.displayName,
          email: wpUser.email,
          photoURL: null, // WordPress API doesn't typically provide this
          isAnonymous: false,
          wordPressId: wpUser.userId,
          bakedBotId: wpUser.bakedBotId,
          synced: wpUser.synced,
          authToken: wpUser.authToken, // Store the auth token in the user object
          source: "wordpress",
        };
      }

      return null;
    } catch (error) {
      console.error("Error fetching WordPress user:", error);
      return null;
    }
  };

  // Sync user accounts between WordPress and BakedBot
  const syncUserAccounts = async (): Promise<boolean> => {
    if (!user || !config.wordPressAjaxUrl || !config.syncUsers) return false;

    try {
      // Only sync if we have both a WordPress user and a BakedBot ID
      if (user.source === "firebase" && user.id) {
        const nonce = (window as any).bakedBotAjax?.nonce || "";

        // Get the Firebase ID token
        const firebaseUser = auth.currentUser;
        if (!firebaseUser) return false;

        const idToken = await firebaseUser.getIdToken();

        // Store the token securely
        tokenStorage.saveToken(
          idToken,
          60, // Default to 60 minutes
          isFederatedMode // Remember across sites if in federated mode
        );

        // Call the sync endpoint
        const response = await axios.post(config.wordPressAjaxUrl, {
          action: "bakedbot_sync_user",
          nonce,
          bakedbot_id: user.id,
          auth_token: idToken,
        });

        if (response.data && response.data.success) {
          // Update our local user with the synced info
          setUser({
            ...user,
            wordPressId: response.data.data?.userId,
            synced: true,
          });
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Error syncing user accounts:", error);
      return false;
    }
  };

  // Verify a federated token
  const verifyToken = async (token: string): Promise<boolean> => {
    if (!config.wordPressAjaxUrl || !token) return false;

    try {
      const nonce = (window as any).bakedBotAjax?.nonce || "";

      // We need a user ID to verify - use current if available
      const userId = user?.id || "";

      const response = await axios.post(config.wordPressAjaxUrl, {
        action: "bakedbot_verify_token",
        nonce,
        bakedbot_id: userId,
        auth_token: token,
      });

      return !!(response.data && response.data.success);
    } catch (error) {
      console.error("Error verifying token:", error);
      return false;
    }
  };

  // Refresh the authentication token
  const refreshToken = async (): Promise<boolean> => {
    try {
      // For Firebase auth, get a fresh token
      if (isBakedBotMode || (isFederatedMode && user?.source === "firebase")) {
        const firebaseUser = auth.currentUser;
        if (!firebaseUser) return false;

        // Force refresh the token
        const idToken = await firebaseUser.getIdToken(true);

        // Store the refreshed token
        const remember = tokenStorage.isFederatedRememberEnabled();
        tokenStorage.saveToken(idToken, 60, remember);

        return true;
      }

      // For WordPress auth, we might need to call an endpoint to refresh
      // This is placeholder for implementing WordPress token refresh
      if (
        isWordPressMode ||
        (isFederatedMode && user?.source === "wordpress")
      ) {
        if (!config.wordPressAjaxUrl) return false;

        // For now, just verify the current token
        const currentToken = tokenStorage.getToken();
        if (currentToken && (await verifyToken(currentToken))) {
          // Token is still valid, extend its expiration
          tokenStorage.updateTokenExpiration(60);
          return true;
        }

        // If token verification failed, we need to re-fetch user data
        const wpUser = await fetchWordPressUser();
        if (wpUser && wpUser.authToken) {
          // Got a fresh token with the user data
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Error refreshing token:", error);
      return false;
    }
  };

  // Logout function that handles all auth modes
  const logout = async (): Promise<void> => {
    try {
      // Firebase logout if we're using Firebase auth
      if (isBakedBotMode || (isFederatedMode && user?.source === "firebase")) {
        await signOut(auth);
      }

      // WordPress logout handling
      if (
        (isWordPressMode || isFederatedMode) &&
        user?.source === "wordpress"
      ) {
        // Call WordPress logout endpoint if configured
        if (config.wordPressAjaxUrl) {
          const wpLogoutUrl = config.wordPressAjaxUrl.replace(
            "admin-ajax.php",
            "wp-login.php?action=logout"
          );

          // Use an iframe to trigger WordPress logout without redirecting the user
          const logoutFrame = document.createElement("iframe");
          logoutFrame.style.display = "none";
          logoutFrame.src = wpLogoutUrl;
          document.body.appendChild(logoutFrame);

          // Remove the iframe after logout likely completed
          setTimeout(() => {
            if (document.body.contains(logoutFrame)) {
              document.body.removeChild(logoutFrame);
            }
          }, 3000);
        }
      }

      // Clear the user
      setUser(null);

      // Remove stored tokens
      tokenStorage.removeToken();
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  return (
    <FederatedAuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        logout,
        syncUserAccounts,
        verifyToken,
        refreshToken,
        isTokenExpiringSoon,
        setRememberAcrossSites,
        isRememberAcrossSitesEnabled,
      }}
    >
      {children}
    </FederatedAuthContext.Provider>
  );
};

// Custom hook for using the auth context
export const useFederatedAuth = () => useContext(FederatedAuthContext);

export default FederatedAuthContext;
