import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import preferencesStorage, {
  CrossSitePreferences,
} from "../utils/preferencesStorage";
import { useAuth } from "./AuthContext";

interface PreferencesContextType {
  preferences: CrossSitePreferences;
  updatePreferences: (updates: Partial<CrossSitePreferences>) => void;
  syncPreferences: () => Promise<boolean>;
  resetPreferences: () => void;
}

const PreferencesContext = createContext<PreferencesContextType | undefined>(
  undefined
);

export const PreferencesProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { user, isAuthenticated } = useAuth();
  const [preferences, setPreferences] = useState<CrossSitePreferences>(
    preferencesStorage.getPreferences()
  );

  // Update preferences locally
  const updatePreferences = (updates: Partial<CrossSitePreferences>) => {
    const updatedPreferences = preferencesStorage.updatePreferences(updates);
    setPreferences(updatedPreferences);
  };

  // Sync preferences with server (simplified version)
  const syncPreferences = async (): Promise<boolean> => {
    // For now, just return true since we've simplified the auth system
    // This can be enhanced later if needed
    return true;
  };

  // Reset preferences to defaults
  const resetPreferences = () => {
    const defaultPrefs: CrossSitePreferences = {
      theme: {
        mode: "light",
        accentColor: "#4a90e2",
      },
      notifications: {
        enabled: true,
        emailNotifications: false,
      },
      chat: {
        showTimestamps: true,
        messageSize: "medium",
      },
      display: {
        fontSize: "medium",
        compactMode: false,
      },
    };

    preferencesStorage.updatePreferences(defaultPrefs);
    setPreferences(defaultPrefs);
  };

  return (
    <PreferencesContext.Provider
      value={{
        preferences,
        updatePreferences,
        syncPreferences,
        resetPreferences,
      }}
    >
      {children}
    </PreferencesContext.Provider>
  );
};

export const usePreferences = () => {
  const context = useContext(PreferencesContext);
  if (context === undefined) {
    throw new Error("usePreferences must be used within a PreferencesProvider");
  }
  return context;
};
