import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useConfig } from "./ConfigContext";

// Define the structure of our theme configuration
interface BakedBotTheme {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  headerColor: string;
  textColor: string;
}

// Default theme values
const defaultTheme: BakedBotTheme = {
  primaryColor: "#22AD85",
  secondaryColor: "#24504A",
  backgroundColor: "#FFFFFF",
  headerColor: "#FFFFFF",
  textColor: "#2C2C2C",
};

// Create the context
const ThemeContext = createContext<{
  theme: BakedBotTheme;
  isWordPressTheme: boolean;
}>({
  theme: defaultTheme,
  isWordPressTheme: false,
});

// Provider component
export const ThemeProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { config } = useConfig();
  const [theme, setTheme] = useState<BakedBotTheme>(defaultTheme);
  const [isWordPressTheme, setIsWordPressTheme] = useState(false);

  useEffect(() => {
    // Check if BakedBotTheme is available in the window object (WordPress integration)
    const windowTheme = (window as any).BakedBotTheme;

    if (windowTheme) {
      // Merge the window theme with default values for any missing properties
      setTheme({
        ...defaultTheme,
        ...windowTheme,
      });
      setIsWordPressTheme(true);
    } else {
      // No WordPress theme, use the defaults
      setTheme(defaultTheme);
      setIsWordPressTheme(false);
    }
  }, []);

  // Apply CSS variables for the theme colors
  useEffect(() => {
    // Apply the theme colors as CSS variables
    document.documentElement.style.setProperty(
      "--primary-color",
      theme.primaryColor
    );
    document.documentElement.style.setProperty(
      "--secondary-color",
      theme.secondaryColor
    );
    document.documentElement.style.setProperty(
      "--background-color",
      theme.backgroundColor
    );
    document.documentElement.style.setProperty(
      "--header-color",
      theme.headerColor
    );
    document.documentElement.style.setProperty("--text-color", theme.textColor);

    // Add a data attribute to indicate if theme settings are disabled
    if (config.disableThemeSettings) {
      document.documentElement.setAttribute(
        "data-disable-theme-settings",
        "true"
      );
    } else {
      document.documentElement.removeAttribute("data-disable-theme-settings");
    }
  }, [theme, config.disableThemeSettings]);

  return (
    <ThemeContext.Provider
      value={{
        theme,
        isWordPressTheme,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook for using the theme
export const useTheme = () => useContext(ThemeContext);

export default ThemeContext;
