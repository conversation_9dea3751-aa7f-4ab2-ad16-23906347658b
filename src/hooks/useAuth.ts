import { useAuth as useNewAuth } from "../contexts/AuthContext";

// Declare global BakedBotConfig for TypeScript
declare global {
  interface Window {
    BakedBotConfig?: {
      embedded?: boolean;
      wpEmbedded?: boolean;
    };
  }
}

/**
 * Check if we're in embedded mode
 */
const isEmbeddedMode = () => {
  return (
    typeof window !== "undefined" &&
    window.BakedBotConfig &&
    (window.BakedBotConfig.embedded || window.BakedBotConfig.wpEmbedded)
  );
};

/**
 * Legacy useAuth hook updated to use the simplified AuthContext
 * @deprecated Use useAuth directly from AuthContext instead
 */
const useAuth = () => {
  const { user: firebaseUser, loading, isAuthenticated, logout } = useNewAuth();

  // Create a simple no-op navigation function for embedded mode
  // In non-embedded mode, this would actually be implemented with react-router-dom
  const navigate = (path: string) => {
    console.log(`[useAuth] Navigate called with path: ${path}`);

    // In embedded mode, we might just want to change the view instead
    if (isEmbeddedMode() && typeof window !== "undefined") {
      // We could potentially trigger a custom event here that the app listens for
      console.log("[useAuth] In embedded mode, not navigating");
    } else if (typeof window !== "undefined") {
      // In normal mode, we would use history.pushState to navigate
      window.location.href = path;
    }
  };

  // Transform the Firebase user to match the expected interface for backward compatibility
  const user = firebaseUser
    ? {
        ...firebaseUser,
        getIdToken: async () => {
          try {
            return await firebaseUser.getIdToken();
          } catch (error) {
            console.warn(
              "[useAuth] No valid auth token found. API calls may fail."
            );
            return "";
          }
        },
        uid: firebaseUser.uid,
      }
    : null;

  const displayName = firebaseUser?.displayName || null;
  const photoURL = firebaseUser?.photoURL || null;

  // Legacy logout function for backward compatibility
  const Logout = async () => {
    console.log("Logging out via useAuth (using simplified AuthContext)");
    await logout();
  };

  return {
    isAuthenticated,
    isLoading: loading,
    user,
    displayName,
    photoURL,
    Logout,
    navigate,
  };
};

export default useAuth;
