import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App.tsx";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { initWebVitals } from "./utils/webVitals";
import { createErrorBoundary } from "./utils/errorHandling";
import BakedBotHeading from "./components/BakedBotHeading";
import HeadlessMenu from "./views/HeadlessMenu";
import { HeadlessHomepage } from "./views/HeadlessHomepage";
import ProductDetailsPage from "./views/ProductDetails/ProductDetailsPage";
import { CartProvider } from "./views/ChatWidget/CartContext";
import { ConfigProvider } from "./contexts/ConfigContext";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { PreferencesProvider } from "./contexts/PreferencesContext";

console.log(
  "BakedBot Debug: main.tsx loaded - React app starting initialization"
);

// Initialize web vitals for performance monitoring
initWebVitals();

// Create an error boundary component
const AppErrorBoundary = createErrorBoundary();

// Create mock router context values for embedded mode
const createMockRouterContext = () => {
  console.log(
    "BakedBot Debug: Creating mock router context to prevent hook errors"
  );

  // Create objects that will be returned by router hooks
  const mockLocation = {
    pathname: "/",
    search: "",
    hash: "",
    state: null,
    key: "default",
  };

  const mockNavigate = (...args: any[]) => {
    console.log("BakedBot Debug: Mock navigate called with:", args);
    return false; // Return expected value type
  };

  const mockParams = {};
  const mockMatch = { params: mockParams, path: "/", url: "/" };

  // Add more mock objects if needed for other router hooks

  return {
    location: mockLocation,
    navigate: mockNavigate,
    params: mockParams,
    match: mockMatch,
  };
};

// Mock router contexts to make hooks work even without real Router
const mockRouterContexts = createMockRouterContext();

// Create a version of App that doesn't use React Router
const NoRouterApp = (props: any) => {
  console.log(
    "BakedBot Debug: NoRouterApp - rendering App without React Router"
  );

  // Create fake context providers to prevent hooks from failing
  const RouterContextMock = React.createContext(mockRouterContexts);
  const LocationContextMock = React.createContext(mockRouterContexts.location);
  const NavigationContextMock = React.createContext({
    basename: "",
    navigator: {},
    static: true,
  });
  const RouteContextMock = React.createContext({ outlet: null, matches: [] });

  // Provide mocked context values to prevent errors with router hooks
  return (
    <React.StrictMode>
      <AppErrorBoundary>
        <RouterContextMock.Provider value={mockRouterContexts}>
          <LocationContextMock.Provider value={mockRouterContexts.location}>
            <NavigationContextMock.Provider
              value={{
                basename: "",
                navigator: mockRouterContexts.navigate,
                static: true,
              }}
            >
              <RouteContextMock.Provider value={{ outlet: null, matches: [] }}>
                <App {...props} />
              </RouteContextMock.Provider>
            </NavigationContextMock.Provider>
          </LocationContextMock.Provider>
        </RouterContextMock.Provider>
      </AppErrorBoundary>
    </React.StrictMode>
  );
};

// Create a standalone component for WordPress integration
const StandaloneApp = (props: any) => {
  console.log(
    "BakedBot Debug: StandaloneApp component called with props:",
    props
  );

  // For embedded mode, we need to check if we're running in a context where React Router might fail
  const isEmbeddedMode =
    props?.config?.embedded || props?.config?.wpEmbedded || false;

  // Skip React Router completely for embedded mode - no try/catch needed since we're bypassing the problematic code
  if (isEmbeddedMode) {
    console.log("BakedBot Debug: Using embedded mode without BrowserRouter");
    // Use our NoRouterApp for embedded mode
    return <NoRouterApp {...props} />;
  }

  // Only use BrowserRouter in non-embedded scenarios
  try {
    // Standard version with BrowserRouter
    return (
      <React.StrictMode>
        <AppErrorBoundary>
          <BrowserRouter>
            <App {...props} />
          </BrowserRouter>
        </AppErrorBoundary>
      </React.StrictMode>
    );
  } catch (error) {
    console.error(
      "BakedBot Debug: Error in StandaloneApp, falling back to embedded mode:",
      error
    );
    // Fallback to non-router version if BrowserRouter causes issues
    return <NoRouterApp {...props} />;
  }
};

// Expose React and ReactDOM globally for WordPress integration
if (typeof window !== "undefined") {
  console.log(
    "BakedBot Debug: Exposing React, ReactDOM, HeadlessMenu and initialization functions to global scope"
  );
  (window as any).React = React;
  (window as any).ReactDOM = ReactDOM;
  (window as any).StandaloneApp = StandaloneApp;
  (window as any).NoRouterApp = NoRouterApp; // Also expose the no-router version
  (window as any).BakedBotMenu = HeadlessMenu; // Expose HeadlessMenu for WordPress plugin
  (window as any).BakedBotHomepage = HeadlessHomepage; // Expose HeadlessHomepage for WordPress plugin
  (window as any).BakedBotProductDetailsPage = ProductDetailsPage; // Expose ProductDetailsPage for WordPress plugin
  (window as any).CartProvider = CartProvider; // Expose CartProvider for add to cart functionality
  (window as any).ConfigProvider = ConfigProvider; // Expose ConfigProvider for WordPress plugin
  (window as any).AuthProvider = AuthProvider; // Expose AuthProvider for WordPress plugin
  (window as any).ThemeProvider = ThemeProvider; // Expose ThemeProvider for WordPress plugin
  (window as any).PreferencesProvider = PreferencesProvider; // Expose PreferencesProvider for WordPress plugin

  // Create and expose initialization functions that WordPress can call
  (window as any).createBakedBotWidget = (containerId: string) => {
    console.log(
      "BakedBot Debug: createBakedBotWidget called for container:",
      containerId
    );
    try {
      const container = document.getElementById(containerId);
      if (!container) {
        console.error(
          `BakedBot Debug: Container with ID ${containerId} not found`
        );
        return;
      }

      const shadowRoot = container.shadowRoot;
      if (!shadowRoot) {
        console.error(
          `BakedBot Debug: Shadow root not found for container ${containerId}`
        );
        return;
      }

      // Create a new element inside the shadow root for the React app
      const appRoot = document.createElement("div");
      appRoot.id = "bakedbot-app-root";
      shadowRoot.appendChild(appRoot);

      // Get config, theme, and user from window
      const config = (window as any).BakedBotConfig || {};

      // Add embedded flag to ensure we use the non-router version
      config.embedded = true;
      config.wpEmbedded = true;
      config.skipRouterCheck = true;
      config.containerId = containerId;

      const theme = (window as any).BakedBotTheme || {};
      const user = (window as any).BakedBotUser || {};

      console.log(
        "BakedBot Debug: Creating ReactDOM root and rendering app with NoRouterApp"
      );

      // Force use of NoRouterApp instead of StandaloneApp to completely avoid React Router
      const root = ReactDOM.createRoot(appRoot);
      root.render(<NoRouterApp config={config} theme={theme} user={user} />);

      console.log("BakedBot Debug: App rendered successfully without router");
      return root;
    } catch (error) {
      console.error("BakedBot Debug: Error initializing widget:", error);

      // Try with a bare-bones approach if all else fails
      try {
        console.log("BakedBot Debug: Attempting fallback with minimal render");
        const container = document.getElementById(containerId);
        if (container) {
          const root = ReactDOM.createRoot(container);
          root.render(
            <div
              className="bakedbot-fallback"
              style={{
                padding: "20px",
                background: "#22AD85",
                color: "white",
                borderRadius: "8px",
              }}
            >
              <BakedBotHeading level={3}>BakedBot Widget</BakedBotHeading>
              <p>Click to open chat</p>
              <button
                style={{
                  background: "#24504A",
                  color: "white",
                  border: "none",
                  padding: "8px 15px",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
                onClick={() => alert("Chat interface would open here")}
              >
                Open Chat
              </button>
            </div>
          );
          return root;
        }
      } catch (fallbackError) {
        console.error(
          "BakedBot Debug: Even fallback rendering failed:",
          fallbackError
        );
      }
    }
  };

  // Create enhancement functions for HeadlessMenu and HeadlessHomepage with full provider hierarchy
  (window as any).enhanceBakedBotMenu = (containerId: string) => {
    console.log("BakedBot Debug: Enhancing menu with ID:", containerId);
    const container = document.getElementById(containerId);
    if (!container) {
      console.error("BakedBot: Menu container not found:", containerId);
      return;
    }

    const config = (window as any).BakedBotMenuConfig || {};
    console.log("BakedBot Debug: Menu config:", config);

    try {
      // Create the full provider hierarchy like in App.tsx
      const menuElement = React.createElement(ConfigProvider, {
        children: React.createElement(ThemeProvider, {
          children: React.createElement(AuthProvider, {
            children: React.createElement(PreferencesProvider, {
              children: React.createElement(CartProvider, {
                children: React.createElement(HeadlessMenu, {
                  ...config,
                  isProgressiveEnhancement: true,
                  containerId: containerId,
                }),
              }),
            }),
          }),
        }),
      });

      const root = ReactDOM.createRoot(container);
      root.render(menuElement);
      console.log(
        "BakedBot Debug: Menu enhanced successfully with full provider hierarchy"
      );

      container.classList.add("enhanced");
    } catch (error) {
      console.error("BakedBot Debug: Error enhancing menu:", error);
    }
  };

  (window as any).enhanceBakedBotHomepage = (containerId: string) => {
    console.log("BakedBot Debug: Enhancing homepage with ID:", containerId);
    const container = document.getElementById(containerId);
    if (!container) {
      console.error("BakedBot: Homepage container not found:", containerId);
      return;
    }

    const config = (window as any).BakedBotHomepageConfig || {};
    console.log("BakedBot Debug: Homepage config:", config);

    try {
      // Create the full provider hierarchy like in App.tsx
      const homepageElement = React.createElement(ConfigProvider, {
        children: React.createElement(ThemeProvider, {
          children: React.createElement(AuthProvider, {
            children: React.createElement(PreferencesProvider, {
              children: React.createElement(CartProvider, {
                children: React.createElement(HeadlessHomepage, {
                  ...config,
                  isProgressiveEnhancement: true,
                  containerId: containerId,
                }),
              }),
            }),
          }),
        }),
      });

      const root = ReactDOM.createRoot(container);
      root.render(homepageElement);
      console.log(
        "BakedBot Debug: Homepage enhanced successfully with full provider hierarchy"
      );

      container.classList.add("enhanced");
    } catch (error) {
      console.error("BakedBot Debug: Error enhancing homepage:", error);
    }
  };

  (window as any).enhanceBakedBotProduct = (containerId: string) => {
    console.log(
      "BakedBot Debug: Enhancing product details with ID:",
      containerId
    );
    const container = document.getElementById(containerId);
    if (!container) {
      console.error("BakedBot: Product container not found:", containerId);
      return;
    }

    const config = (window as any).BakedBotProductConfig || {};
    console.log("BakedBot Debug: Product config:", config);

    try {
      // Create the full provider hierarchy like in App.tsx
      const productElement = React.createElement(ConfigProvider, {
        children: React.createElement(ThemeProvider, {
          children: React.createElement(AuthProvider, {
            children: React.createElement(PreferencesProvider, {
              children: React.createElement(CartProvider, {
                children: React.createElement(ProductDetailsPage, {
                  ...config,
                  isProgressiveEnhancement: true,
                  containerId: containerId,
                }),
              }),
            }),
          }),
        }),
      });

      const root = ReactDOM.createRoot(container);
      root.render(productElement);
      console.log(
        "BakedBot Debug: Product details enhanced successfully with full provider hierarchy"
      );

      container.classList.add("enhanced");
    } catch (error) {
      console.error("BakedBot Debug: Error enhancing product details:", error);
    }
  };

  // Add aliases for the initialization function
  (window as any).BakedBotInitialize = (window as any).createBakedBotWidget;
  (window as any).initBakedBot = (window as any).createBakedBotWidget;

  // BakedBot global object with initialization method
  (window as any).BakedBot = {
    create: (window as any).createBakedBotWidget,
    version: "1.5.0",
  };

  console.log(
    "BakedBot Debug: React app initialization complete, waiting for createBakedBotWidget() call"
  );

  // Self-initialization - find the container and initialize after a delay
  setTimeout(() => {
    console.log("BakedBot Debug: Running self-initialization");

    // Try to find any BakedBot container in the DOM
    const containers = document.querySelectorAll(
      '[id^="bakedbot-widget-root-"], [id^="bakedbot-chatbot-"]'
    );

    if (containers.length > 0) {
      console.log(
        `BakedBot Debug: Found ${containers.length} container(s), initializing first one:`,
        containers[0].id
      );
      (window as any).createBakedBotWidget(containers[0].id);
    } else {
      console.log(
        "BakedBot Debug: No existing containers found, looking for containers every second"
      );

      // Set up an interval to keep looking for containers
      const checkInterval = setInterval(() => {
        const containers = document.querySelectorAll(
          '[id^="bakedbot-widget-root-"], [id^="bakedbot-chatbot-"]'
        );

        if (containers.length > 0) {
          console.log(
            `BakedBot Debug: Found ${containers.length} container(s), initializing first one:`,
            containers[0].id
          );
          (window as any).createBakedBotWidget(containers[0].id);
          clearInterval(checkInterval);
        }
      }, 1000);

      // Stop looking after 10 seconds to avoid infinite checks
      setTimeout(() => clearInterval(checkInterval), 10000);
    }
  }, 500);
}

// Find the root element - try different possible IDs, prioritizing our branded ID
const rootElement =
  document.getElementById("bakedbot-widget-root") ||
  document.getElementById("bakedbot-container") ||
  document.getElementById("root") ||
  document.querySelector("#bakedbot-container div");

// Only render if we found a root element
if (rootElement) {
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <AppErrorBoundary>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </AppErrorBoundary>
    </React.StrictMode>
  );
} else {
  console.error(
    "BakedBot: Could not find mount element for React app. Looking for #bakedbot-widget-root"
  );
}
