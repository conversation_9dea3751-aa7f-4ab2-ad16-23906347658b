@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  @apply box-border p-0 m-0;
}

.li-customer-name {
  color: white;
  margin-bottom: 5px;
  background-color: #414141;
  width: 50%;
  border-radius: 5px;
  padding: 5px;
  display: flex;
}

.li-customer-name label {
  width: 100%;
  margin-left: 5px;
}

.bb-sm-feedback-buttons {
  @apply flex justify-end mt-1;
  flex-direction: row-reverse;
}

.bb-sm-right-buttons {
  @apply flex gap-2;
}

.bb-sm-feedback-button {
  @apply p-1 rounded hover:bg-gray-700 transition-colors;
}

.bb-sm-feedback-given {
  @apply text-blue-500;
}

