import axios from "axios";
import tokenStorage from "./tokenStorage";

export const BASE_URL = import.meta.env.VITE_BASE_URL as string;
export const VITE_BAKED_BOT_API = import.meta.env.VITE_BAKED_BOT_API as string;
const WS_BASE_URL = VITE_BAKED_BOT_API.replace(/^http:/, "ws:").replace(
  /^https:/,
  "wss:"
);
const VITE_BAKED_BOT_API_KEY = import.meta.env.VITE_BAKED_BOT_API_KEY as string;

const WS_CHAT_ENDPOINT = "/public/products/chats/ws";

// Type definitions for BakedBot API
export interface ProductVariant {
  id: string;
  product_id: string;
  display_weight: string;
  latest_price: number;
  medical: boolean;
  recreational: boolean;
}

export interface GroupedProduct {
  meta_sku: string;
  product_name: string;
  brand_name: string;
  category: string;
  subcategory?: string;
  image_url: string;
  base_price: number;
  price_range: { min: number; max: number };
  percentage_thc?: number;
  percentage_cbd?: number;
  description?: string;
  product_short_description?: string;
  variants: ProductVariant[];
  is_featured?: boolean;
  is_top_pick?: boolean;
  sort_order?: number;
}

export interface Product {
  cann_sku_id: string;
  brand_name: string | null;
  brand_id: number | null;
  url: string;
  image_url: string;
  raw_product_name: string;
  product_name: string;
  raw_weight_string: string | null;
  display_weight: string | null;
  raw_product_category: string | null;
  category: string;
  raw_subcategory: string | null;
  subcategory: string | null;
  product_tags: string[] | null;
  percentage_thc: number | null;
  percentage_cbd: number | null;
  mg_thc: number | null;
  mg_cbd: number | null;
  quantity_per_package: number | null;
  medical: boolean;
  recreational: boolean;
  latest_price: number;
  menu_provider: string;
  retailer_id: string;
  meta_sku: string;
  updated_at: string;
  id: string;
  price: number | null;
  description: string;
  product_id: string;
  discount?: boolean;
  original_price?: number;
  discount_percentage?: number;
  featured?: boolean;
  best_value?: boolean;
  is_featured?: boolean;
  is_top_pick?: boolean;
  sort_order?: number;
}

export interface ProductResponse {
  products: {
    meta_sku: string;
    retailer_id: string;
    products: Product[];
  }[];
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  };
}

export interface GroupedProductResponse {
  products: GroupedProduct[];
  pagination: {
    total: number;
    count?: number;
    per_page: number;
    current_page?: number;
    total_pages?: number;
    // API actually returns camelCase - handle both formats
    currentPage?: number;
    totalPages?: number;
  };
}

export interface FilterOptions {
  brands: string[];
  categories: string[];
  subcategories: string[];
}

export interface FeaturedProductsResponse {
  featured_products: GroupedProduct[];
  top_picks: GroupedProduct[];
  total: number;
}

export interface TopPicksResponse {
  top_picks: GroupedProduct[];
  total: number;
}

/**
 * Debug utility to help troubleshoot token issues
 * Only logs in development mode
 */
export const debugAuthToken = () => {
  if (process.env.NODE_ENV === "development") {
    try {
      const storedToken = tokenStorage.getToken();
      console.log("Debug Auth Token:");
      console.log("- Token exists:", !!storedToken);

      if (storedToken) {
        const expiration = tokenStorage.getTokenExpiration();
        if (expiration) {
          const expiresIn = new Date(expiration).toLocaleString();
          const minutesLeft = Math.round(
            (expiration - Date.now()) / (60 * 1000)
          );
          console.log("- Token expires:", expiresIn);
          console.log("- Minutes left:", minutesLeft);
        }

        console.log(
          "- Remember enabled:",
          tokenStorage.isFederatedRememberEnabled()
        );

        // Log first few chars of token for debugging
        if (storedToken.length > 10) {
          console.log("- Token preview:", `${storedToken.substring(0, 10)}...`);
        }
      }
    } catch (error) {
      console.error("Error debugging token:", error);
    }
  }
};

/**
 * Helper function to ensure a valid token for API requests
 * @param {string | null | undefined} token - The token provided by getIdToken or other source
 * @returns A valid token string or null if no valid token is available
 */
const ensureValidToken = (token: string | null | undefined): string | null => {
  // First check if provided token is valid
  if (token && typeof token === "string" && token.trim() !== "") {
    return token.trim();
  }

  // If not, try to get from token storage
  const storedToken = tokenStorage.getToken();
  if (storedToken) {
    return storedToken;
  }

  // No valid token available
  return null;
};

/**
 * Set up authorization headers for requests
 * @param {string | null | undefined} token - The auth token
 * @returns Object with headers or undefined if no token
 */
const getAuthHeaders = (token: string | null | undefined) => {
  const validToken = ensureValidToken(token);

  // Only add auth headers if we have a valid token
  if (validToken) {
    return { Authorization: `Bearer ${validToken}` };
  }

  return undefined;
};

/**
 * Generic API request function with authentication
 * @param method HTTP method
 * @param endpoint API endpoint
 * @param token Auth token
 * @param data Request body data
 * @returns Response data
 */
const apiRequest = async (
  method: "GET" | "POST" | "PUT" | "DELETE",
  endpoint: string,
  token?: string | null,
  data?: any
) => {
  try {
    const headers = {
      "Content-Type": "application/json",
      ...(getAuthHeaders(token) || {}),
    };

    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers,
      data: method !== "GET" ? data : undefined,
      params: method === "GET" && data ? data : undefined,
    };

    console.log(`API Request: ${method} ${endpoint}`, {
      hasToken: !!getAuthHeaders(token),
      data: method !== "GET" ? data : undefined,
    });

    const response = await axios(config);
    return response.data;
  } catch (error: any) {
    console.error(
      `API Error: ${method} ${endpoint}`,
      error.response?.data || error.message
    );

    // Handle auth errors specifically
    if (error.response?.status === 401 || error.response?.status === 403) {
      console.error("Authentication error. Token may be invalid or expired.");
      // Clear the token if it's invalid
      if (error.response?.data?.detail?.includes("expired")) {
        tokenStorage.removeToken();
      }
    }

    throw error;
  }
};

export const getChats = async (userToken?: string | null) => {
  return bakedBotApiRequest(
    "GET",
    "/public/products/chats",
    undefined,
    userToken
  ).then((res) => {
    console.log("res", res);
    return res.data || [];
  });
};

export const getChatMessages = async (
  chatId: string,
  userToken?: string | null
) => {
  return bakedBotApiRequest(
    "GET",
    `/public/products/chats/${chatId}/messages`,
    undefined,
    userToken
  ).then((res) => {
    console.log("res", res);
    return res.data || [];
  });
};

/**
 * Send a message using WebSocket if possible, with fallback to HTTP
 * @param message Message content to send
 * @param agentId Agent ID for the message
 * @param chatId Chat ID if continuing an existing conversation
 * @param token Auth token
 * @param onStreamUpdate Optional callback function for streaming updates
 * @param location Optional location string
 * @returns Promise resolving to the response data
 */
export const sendMessage = async (
  message: string,
  agentId: string,
  chatId: string | null,
  token?: string,
  onStreamUpdate?: (data: any) => void,
  location?: string
) => {
  return sendMessageViaHttp(message, agentId, chatId, token, location);

  // // Try WebSocket connection first
  // try {
  //   console.log("Attempting WebSocket connection for chat");
  //   return await sendMessageViaWebSocket(
  //     message,
  //     agentId,
  //     chatId,
  //     token,
  //     onStreamUpdate,
  //     location
  //   );
  // } catch (error) {
  //   console.warn("WebSocket connection failed, falling back to HTTP", error);
  //   // Fallback to HTTP if WebSocket fails
  //   return sendMessageViaHttp(message, agentId, chatId, token, location);
  // }
};

/**
 * Send a message via HTTP POST request
 */
const sendMessageViaHttp = async (
  message: string,
  agentId: string,
  chatId: string | null,
  token?: string,
  location?: string
) => {
  return bakedBotApiRequest(
    "POST",
    "/public/products/chat",
    {
      message,
      chat_id: chatId || undefined,
    },
    token
  );
};

/**
 * Send a message via WebSocket connection with reconnection support
 */
const sendMessageViaWebSocket = (
  message: string,
  agentId: string,
  chatId: string | null,
  token?: string,
  onStreamUpdate?: (data: any) => void,
  location?: string
): Promise<any> => {
  return new Promise((resolve, reject) => {
    // Create WebSocket URL with query parameters
    const validToken = ensureValidToken(token);

    // Create query parameters object
    const queryParams = new URLSearchParams();

    // Add API key if available (required for location identification)
    const apiKey = getBakedBotApiKey();
    if (apiKey) {
      queryParams.append("apikey", apiKey);
      console.log("Using API key for WebSocket authentication");
    }

    // Add user token if available (optional for enhanced features)
    if (validToken) {
      queryParams.append("user_token", validToken);
      console.log("Using user token for WebSocket authentication");
    }

    // Add chat_id if available
    if (chatId) {
      queryParams.append("chat_id", chatId);
      console.log(`Continuing conversation with chat ID: ${chatId}`);
    } else {
      console.log("Starting new conversation (no chat ID provided)");
    }

    // Construct the WebSocket URL with query parameters
    const wsUrl = `${WS_BASE_URL}${WS_CHAT_ENDPOINT}?${queryParams.toString()}`;

    console.log(`Connecting to WebSocket: ${wsUrl}`);
    console.log(
      `Original BASE_URL: ${BASE_URL}, WebSocket BASE_URL: ${WS_BASE_URL}`
    );

    // Variables to store the complete response
    const responseData: any = {
      type: "ai",
      content: "",
      message_id: Date.now().toString(),
      data: {
        suggested_next_questions: [],
        products: [],
        images: [],
      },
    };

    // Reconnection parameters
    let reconnectAttempt = 0;
    const maxReconnectAttempts = 2;

    // Track whether we've received a chat_id yet
    let chatIdReceived = false;

    // Connection timeout reference
    let connectionTimeout: NodeJS.Timeout | null = null;

    // Function to create and set up the WebSocket
    const setupWebSocket = () => {
      try {
        // Clear any existing timeout
        if (connectionTimeout) {
          clearTimeout(connectionTimeout);
        }

        // Set a new timeout
        connectionTimeout = setTimeout(() => {
          console.error(
            `WebSocket connection timeout (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            })`
          );

          if (socket && socket.readyState !== WebSocket.CLOSED) {
            socket.close();
          }

          if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            reconnectAttempt++;
            setupWebSocket();
          } else {
            reject(new Error("WebSocket connection timeout after max retries"));
          }
        }, 5000);

        // For standard WebSocket API, we can't directly set headers in the constructor
        // We're already using query parameters for authentication
        console.log(`Connecting to: ${wsUrl}`);
        const socket = new WebSocket(wsUrl);

        // Handle socket events
        socket.onopen = () => {
          console.log(
            `WebSocket connection established successfully (attempt ${
              reconnectAttempt + 1
            }/${maxReconnectAttempts + 1})`
          );
          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          // Send the message as JSON
          // Based on the server's expected format
          const payload = {
            message: message,
            // agent_id: agentId,
            // Chat ID is already in the URL as a query parameter
          };

          console.log(
            "Sending message via WebSocket:",
            message.substring(0, 50) + (message.length > 50 ? "..." : "")
          );
          socket.send(JSON.stringify(payload));
        };

        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            // Debug log to see the full structure of the response
            console.log(
              "WebSocket raw response:",
              JSON.stringify(data).substring(0, 200) + "..."
            );
            console.log(
              "WebSocket response type:",
              data.type || "unknown type"
            );

            // Special handling for different message formats
            if (data.type === "chat_response") {
              console.log("Received chat_response type message");

              // In this format, the actual message content is nested inside a "message" object
              if (data.message && typeof data.message === "object") {
                console.log("Processing nested message structure");

                // Extract data from the nested message object
                const messageObj = data.message;

                // Log the message structure for debugging
                console.log(
                  "Message object structure:",
                  Object.keys(messageObj).join(", ")
                );

                // Update response data with the correct fields from the nested structure
                responseData.content = messageObj.content || "";
                responseData.chat_id =
                  messageObj.chat_id || responseData.chat_id;
                responseData.message_id =
                  messageObj.message_id || responseData.message_id;

                // Handle nested data structure if it exists
                if (messageObj.data) {
                  console.log(
                    "Message contains data:",
                    Object.keys(messageObj.data).join(", ")
                  );

                  // Update suggested questions and other data properties
                  if (messageObj.data.suggested_next_questions) {
                    responseData.data.suggested_next_questions =
                      messageObj.data.suggested_next_questions;
                  }

                  if (messageObj.data.products) {
                    responseData.data.products = messageObj.data.products;
                  }

                  if (messageObj.data.images) {
                    responseData.data.images = messageObj.data.images;
                  }
                }

                // Ensure content is always a string before resolving
                if (typeof responseData.content !== "string") {
                  console.warn(
                    "Content is not a string, converting to empty string"
                  );
                  responseData.content = "";
                }

                // Call the stream update callback with final response
                if (onStreamUpdate && typeof onStreamUpdate === "function") {
                  onStreamUpdate({ ...responseData });
                }

                // Close the connection and resolve
                if (connectionTimeout) {
                  clearTimeout(connectionTimeout);
                  connectionTimeout = null;
                }
                socket.close();
                resolve(responseData);
                return;
              } else {
                // Legacy format where message might be a string
                responseData.content =
                  typeof data.message === "string" ? data.message : "";
              }
            } else {
              // Handle other message types (status updates, streaming content, etc.)

              // Store chatId if received
              if (data.chat_id && !chatIdReceived) {
                responseData.chat_id = data.chat_id;
                chatIdReceived = true;
              }

              // Check for message_id in response
              if (data.message_id && !responseData.message_id) {
                responseData.message_id = data.message_id;
              }

              // Handle status updates and partial content
              if (data.type === "status") {
                // Status update - contains a message with status info
                if (data.message && typeof data.message === "string") {
                  // Just update the status, don't add to content
                  console.log("Status update:", data.message);
                }
              } else if (
                data.content ||
                (data.message && typeof data.message === "string")
              ) {
                // Streaming content or partial response
                const newContent =
                  data.content ||
                  (typeof data.message === "string" ? data.message : "") ||
                  "";
                if (typeof newContent === "string") {
                  responseData.content += newContent;
                }
              }

              // Add any suggested questions received
              if (data.data?.suggested_next_questions) {
                responseData.data.suggested_next_questions =
                  data.data.suggested_next_questions;
              }

              // Add any products received
              if (data.data?.products) {
                responseData.data.products = data.data.products;
              }

              // Add any images received
              if (data.data?.images) {
                responseData.data.images = data.data.images;
              }

              // Call the stream update callback if provided for incremental updates
              if (onStreamUpdate && typeof onStreamUpdate === "function") {
                // Create a safe copy with string content
                const safeResponseData = {
                  ...responseData,
                  content:
                    typeof responseData.content === "string"
                      ? responseData.content
                      : "",
                };
                onStreamUpdate(safeResponseData);
              }

              // Check for final/complete message types
              if (data.type === "final" || data.type === "complete") {
                // Final message - close and resolve

                // Ensure content is always a string before resolving
                if (typeof responseData.content !== "string") {
                  console.warn(
                    "Content is not a string in final message, converting to empty string"
                  );
                  responseData.content = "";
                }

                if (connectionTimeout) {
                  clearTimeout(connectionTimeout);
                  connectionTimeout = null;
                }
                socket.close();
                resolve(responseData);
              }
            }
          } catch (err) {
            console.error("Error parsing WebSocket message:", err);
          }
        };

        socket.onerror = (error) => {
          console.error(
            `WebSocket error (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            }):`,
            error
          );

          if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect after error (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            if (connectionTimeout) {
              clearTimeout(connectionTimeout);
              connectionTimeout = null;
            }
            reconnectAttempt++;
            setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
          } else {
            if (connectionTimeout) {
              clearTimeout(connectionTimeout);
              connectionTimeout = null;
            }
            reject(error);
          }
        };

        socket.onclose = (event) => {
          console.log(
            `WebSocket connection closed (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            }):`,
            event.code,
            event.reason
          );

          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          // If we have some data already, resolve with it
          if (responseData.content || responseData.chat_id) {
            // Ensure content is always a string before resolving from onclose handler
            if (typeof responseData.content !== "string") {
              console.warn(
                "Content is not a string in onclose handler, converting to empty string"
              );
              responseData.content = "";
            }

            resolve(responseData);
          } else if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect after close (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            reconnectAttempt++;
            setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
          } else {
            reject(
              new Error(`WebSocket closed: ${event.code} ${event.reason}`)
            );
          }
        };

        return socket;
      } catch (initError: any) {
        console.error(
          `Error initializing WebSocket (attempt ${reconnectAttempt + 1}/${
            maxReconnectAttempts + 1
          }):`,
          initError
        );

        if (connectionTimeout) {
          clearTimeout(connectionTimeout);
          connectionTimeout = null;
        }

        if (reconnectAttempt < maxReconnectAttempts) {
          console.log(
            `Attempting to reconnect after init error (${
              reconnectAttempt + 1
            }/${maxReconnectAttempts})...`
          );
          reconnectAttempt++;
          setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
        } else {
          reject(initError);
        }
      }
    };

    // Start the initial connection attempt
    setupWebSocket();
  });
};

export const renameChat = async (
  chatId: string,
  newName: string,
  userToken?: string | null
) => {
  return bakedBotApiRequest(
    "PUT",
    `/public/products/chats/${chatId}`,
    {
      name: newName,
    },
    userToken
  );
};

export const deleteChat = async (chatId: string, userToken?: string | null) => {
  return bakedBotApiRequest(
    "DELETE",
    `/public/products/chats/${chatId}`,
    undefined,
    userToken
  );
};

export const recordFeedback = async (
  message_id: string,
  feedback_type: string,
  userToken?: string | null
) => {
  return bakedBotApiRequest(
    "POST",
    "/public/products/chats/feedback",
    {
      message_id,
      feedback_type,
    },
    userToken
  );
};

export const retryMessage = async (
  message_id: string,
  userToken?: string | null
) => {
  return bakedBotApiRequest(
    "POST",
    "/public/products/chats/retry",
    {
      message_id,
    },
    userToken
  );
};

export const checkout = async (
  orderData: {
    items: Array<{
      product_id: string;
      quantity: number;
    }>;
    user: {
      email: string;
      phone: string;
      firstName: string;
      lastName: string;
    };
    shipping_address: {
      name: string;
      line1: string;
      city: string;
      state: string;
      postal_code: string;
      country: string;
      phone: string;
    };
  },
  userToken?: string | null
) => {
  try {
    return await bakedBotApiRequest(
      "POST",
      "/orders/checkout",
      orderData,
      userToken
    ).then((res) => {
      return { success: true, message: res.message };
    });
  } catch (error) {
    console.error("Error processing checkout:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Unknown checkout error",
    };
  }
};

/**
 * Helper function to get API key from BakedBotConfig
 */
const getBakedBotApiKey = (): string => {
  const config = (window as any).BakedBotConfig || {};
  return config.apiKey || VITE_BAKED_BOT_API_KEY || "";
};

/**
 * Set up authorization headers for BakedBot API requests
 * Supports dual authentication: API key (required) + user token (optional)
 */
const getBakedBotAuthHeaders = (userToken?: string | null) => {
  const apiKey = getBakedBotApiKey();
  const validUserToken = ensureValidToken(userToken);

  return {
    "Content-Type": "application/json",
    // API key is required for location identification
    ...(apiKey ? { Authorization: `Bearer ${apiKey}` } : {}),
    // User token is optional for enhanced features (chat history, etc.)
    ...(validUserToken ? { "x-user-token": validUserToken } : {}),
  };
};

/**
 * Generic BakedBot API request function
 * Supports dual authentication: API key (required) + user token (optional)
 */
export const bakedBotApiRequest = async (
  method: "GET" | "POST" | "PUT" | "DELETE",
  endpoint: string,
  data?: any,
  userToken?: string | null
) => {
  try {
    const headers = getBakedBotAuthHeaders(userToken);

    const config = {
      method,
      url: `${VITE_BAKED_BOT_API}${endpoint}`,
      headers,
      data: method !== "GET" ? data : undefined,
      params: method === "GET" && data ? data : undefined,
    };

    console.log(`BakedBot API Request: ${method} ${endpoint}`, {
      hasApiKey: !!getBakedBotApiKey(),
      hasUserToken: !!ensureValidToken(userToken),
      data: method !== "GET" ? data : undefined,
    });

    const response = await axios(config);
    return response.data;
  } catch (error: any) {
    console.error(
      `BakedBot API Error: ${method} ${endpoint}`,
      error.response?.data || error.message
    );
    throw error;
  }
};

/**
 * Fetch products from BakedBot API (legacy - returns individual products)
 */
export const fetchProducts = async (page = 1): Promise<ProductResponse> => {
  return bakedBotApiRequest(
    "GET",
    `/public/products?page=${page}&grouped=false`
  );
};

/**
 * Fetch grouped products from BakedBot API (new default)
 */
export const fetchGroupedProducts = async (
  page = 1,
  filters: {
    brand?: string;
    category?: string;
    subcategory?: string;
    q?: string;
    per_page?: number;
    sort?: string;
    direction?: "asc" | "desc";
  } = {}
): Promise<GroupedProductResponse> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());

  if (filters.brand) params.append("brand", filters.brand);
  if (filters.category) params.append("category", filters.category);
  if (filters.subcategory) params.append("subcategory", filters.subcategory);
  if (filters.q) params.append("q", filters.q);
  if (filters.per_page) params.append("per_page", filters.per_page.toString());
  if (filters.sort) params.append("sort", filters.sort);
  if (filters.direction) params.append("direction", filters.direction);

  const endpoint = `/public/products?${params.toString()}`;
  console.log("BakedBot API URL Debug:", {
    endpoint,
    fullUrl: `${VITE_BAKED_BOT_API}${endpoint}`,
    params: Object.fromEntries(params.entries()),
  });

  return bakedBotApiRequest("GET", endpoint);
};

/**
 * Fetch a single product by ID or meta_sku
 * @param identifier - Either numeric ID or meta_sku string
 * @returns Promise<GroupedProduct | null>
 */
export const fetchSingleProduct = async (
  identifier: string | number
): Promise<GroupedProduct | null> => {
  try {
    // Check if identifier is numeric (product ID) or string (meta_sku)
    const isNumericId = !isNaN(Number(identifier));

    let endpoint: string;
    if (isNumericId) {
      // Use numeric ID endpoint
      endpoint = `/public/products/${identifier}`;
    } else {
      // Use meta_sku query parameter (fallback to original working format)
      endpoint = `/public/products/${encodeURIComponent(identifier)}`;
    }

    console.log("Fetching single product:", {
      identifier,
      isNumericId,
      endpoint,
      fullUrl: `${VITE_BAKED_BOT_API}${endpoint}`,
    });

    const response = await bakedBotApiRequest("GET", endpoint);

    console.log("Raw API response:", response);
    console.log("Response type:", typeof response);
    console.log(
      "Response keys:",
      response ? Object.keys(response) : "null/undefined"
    );

    if (isNumericId) {
      // Direct product response for numeric ID
      return response;
    } else {
      // For meta_sku, check if response is direct product or wrapped in products array
      if (response && response.meta_sku) {
        // Direct product response from /public/products/{meta_sku}
        console.log("Found direct product response:", response.product_name);
        return response;
      } else if (
        response &&
        response.products &&
        Array.isArray(response.products)
      ) {
        // Array response for meta_sku query, return first result
        const product = response.products[0];
        console.log("Found product from array:", product);
        return product ?? null;
      } else {
        console.error("Unexpected response format:", response);
        return null;
      }
    }
  } catch (error: any) {
    console.error("Error fetching single product:", error);
    console.error("Error details:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });
    return null;
  }
};

/**
 * Get available filter options
 */
export const fetchFilterOptions = async (): Promise<FilterOptions> => {
  return bakedBotApiRequest("GET", "/public/products/filters");
};

/**
 * Fetch featured products from BakedBot API
 * @param topPicksOnly - Optional filter to only show top picks
 * @param limit - Maximum number of featured products to return (default: 10)
 * @returns Promise<FeaturedProductsResponse>
 */
export const fetchFeaturedProducts = async (
  topPicksOnly?: boolean,
  limit: number = 10
): Promise<FeaturedProductsResponse> => {
  const params = new URLSearchParams();

  if (topPicksOnly) {
    params.append("top_picks_only", "true");
  }

  if (limit) {
    params.append("limit", limit.toString());
  }

  const endpoint = params.toString()
    ? `/public/products/featured?${params.toString()}`
    : `/public/products/featured`;

  console.log("Fetching featured products:", {
    endpoint,
    topPicksOnly,
    limit,
    fullUrl: `${VITE_BAKED_BOT_API}${endpoint}`,
  });

  return bakedBotApiRequest("GET", endpoint);
};

/**
 * Fetch top pick products from BakedBot API
 * @param limit - Maximum number of top picks to return (default: 5)
 * @returns Promise<TopPicksResponse>
 */
export const fetchTopPicks = async (
  limit: number = 5
): Promise<TopPicksResponse> => {
  const params = new URLSearchParams();

  if (limit) {
    params.append("limit", limit.toString());
  }

  const endpoint = params.toString()
    ? `/public/products/top-picks?${params.toString()}`
    : `/public/products/top-picks`;

  console.log("Fetching top picks:", {
    endpoint,
    limit,
    fullUrl: `${VITE_BAKED_BOT_API}${endpoint}`,
  });

  return bakedBotApiRequest("GET", endpoint);
};

/**
 * Search products from BakedBot API
 */
export const searchProducts = async (
  query: string,
  page = 1
): Promise<ProductResponse> => {
  return bakedBotApiRequest("GET", `/public/products/search`, {
    q: query,
    page: page,
  });
};

/**
 * Save theme settings for authenticated users
 */
export const saveThemeSettings = async (
  settings: any,
  userToken?: string | null
) => {
  try {
    return await bakedBotApiRequest(
      "POST",
      "/public/products/users/theme",
      settings,
      userToken
    );
  } catch (error) {
    console.error("Error saving theme settings:", error);
    return false;
  }
};

/**
 * Get theme settings for authenticated users
 */
export const getThemeSettings = async (userToken?: string | null) => {
  try {
    return await bakedBotApiRequest(
      "GET",
      "/public/products/users/theme",
      undefined,
      userToken
    );
  } catch (error) {
    console.error("Error fetching theme settings:", error);
    return null;
  }
};

// Event types for the events API
export interface Event {
  id: string;
  event_id: string;
  event_name: string;
  category: string[];
  start_time: string;
  timezone: string;
  host: string;
  starting_price: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  image: string;
  url: string;
  source: string;
}

export interface EventsSearchRequest {
  query?: string;
  city?: string;
  state?: string;
  limit?: number;
  page?: number;
}

export interface EventsSearchResponse {
  events: Event[];
  total_count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Search events based on location and query
 */
export const searchEvents = async (
  searchParams: EventsSearchRequest,
  userToken?: string | null
): Promise<EventsSearchResponse> => {
  try {
    return await bakedBotApiRequest(
      "POST",
      "/misc/events/search",
      searchParams,
      userToken
    );
  } catch (error) {
    console.error("Error searching events:", error);
    // Return empty results on error
    return {
      events: [],
      total_count: 0,
      page: searchParams.page || 1,
      limit: searchParams.limit || 20,
      total_pages: 0,
    };
  }
};

// Menu Settings Types
export interface MenuSetting {
  id: number;
  location_id: number;
  type: "carousel_slide" | "promo_content" | "announcement";
  title: string;
  description: string;
  image_url: string;
  link: string;
  order: number;
  active: boolean;
  start_date: string | null;
  end_date: string | null;
  metadata: any | null;
  created_at: string;
  updated_at: string;
}

/**
 * Fetch menu settings from the backend API
 * @param type Optional filter by menu setting type
 * @param userToken Auth token (optional for public API)
 * @returns Promise resolving to array of menu settings
 */
export const fetchMenuSettings = async (
  type?: "carousel_slide" | "promo_content" | "announcement",
  userToken?: string | null
): Promise<MenuSetting[]> => {
  try {
    const params = new URLSearchParams();

    // Add optional type filter
    if (type) {
      params.append("type", type);
    }

    const endpoint = params.toString()
      ? `/public/products/menu-settings?${params.toString()}`
      : `/public/products/menu-settings`;

    const response = await bakedBotApiRequest(
      "GET",
      endpoint,
      undefined,
      userToken
    );

    // The API returns an array directly, not wrapped in a data property
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error("Error fetching menu settings:", error);
    // Return empty array on error
    return [];
  }
};

/**
 * Note: To avoid CSS conflicts with WordPress themes:
 * - All CSS classes should be prefixed with "bakedbot-" or "bb-"
 * - Critical styles should use higher specificity or !important
 * - Container elements should use Shadow DOM or CSS isolation techniques
 */

/**
 * Customer Profile and Orders API Functions
 */

// Type definitions for customer endpoints
export interface CustomerOrder {
  id: number;
  user_id: number;
  total_amount: number;
  status: string;
  created_at: string;
  items: Array<{
    product_id: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    product_data: {
      name: string;
      description: string;
    };
  }>;
  shipping_address: {
    name: string;
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

export interface CustomerOrdersResponse {
  results: CustomerOrder[];
  nextCursor: string | null;
  prevCursor: string | null;
  limit: number;
}

export interface CustomerProfile {
  id: number;
  created_at: string;
  updated_at: string;
  location_id: number;
  anonymous_id: string | null;
  external_id: string | null;
  auth_id: string;
  email: string;
  phone: string;
  devices: any | null;
  data: {
    first_name: string;
    last_name: string;
  };
  timezone: string | null;
  locale: string | null;
  birth_date: string;
  vip: boolean;
  name: string;
}

/**
 * Fetch customer orders (requires authenticated user)
 */
export const fetchCustomerOrders = async (
  userToken?: string | null,
  cursor?: string,
  limit: number = 25
): Promise<CustomerOrdersResponse> => {
  try {
    const validToken = ensureValidToken(userToken);
    if (!validToken) {
      throw new Error("Authentication required to fetch orders");
    }

    const params = new URLSearchParams();
    if (cursor) params.append("cursor", cursor);
    if (limit) params.append("limit", limit.toString());

    const endpoint = params.toString()
      ? `/customer/orders?${params.toString()}`
      : `/customer/orders`;

    const apiKey = getBakedBotApiKey();

    // Use the domain from the API documentation with user JWT token
    const config = {
      method: "GET",
      url: `${VITE_BAKED_BOT_API.replace("/api/", "/")}${endpoint}`,
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "x-user-token": validToken,
        "Content-Type": "application/json",
      },
    };

    console.log(`Customer Orders Request:`, {
      hasToken: !!validToken,
      endpoint,
      fullUrl: config.url,
    });

    const response = await axios(config);
    return response.data;
  } catch (error: any) {
    console.error(
      "Error fetching customer orders:",
      error.response?.data || error.message
    );

    if (error.response?.status === 401 || error.response?.status === 403) {
      console.error("Authentication error. Please log in again.");
      tokenStorage.removeToken();
    }

    throw error;
  }
};

/**
 * Fetch customer profile (requires authenticated user)
 */
export const fetchCustomerProfile = async (
  userToken?: string | null
): Promise<CustomerProfile> => {
  try {
    const validToken = ensureValidToken(userToken);
    if (!validToken) {
      throw new Error("Authentication required to fetch profile");
    }
    const apiKey = getBakedBotApiKey();

    // Use the domain from the API documentation with user JWT token
    const config = {
      method: "GET",
      url: `${VITE_BAKED_BOT_API.replace("/api/", "/")}/customer/me`,
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "x-user-token": validToken,
        "Content-Type": "application/json",
      },
    };

    console.log(`Customer Profile Request:`, {
      hasToken: !!validToken,
      fullUrl: config.url,
    });

    const response = await axios(config);
    return response.data;
  } catch (error: any) {
    console.error(
      "Error fetching customer profile:",
      error.response?.data || error.message
    );

    if (error.response?.status === 401 || error.response?.status === 403) {
      console.error("Authentication error. Please log in again.");
      tokenStorage.removeToken();
    }

    throw error;
  }
};
