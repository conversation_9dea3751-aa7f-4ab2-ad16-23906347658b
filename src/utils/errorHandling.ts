import React from "react";
import { analytics } from "../config/firebase-config";
import { logEvent } from "firebase/analytics";

/**
 * Log an error to Firebase Analytics (used for Crashlytics on web)
 * @param error - The error object
 * @param context - Additional context about where the error occurred
 */
export const logError = (
  error: Error,
  context: Record<string, string> = {}
): void => {
  console.error("Error occurred:", error);

  // Log to Firebase Analytics (which feeds into Crashlytics on web)
  logEvent(analytics, "exception", {
    description: error.message,
    fatal: false,
    ...context,
  });
};

/**
 * Global error boundary for React components
 * @param error - The error that was caught
 * @param componentStack - The component stack trace
 */
export const logComponentError = (
  error: Error,
  componentStack: string
): void => {
  logError(error, {
    type: "react_error",
    componentStack,
  });
};

/**
 * Create a wrapped version of a function that catches and logs errors
 * @param fn - The function to wrap
 * @param context - Additional context about where the function is used
 * @returns A wrapped function that catches and logs errors
 */
export const withErrorHandling = <T, Args extends any[]>(
  fn: (...args: Args) => T,
  context: Record<string, string> = {}
): ((...args: Args) => T | undefined) => {
  return (...args: Args): T | undefined => {
    try {
      return fn(...args);
    } catch (error) {
      if (error instanceof Error) {
        logError(error, context);
      } else {
        logError(new Error(String(error)), context);
      }
      return undefined;
    }
  };
};

/**
 * Create a wrapped version of an async function that catches and logs errors
 * @param fn - The async function to wrap
 * @param context - Additional context about where the function is used
 * @returns A wrapped async function that catches and logs errors
 */
export const withAsyncErrorHandling = <T, Args extends any[]>(
  fn: (...args: Args) => Promise<T>,
  context: Record<string, string> = {}
): ((...args: Args) => Promise<T | undefined>) => {
  return async (...args: Args): Promise<T | undefined> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (error instanceof Error) {
        logError(error, context);
      } else {
        logError(new Error(String(error)), context);
      }
      return undefined;
    }
  };
};

// Simple fallback component for error boundaries
export const ErrorFallback: React.FC = () => {
  return React.createElement("div", null, "Something went wrong.");
};

/**
 * React error boundary component props
 */
export interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * React error boundary component state
 */
export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

/**
 * Create a React error boundary class component
 * @returns A React error boundary class component
 */
export const createErrorBoundary = (): React.ComponentClass<
  ErrorBoundaryProps,
  ErrorBoundaryState
> => {
  return class ErrorBoundary extends React.Component<
    ErrorBoundaryProps,
    ErrorBoundaryState
  > {
    constructor(props: ErrorBoundaryProps) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, info: { componentStack: string }) {
      logComponentError(error, info.componentStack);
    }

    render() {
      if (this.state.hasError) {
        return this.props.fallback || React.createElement(ErrorFallback);
      }

      return this.props.children;
    }
  };
};
