/**
 * Image utilities to prevent direct URL imports
 *
 * This file maps all remote image URLs to local paths in the public directory
 */

// Base URL for all remote images
const REMOTE_BASE_URL = "https://bakedbot-3e8a803d7a8a.herokuapp.com/images";

// Map of remote image paths to local paths
export const imageMap: Record<string, string> = {
  // Product types
  "productType1.png": "/images/productType1.png",
  "productType2.png": "/images/productType2.png",
  "Vape.png": "/images/Vape.png",
  "Edible.png": "/images/Edible.png",
  "Concentrate.png": "/images/Concentrate.png",
  "Topical.png": "/images/Topical.png",
  "Tincture.png": "/images/Tincture.png",
  "Accessories.png": "/images/Accessories.png",

  // Feelings
  "creative.png": "/images/creative.png",
  "energized.png": "/images/energized.png",
  "focused.png": "/images/focused.png",
  "euorphic.png": "/images/euorphic.png",
  "gigly.png": "/images/gigly.png",
  "relaxed.png": "/images/relaxed.png",
  "tingly.png": "/images/tingly.png",
  "stimulated.png": "/images/stimulated.png",

  // Other images
  "pointing.png": "/images/pointing.png",
  "tech_smokey.png": "/images/tech_smokey.png",
  "google-icon.png": "/images/google-icon.png",
  "blunt-smokey-sm.png": "/images/blunt-smokey-sm.png",
  "loading-spinner-white.gif": "/images/loading-spinner-white.gif",
  "StoreHeader.jpeg": "/images/StoreHeader.jpeg",

  // Events
  "events/mcba-logo-2.jpeg": "/images/events/mcba-logo-2.jpeg",
  "events/240-fest.png": "/images/events/240-fest.png",
  "events/240-fest-2.png": "/images/events/240-fest-2.png",
};

/**
 * Converts a remote URL to a local path
 * @param url Remote URL or path
 * @returns Local path
 */
export const getLocalImagePath = (url: string): string => {
  if (!url) return "";

  // If it's already a local path, return it
  if (url.startsWith("/")) {
    return url;
  }

  // Extract the image path from the remote URL
  const imagePath = url.replace(REMOTE_BASE_URL, "").replace(/^\//, "");

  // Return the mapped local path or the original URL if not found
  return imageMap[imagePath] || url;
};

/**
 * Creates a local src attribute for an image tag
 * @param url Remote URL or image name
 * @returns Object with src attribute set to local path
 */
export const localSrc = (url: string) => ({
  src: getLocalImagePath(
    url.includes(REMOTE_BASE_URL) ? url : `${REMOTE_BASE_URL}/${url}`
  ),
});

export default imageMap;
