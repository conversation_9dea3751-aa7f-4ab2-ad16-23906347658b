/* Pure helpers – no React imports here */
export type Range = [number, number];

export interface Filters {
  category: string[];
  subcategory: string[];
  brands: string[];
  priceRange: Range;
  thcRange: Range;
  cbdRange: Range;
}

export const DEFAULT_FILTERS: Filters = {
  category: [],
  subcategory: [],
  brands: [],
  priceRange: [0, 1000],
  thcRange: [0, 100],
  cbdRange: [0, 100],
};

/** Map category → emoji for pills */
export const emojiForCategory = (cat: string) => {
  const l = cat.toLowerCase();
  if (l.includes("flower")) return "🌸";
  if (l.includes("edible")) return "🍫";
  if (l.includes("concentrate")) return "💎";
  if (l.includes("wellness")) return "🧘";
  if (l.includes("accessories")) return "🔥";
  if (l.includes("preroll")) return "🚬";
  if (l.includes("vape")) return "💨";
  if (l.includes("tincture")) return "💧";
  if (l.includes("topical")) return "🧴";
  if (l.includes("pre-roll") || l.includes("preroll")) return "🚬";

  return "🤷";
};
