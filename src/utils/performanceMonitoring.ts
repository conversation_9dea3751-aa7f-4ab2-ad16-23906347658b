import { trace, getPerformance } from "firebase/performance";
import { perf } from "../config/firebase-config";

// Define the Trace interface based on Firebase Performance API
interface Trace {
  start: () => void;
  stop: () => void;
  putMetric: (metricName: string, value: number) => void;
  putAttribute: (name: string, value: string) => void;
}

/**
 * Create and start a custom trace
 * @param traceName - Name of the trace
 * @returns The trace object
 */
export const startTrace = (traceName: string): Trace => {
  const customTrace = trace(perf, traceName);
  customTrace.start();
  return customTrace;
};

/**
 * Stop a custom trace
 * @param customTrace - The trace to stop
 */
export const stopTrace = (customTrace: Trace): void => {
  customTrace.stop();
};

/**
 * Record a custom metric with a trace
 * @param customTrace - The trace to add the metric to
 * @param metricName - Name of the metric
 * @param value - Value of the metric
 */
export const recordMetric = (
  customTrace: Trace,
  metricName: string,
  value: number
): void => {
  customTrace.putMetric(metricName, value);
};

/**
 * Create a trace that automatically wraps an async function
 * @param traceName - Name of the trace
 * @param fn - The async function to trace
 * @returns A function that will be traced
 */
export const traceAsync = <T, Args extends any[]>(
  traceName: string,
  fn: (...args: Args) => Promise<T>
): ((...args: Args) => Promise<T>) => {
  return async (...args: Args): Promise<T> => {
    const customTrace = startTrace(traceName);
    try {
      const result = await fn(...args);
      return result;
    } finally {
      stopTrace(customTrace);
    }
  };
};

/**
 * Utility to measure component render time
 * @param componentName - Name of the component
 * @returns An object with start and end functions
 */
export const measureComponentRender = (componentName: string) => {
  const traceName = `render_${componentName}`;
  let customTrace: Trace | null = null;

  return {
    start: () => {
      customTrace = startTrace(traceName);
    },
    end: () => {
      if (customTrace) {
        stopTrace(customTrace);
        customTrace = null;
      }
    },
  };
};
