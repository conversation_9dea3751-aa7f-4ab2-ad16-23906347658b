import tokenStorage from "./tokenStorage";
import axios from "axios";

// Define types for preferences
export interface CrossSitePreferences {
  theme?: {
    mode: "light" | "dark" | "system";
    accentColor?: string;
  };
  notifications?: {
    enabled: boolean;
    emailNotifications: boolean;
  };
  chat?: {
    showTimestamps: boolean;
    messageSize: "small" | "medium" | "large";
  };
  display?: {
    fontSize: "small" | "medium" | "large";
    compactMode: boolean;
  };
  [key: string]: any; // Allow for extensibility
}

// Define the config structure needed for API calls
interface ApiConfig {
  apiUrl?: string;
}

// Default preferences
const defaultPreferences: CrossSitePreferences = {
  theme: {
    mode: "system",
    accentColor: "#4a90e2",
  },
  notifications: {
    enabled: true,
    emailNotifications: false,
  },
  chat: {
    showTimestamps: true,
    messageSize: "medium",
  },
  display: {
    fontSize: "medium",
    compactMode: false,
  },
};

// Storage key
const PREFERENCES_STORAGE_KEY = "bakedbot_preferences";

// Helper to encrypt/decrypt preferences (simple for now, could be enhanced)
const encryptPreferences = (preferences: CrossSitePreferences): string => {
  // Simple encryption - in production would use a proper encryption library
  return btoa(JSON.stringify(preferences));
};

const decryptPreferences = (encrypted: string): CrossSitePreferences => {
  try {
    // Simple decryption
    return JSON.parse(atob(encrypted));
  } catch (e) {
    console.error("Failed to decrypt preferences", e);
    return { ...defaultPreferences };
  }
};

/**
 * Utility for managing cross-site preferences
 */
const preferencesStorage = {
  // Save preferences locally
  savePreferences: (preferences: CrossSitePreferences): void => {
    const encrypted = encryptPreferences(preferences);
    localStorage.setItem(PREFERENCES_STORAGE_KEY, encrypted);
  },

  // Get preferences from local storage
  getPreferences: (): CrossSitePreferences => {
    const encrypted = localStorage.getItem(PREFERENCES_STORAGE_KEY);
    if (!encrypted) {
      return { ...defaultPreferences };
    }
    return decryptPreferences(encrypted);
  },

  // Update specific preference values
  updatePreferences: (
    updates: Partial<CrossSitePreferences>
  ): CrossSitePreferences => {
    const current = preferencesStorage.getPreferences();
    const updated = { ...current };

    // Deep merge updates
    Object.keys(updates).forEach((key) => {
      if (typeof updates[key] === "object" && updates[key] !== null) {
        updated[key] = {
          ...(updated[key] || {}),
          ...updates[key],
        };
      } else {
        updated[key] = updates[key];
      }
    });

    preferencesStorage.savePreferences(updated);
    return updated;
  },

  // Sync preferences with server (for federated auth)
  syncWithServer: async (
    config: ApiConfig,
    preferences?: CrossSitePreferences
  ): Promise<boolean> => {
    const authToken = tokenStorage.getToken();

    if (!authToken || !config.apiUrl) {
      return false;
    }

    const prefsToSync = preferences || preferencesStorage.getPreferences();

    try {
      // Send preferences to server
      await axios.post(
        `${config.apiUrl}/preferences/sync`,
        { preferences: prefsToSync },
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
        }
      );
      return true;
    } catch (error) {
      console.error("Failed to sync preferences with server", error);
      return false;
    }
  },

  // Fetch preferences from server
  fetchFromServer: async (
    config: ApiConfig
  ): Promise<CrossSitePreferences | null> => {
    const authToken = tokenStorage.getToken();

    if (!authToken || !config.apiUrl) {
      return null;
    }

    try {
      const response = await axios.get(`${config.apiUrl}/preferences`, {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.data && response.data.preferences) {
        // Update local storage with server preferences
        preferencesStorage.savePreferences(response.data.preferences);
        return response.data.preferences;
      }

      return null;
    } catch (error) {
      console.error("Failed to fetch preferences from server", error);
      return null;
    }
  },

  // Reset preferences to default
  resetToDefault: (): CrossSitePreferences => {
    const defaults = { ...defaultPreferences };
    preferencesStorage.savePreferences(defaults);
    return defaults;
  },
};

export default preferencesStorage;
