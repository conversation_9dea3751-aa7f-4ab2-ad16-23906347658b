/**
 * Utility functions for formatting sales data for display in the chat interface
 */

type ChartType = "bar" | "line" | "pie" | "doughnut";

/**
 * Format raw sales records into a table format
 * @param records - Array of sales record objects
 * @param title - Optional title for the table
 * @returns Formatted table data
 */
export const formatSalesRecordsAsTable = (records: any[], title?: string) => {
  if (!records || records.length === 0) {
    return null;
  }

  // Extract headers from the first record
  const headers = Object.keys(records[0]).map((key) =>
    key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  );

  // Create rows from records
  const rows = records.map((record) =>
    Object.values(record).map((value) =>
      typeof value === "number" ? value.toFixed(2) : String(value)
    )
  );

  return {
    tableData: {
      headers,
      rows,
      title: title || "Sales Records",
    },
  };
};

/**
 * Format sales data for a bar chart
 * @param data - Array of data objects with labels and values
 * @param title - Chart title
 * @param labelKey - Key in data objects to use for labels
 * @param valueKey - Key in data objects to use for values
 * @returns Formatted chart data
 */
export const formatSalesDataAsBarChart = (
  data: any[],
  title: string,
  labelKey: string = "label",
  valueKey: string = "value"
) => {
  if (!data || data.length === 0) {
    return null;
  }

  return {
    chartData: {
      type: "bar" as ChartType,
      title,
      labels: data.map((item) => item[labelKey]),
      datasets: [
        {
          label: "Sales",
          data: data.map((item) => item[valueKey]),
          backgroundColor: "rgba(101, 113, 95, 0.6)",
          borderColor: "rgba(101, 113, 95, 1)",
        },
      ],
    },
  };
};

/**
 * Format sales data for a line chart (time series)
 * @param data - Array of data objects with date and value
 * @param title - Chart title
 * @param dateKey - Key in data objects to use for dates
 * @param valueKey - Key in data objects to use for values
 * @returns Formatted chart data
 */
export const formatSalesDataAsLineChart = (
  data: any[],
  title: string,
  dateKey: string = "date",
  valueKey: string = "value"
) => {
  if (!data || data.length === 0) {
    return null;
  }

  // Sort data by date
  const sortedData = [...data].sort((a, b) => {
    const dateA = new Date(a[dateKey]);
    const dateB = new Date(b[dateKey]);
    return dateA.getTime() - dateB.getTime();
  });

  return {
    chartData: {
      type: "line" as ChartType,
      title,
      labels: sortedData.map((item) => item[dateKey]),
      datasets: [
        {
          label: "Sales Over Time",
          data: sortedData.map((item) => item[valueKey]),
          borderColor: "rgba(101, 113, 95, 1)",
          backgroundColor: "rgba(101, 113, 95, 0.2)",
          fill: true,
        },
      ],
    },
  };
};

/**
 * Format sales data for a pie chart
 * @param data - Array of data objects with labels and values
 * @param title - Chart title
 * @param labelKey - Key in data objects to use for labels
 * @param valueKey - Key in data objects to use for values
 * @returns Formatted chart data
 */
export const formatSalesDataAsPieChart = (
  data: any[],
  title: string,
  labelKey: string = "label",
  valueKey: string = "value"
) => {
  if (!data || data.length === 0) {
    return null;
  }

  // Generate colors for each segment
  const generateColors = (count: number) => {
    const baseColors = [
      "rgba(101, 113, 95, 0.8)",
      "rgba(75, 192, 192, 0.8)",
      "rgba(153, 102, 255, 0.8)",
      "rgba(255, 159, 64, 0.8)",
      "rgba(255, 99, 132, 0.8)",
      "rgba(54, 162, 235, 0.8)",
      "rgba(255, 206, 86, 0.8)",
    ];

    // If we need more colors than in our base array, generate them
    if (count <= baseColors.length) {
      return baseColors.slice(0, count);
    }

    // Generate additional colors by adjusting opacity
    const colors = [...baseColors];
    for (let i = 0; colors.length < count; i++) {
      const opacity = 0.5 - i * 0.05;
      colors.push(
        baseColors[i % baseColors.length].replace("0.8", opacity.toString())
      );
    }

    return colors;
  };

  return {
    chartData: {
      type: "pie" as ChartType,
      title,
      labels: data.map((item) => item[labelKey]),
      datasets: [
        {
          label: "Sales Distribution",
          data: data.map((item) => item[valueKey]),
          backgroundColor: generateColors(data.length),
        },
      ],
    },
  };
};
