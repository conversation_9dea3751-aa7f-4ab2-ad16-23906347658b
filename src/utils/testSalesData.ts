/**
 * Test utility for sales data visualization
 * This file contains sample data formats that the ChatHistory component should be able to handle
 */

// Sample message with graph data and table type (similar to what we're seeing from the backend)
export const sampleGraphTableMessage = {
  content: "Found 1 sales record. Here are some examples:",
  data: {
    type: "graph",
    graph: {
      graphData: {
        title: "Sales Data Overview",
        type: "table",
        headers: ["Product", "Quantity", "Revenue", "Date", "Location"],
        rows: [
          [
            "Lemon Cherry Gelato Diamond",
            "3.0",
            "$91.20",
            "2025-03-04T10:39:10.675056",
            "High Street Dispensary",
          ],
        ],
      },
      title: "Sales Data Overview",
    },
    sales_data: [
      {
        product_name: "Lemon Cherry Gelato Diamond",
        quantity: 3,
        revenue: 91.2,
        date: "2025-03-04T10:39:10.675056",
        customer: "<PERSON> Thomas",
        location: "High Street Dispensary",
        year: 2025,
        month: 3,
        year_month: "2025-03",
      },
    ],
    sales_data_type: "sales_records",
  },
};

// Sample message with direct sales_data array
export const sampleSalesDataMessage = {
  content: "Here are the top selling products:",
  data: {
    type: "salesData",
    sales_data: [
      {
        product_name: "Blue Dream",
        quantity: 42,
        revenue: 1260.0,
        date: "2023-04-15",
        customer: "<PERSON> Smith",
        location: "San Francisco",
      },
      {
        product_name: "OG Kush",
        quantity: 28,
        revenue: 840.0,
        date: "2023-04-16",
        customer: "Jane Doe",
        location: "Los Angeles",
      },
    ],
    sales_data_type: "top_products",
  },
};

// Sample message with salesData in format expected by the original implementation
export const sampleSalesDataFormatMessage = {
  content: "Here are your sales records:",
  data: {
    type: "salesData",
    salesData: {
      tableData: {
        headers: ["Product", "Quantity", "Revenue", "Date"],
        rows: [
          ["Blue Dream", "42", "$1,260.00", "2023-04-15"],
          ["OG Kush", "28", "$840.00", "2023-04-16"],
        ],
        title: "Recent Sales",
      },
    },
  },
};

// Sample message with chart data
export const sampleChartDataMessage = {
  content: "Here's your revenue trend:",
  data: {
    type: "graph",
    graph: {
      graphData: {
        type: "bar",
        title: "Revenue by Product",
        labels: ["Blue Dream", "OG Kush", "Sour Diesel"],
        datasets: [
          {
            label: "Revenue",
            data: [1260, 840, 1050],
            backgroundColor: "rgba(101, 113, 95, 0.6)",
          },
        ],
      },
      title: "Revenue by Product",
    },
  },
};

// Function to test if the ChatHistory component can handle these formats
export const testSalesDataFormats = () => {
  console.log("Testing sales data formats...");
  console.log(
    "Format 1 (graph with table):",
    sampleGraphTableMessage.data.type === "graph" &&
      sampleGraphTableMessage.data.graph.graphData.type === "table"
  );
  console.log(
    "Format 2 (direct sales_data):",
    Array.isArray(sampleSalesDataMessage.data.sales_data) &&
      sampleSalesDataMessage.data.sales_data.length > 0
  );
  console.log(
    "Format 3 (salesData with tableData):",
    sampleSalesDataFormatMessage.data.type === "salesData" &&
      !!sampleSalesDataFormatMessage.data.salesData.tableData
  );
  console.log(
    "Format 4 (chart data):",
    sampleChartDataMessage.data.type === "graph" &&
      sampleChartDataMessage.data.graph.graphData.type === "bar"
  );
};
