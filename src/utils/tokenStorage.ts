/**
 * Token Storage Utility
 *
 * This utility provides secure token storage with encryption and automatic
 * expiration handling for the federated authentication system.
 */

// Interface for stored token data
interface StoredToken {
  value: string; // The encrypted token value
  expiresAt: number; // Expiration timestamp in milliseconds
  federatedRemember: boolean; // Whether to remember across sites
}

// Key used for storing tokens in localStorage
const TOKEN_STORAGE_KEY = "bakedbot_auth_token";

// Simple encryption key - in a real implementation, this should be more secure
// and potentially derived from user data or stored securely
const ENCRYPTION_KEY = "bakedbot-secure-key";

/**
 * Simple encryption function.
 * Note: In a production environment, use a more robust encryption library.
 */
const encrypt = (text: string): string => {
  // This is a very basic XOR encryption for demonstration purposes
  // In production, use a proper encryption library like crypto-js
  const result: string[] = [];
  for (let i = 0; i < text.length; i++) {
    const charCode =
      text.charCodeAt(i) ^ ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
    result.push(String.fromCharCode(charCode));
  }
  return btoa(result.join("")); // Convert to base64
};

/**
 * Simple decryption function matching the encrypt function above.
 */
const decrypt = (encryptedText: string): string => {
  try {
    const text = atob(encryptedText); // Convert from base64
    const result: string[] = [];
    for (let i = 0; i < text.length; i++) {
      const charCode =
        text.charCodeAt(i) ^
        ENCRYPTION_KEY.charCodeAt(i % ENCRYPTION_KEY.length);
      result.push(String.fromCharCode(charCode));
    }
    return result.join("");
  } catch (error) {
    console.error("Failed to decrypt token:", error);
    return "";
  }
};

/**
 * Saves an authentication token with encryption and expiration time.
 *
 * @param token The authentication token to store
 * @param expiresInMinutes How long the token should be valid (default: 60 minutes)
 * @param federatedRemember Whether to remember this token across sites
 */
export const saveToken = (
  token: string,
  expiresInMinutes: number = 60,
  federatedRemember: boolean = false
): void => {
  try {
    const expiresAt = Date.now() + expiresInMinutes * 60 * 1000;
    const encryptedToken = encrypt(token);

    const tokenData: StoredToken = {
      value: encryptedToken,
      expiresAt,
      federatedRemember,
    };

    localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokenData));
  } catch (error) {
    console.error("Failed to save token:", error);
  }
};

/**
 * Retrieves the stored authentication token if it's valid and not expired.
 *
 * @returns The decrypted token or null if not found or expired
 */
export const getToken = (): string | null => {
  try {
    const tokenData = localStorage.getItem(TOKEN_STORAGE_KEY);

    if (!tokenData) {
      return null;
    }

    const parsed: StoredToken = JSON.parse(tokenData);

    // Check if token has expired
    if (parsed.expiresAt < Date.now()) {
      // Token expired, remove it
      removeToken();
      return null;
    }

    // Decrypt the token
    return decrypt(parsed.value);
  } catch (error) {
    console.error("Failed to retrieve token:", error);
    return null;
  }
};

/**
 * Gets the token expiration time in milliseconds.
 *
 * @returns The expiration timestamp or null if no token found
 */
export const getTokenExpiration = (): number | null => {
  try {
    const tokenData = localStorage.getItem(TOKEN_STORAGE_KEY);

    if (!tokenData) {
      return null;
    }

    const parsed: StoredToken = JSON.parse(tokenData);
    return parsed.expiresAt;
  } catch (error) {
    console.error("Failed to get token expiration:", error);
    return null;
  }
};

/**
 * Checks if the current token is set to be remembered across dispensary sites.
 *
 * @returns Boolean indicating if the token is federated-remember enabled
 */
export const isFederatedRememberEnabled = (): boolean => {
  try {
    const tokenData = localStorage.getItem(TOKEN_STORAGE_KEY);

    if (!tokenData) {
      return false;
    }

    const parsed: StoredToken = JSON.parse(tokenData);
    return parsed.federatedRemember;
  } catch (error) {
    console.error("Failed to check federated remember status:", error);
    return false;
  }
};

/**
 * Checks if the current token will expire soon.
 *
 * @param thresholdMinutes Minutes threshold to consider "expiring soon" (default: 5)
 * @returns Boolean indicating if token will expire soon
 */
export const isTokenExpiringSoon = (thresholdMinutes: number = 5): boolean => {
  const expiration = getTokenExpiration();

  if (!expiration) {
    return false;
  }

  const thresholdMs = thresholdMinutes * 60 * 1000;
  return expiration - Date.now() < thresholdMs;
};

/**
 * Removes the stored token from localStorage.
 */
export const removeToken = (): void => {
  localStorage.removeItem(TOKEN_STORAGE_KEY);
};

/**
 * Updates the token expiration time.
 *
 * @param newExpiresInMinutes New expiration time in minutes
 */
export const updateTokenExpiration = (newExpiresInMinutes: number): void => {
  try {
    const tokenData = localStorage.getItem(TOKEN_STORAGE_KEY);

    if (!tokenData) {
      return;
    }

    const parsed: StoredToken = JSON.parse(tokenData);
    const newExpiresAt = Date.now() + newExpiresInMinutes * 60 * 1000;

    const updatedTokenData: StoredToken = {
      ...parsed,
      expiresAt: newExpiresAt,
    };

    localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(updatedTokenData));
  } catch (error) {
    console.error("Failed to update token expiration:", error);
  }
};

export default {
  saveToken,
  getToken,
  removeToken,
  getTokenExpiration,
  isTokenExpiringSoon,
  updateTokenExpiration,
  isFederatedRememberEnabled,
};
