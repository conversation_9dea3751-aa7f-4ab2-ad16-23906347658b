import { onFID, onLCP, onCLS, onFCP, onTTFB } from "web-vitals";
import { analytics } from "../config/firebase-config";
import { logEvent } from "firebase/analytics";

/**
 * Initialize web vitals measurement
 * This helps Firebase Performance Monitoring collect important web metrics
 */
export const initWebVitals = (): void => {
  // First Input Delay
  onFID((metric) => {
    const { name, value, delta, id } = metric;
    logEvent(analytics, "web_vital", {
      metric_name: name,
      metric_value: value,
      metric_delta: delta,
      metric_id: id,
    });
  });

  // Largest Contentful Paint
  onLCP((metric) => {
    const { name, value, delta, id } = metric;
    logEvent(analytics, "web_vital", {
      metric_name: name,
      metric_value: value,
      metric_delta: delta,
      metric_id: id,
    });
  });

  // Cumulative Layout Shift
  onCLS((metric) => {
    const { name, value, delta, id } = metric;
    logEvent(analytics, "web_vital", {
      metric_name: name,
      metric_value: value,
      metric_delta: delta,
      metric_id: id,
    });
  });

  // First Contentful Paint
  onFCP((metric) => {
    const { name, value, delta, id } = metric;
    logEvent(analytics, "web_vital", {
      metric_name: name,
      metric_value: value,
      metric_delta: delta,
      metric_id: id,
    });
  });

  // Time to First Byte
  onTTFB((metric) => {
    const { name, value, delta, id } = metric;
    logEvent(analytics, "web_vital", {
      metric_name: name,
      metric_value: value,
      metric_delta: delta,
      metric_id: id,
    });
  });
};
