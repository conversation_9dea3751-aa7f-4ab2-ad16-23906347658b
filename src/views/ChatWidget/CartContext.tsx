import React, {
  create<PERSON>ontext,
  ReactNode,
  useState,
  useContext,
  useEffect,
} from "react";
import useAuth from "../../hooks/useAuth";
import { checkout } from "../../utils/api";
import { Product } from "../../utils/api";
import Swal from "sweetalert2";

interface CartItem {
  product: Product;
  quantity: number;
}

interface CartData {
  name: string;
  contact_info: {
    email: string;
    phone: string;
  };
  cart: {
    [key: string]: CartItem;
  };
}

const CART_STORAGE_KEY = "bakedbot_cart";

// Helper functions for localStorage operations
const loadCartFromStorage = (): { [key: string]: CartItem } => {
  try {
    const stored = localStorage.getItem(CART_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error("Error loading cart from localStorage:", error);
    return {};
  }
};

const saveCartToStorage = (cart: { [key: string]: CartItem }) => {
  try {
    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));

    // Dispatch a custom event to notify other components of cart changes
    window.dispatchEvent(new CustomEvent("cart-updated", { detail: cart }));
  } catch (error) {
    console.error("Error saving cart to localStorage:", error);
  }
};

const buildCartData = (
  email: string,
  phone: string,
  name: string,
  cartItems: Map<string, CartItem>
): CartData => {
  return {
    name: name || "Anonymous",
    contact_info: {
      email: email || "",
      phone: phone || "",
    },
    cart: Object.fromEntries(cartItems),
  };
};

interface CartContextType {
  cart: { [key: string]: CartItem };
  addToCart: (product: Product) => void;
  updateQuantity: (productId: string, change: number) => void;
  removeFromCart: (productId: string) => void;
  handleCheckout: (contactInfo: {
    email: string;
    phone: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    birth_date?: string;
    shippingAddress?: {
      line1: string;
      city: string;
      state: string;
      postal_code: string;
    };
    couponCode?: string;
  }) => Promise<boolean>;
}

interface CartProviderProps {
  children: ReactNode;
}

// Create a default cart context that will be used if the provider is not present
// This helps in embedded mode where the CartProvider might not be used
const defaultCartContext: CartContextType = {
  cart: loadCartFromStorage(), // Load from localStorage even in default context
  addToCart: (product) => {
    const currentCart = loadCartFromStorage();
    // Ensure consistent ID usage across components
    const productId = product.product_id ?? product.id;
    const newCart = {
      ...currentCart,
      [productId]: {
        product,
        quantity: currentCart[productId]?.quantity + 1 || 1,
      },
    };
    saveCartToStorage(newCart);

    // Show toast notification
    Swal.fire({
      icon: "success",
      title: "Item Added!",
      text: `${product.product_name} has been added to your cart`,
      toast: true,
      position: "top-end",
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true,
      background: "#10b981",
      color: "#ffffff",
      customClass: {
        popup: "bakedbot-toast",
      },
    });
  },
  updateQuantity: (productId, change) => {
    const currentCart = loadCartFromStorage();
    const item = currentCart[productId];
    if (!item) return;

    const newQuantity = item.quantity + change;
    if (newQuantity <= 0) {
      const { [productId]: _, ...rest } = currentCart;
      saveCartToStorage(rest);
    } else {
      const newCart = {
        ...currentCart,
        [productId]: {
          ...item,
          quantity: newQuantity,
        },
      };
      saveCartToStorage(newCart);
    }
  },
  removeFromCart: (productId) => {
    const currentCart = loadCartFromStorage();
    const { [productId]: _, ...rest } = currentCart;
    saveCartToStorage(rest);
  },
  handleCheckout: async () => {
    return true;
  },
};

// Set the default context value to avoid null errors
export const CartContext = createContext<CartContextType>(defaultCartContext);

// Add a displayName to the context for easy identification
CartContext.displayName = "CartContext";

// Export a useCart hook for easily accessing the cart context
export const useCart = () => {
  const context = useContext(CartContext);
  return context;
};

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [cart, setCart] = useState<{ [key: string]: CartItem }>(() =>
    loadCartFromStorage()
  );
  const { user } = useAuth();

  // Test localStorage access on mount
  useEffect(() => {
    // Test localStorage write/read
    localStorage.setItem("bakedbot_test", "test_value");
    const testValue = localStorage.getItem("bakedbot_test");
    localStorage.removeItem("bakedbot_test");
  }, []);

  // Listen for cart updates from other components/contexts
  useEffect(() => {
    const handleCartUpdate = (event: CustomEvent) => {
      setCart(event.detail);
    };

    window.addEventListener("cart-updated", handleCartUpdate as EventListener);

    return () => {
      window.removeEventListener(
        "cart-updated",
        handleCartUpdate as EventListener
      );
    };
  }, []);

  // Save to localStorage whenever cart changes
  useEffect(() => {
    saveCartToStorage(cart);
  }, [cart]);

  const addToCart = (product: Product) => {
    setCart((prevCart) => {
      // Ensure consistent ID usage across components
      const productId = product.product_id ?? product.id;
      const existingItem = prevCart[productId];

      const newCart = {
        ...prevCart,
        [productId]: {
          product,
          quantity: existingItem ? existingItem.quantity + 1 : 1,
        },
      };

      // Show toast notification
      Swal.fire({
        icon: "success",
        title: "Item Added!",
        text: `${product.product_name} has been added to your cart`,
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        background: "#10b981",
        color: "#ffffff",
        customClass: {
          popup: "bakedbot-toast",
        },
      });

      return newCart;
    });
  };

  const updateQuantity = (productId: string, change: number) => {
    setCart((prevCart) => {
      const item = prevCart[productId];
      if (!item) return prevCart;

      const newQuantity = item.quantity + change;
      if (newQuantity <= 0) {
        const { [productId]: _, ...rest } = prevCart;
        return rest;
      }

      const newCart = {
        ...prevCart,
        [productId]: {
          ...item,
          quantity: newQuantity,
        },
      };
      return newCart;
    });
  };

  const removeFromCart = (productId: string) => {
    setCart((prevCart) => {
      const { [productId]: _, ...rest } = prevCart;
      return rest;
    });
  };

  const handleCheckout = async (contactInfo: {
    email: string;
    phone: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    birth_date?: string;
    shippingAddress?: {
      line1: string;
      city: string;
      state: string;
      postal_code: string;
    };
    couponCode?: string;
  }): Promise<any> => {
    try {
      if (Object.keys(cart).length === 0) {
        return false;
      }

      // Format cart items as required by the API
      const items = Object.entries(cart).map(
        ([productId, { product, quantity }]) => ({
          product_id: product.product_id || product.id,
          quantity: quantity,
        })
      );

      // Split name into first and last name if not provided
      const nameParts = (
        contactInfo.name ||
        user?.displayName ||
        "Anonymous"
      ).split(" ");
      const firstName = contactInfo.firstName || nameParts[0] || "";
      const lastName =
        contactInfo.lastName || nameParts.slice(1).join(" ") || "";

      const orderData = {
        items: items,
        user: {
          email: contactInfo.email,
          phone: contactInfo.phone,
          firstName: firstName,
          lastName: lastName,
          birth_date: contactInfo.birth_date || "",
        },
        shipping_address: {
          name: contactInfo.name || user?.displayName || "Anonymous",
          line1: contactInfo.shippingAddress?.line1 || "",
          city: contactInfo.shippingAddress?.city || "",
          state: contactInfo.shippingAddress?.state || "",
          postal_code: contactInfo.shippingAddress?.postal_code || "",
          country: "USA",
          phone: contactInfo.phone,
        },
      };

      const userToken = user ? await user.getIdToken() : null;

      const response = await checkout(orderData, userToken);
      if (response.success) {
        // Clear the cart after successful checkout
        setCart({});
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error("Checkout error:", error);
      return false;
    }
  };

  return (
    <CartContext.Provider
      value={{
        cart,
        addToCart,
        updateQuantity,
        removeFromCart,
        handleCheckout,
      }}
    >
      {children}
    </CartContext.Provider>
  );
};
