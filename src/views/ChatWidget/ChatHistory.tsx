import React, {
  useState,
  useEffect,
  useRef,
  useC<PERSON>back,
  forwardRef,
  useImperative<PERSON>andle,
} from "react";
import ReactMarkdown from "react-markdown";
// Use a variable for the loading icon URL rather than an import
const loadingIcon =
  "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/loading-spinner-white.gif";
import {
  FaThumbsUp,
  FaThumbsDown,
  FaRedo,
  FaCopy,
  FaLongArrowAltRight,
  FaCheck,
  FaClock,
  FaFire,
  FaTag,
  FaChartBar,
  FaTable,
} from "react-icons/fa";
import ProductCard from "../../components/ProductCard";
import { Product } from "../../utils/api";
import RetailerCard from "../../components/RetailerCard";
import { Js } from "iconsax-react";
import TableRenderer from "../../components/TableRenderer";
import ChartRenderer from "../../components/ChartRenderer";
import { css } from "@emotion/react";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import BakedBotHeading from "../../components/BakedBotHeading";

// Define a basic ChatMessage type if it doesn't exist elsewhere
interface ChatMessage {
  id?: string;
  message_id?: string;
  content?: string;
  type?: string;
  graphData?: any;
  sales_data?: any[];
  salesData?: any;
  special?: string;
  data?: any;
  error?: any;
  specialType?: string;
}

// Shimmer component for the loading effect
const Shimmer = () => (
  <div className="absolute inset-0 w-full h-full">
    <div className="shimmer-wrapper">
      <div className="shimmer"></div>
    </div>
  </div>
);

// ProductCardSkeleton component for shimmer effect
const ProductCardSkeleton = () => (
  <div className="border rounded-lg p-2 overflow-hidden relative">
    {/* Image skeleton */}
    <div className="w-full h-24 rounded-lg bg-gray-200 mb-2 relative overflow-hidden">
      <Shimmer />
    </div>

    {/* Title skeleton */}
    <div className="h-4 bg-gray-200 rounded mb-2 w-3/4 relative overflow-hidden">
      <Shimmer />
    </div>

    {/* Price skeleton */}
    <div className="h-4 bg-gray-200 rounded mb-2 w-1/2 relative overflow-hidden">
      <Shimmer />
    </div>

    {/* Button skeleton */}
    <div className="h-8 bg-gray-200 rounded mt-2 relative overflow-hidden">
      <Shimmer />
    </div>
  </div>
);

// CategoryButtonSkeleton component
const CategoryButtonSkeleton = () => (
  <div className="px-3 py-1 rounded-full bg-gray-200 h-6 w-16 relative overflow-hidden">
    <Shimmer />
  </div>
);

interface ChatHistoryProps {
  chatHistory: any[];
  loading: boolean;
  historyLoading?: boolean;
  cart: { [key: string]: { quantity: number } };
  updateQuantity: (productId: string, quantity: number) => void;
  chatEndRef: React.RefObject<HTMLDivElement>;
  chatTopRef: React.RefObject<HTMLDivElement>;
  onFeedback: (message_id: string, feedbackType: "like" | "dislike") => void;
  onRetry: (message_id: string) => void;
  onAddToCart: (product: Product) => void;
  allowCart?: boolean;
  onProductClick?: (product: Product) => void;
  onDeleteMessage?: (message_id: string) => void;
  onSuggestedQuestionClick?: (question: string) => void;
  products?: any[]; // Add products prop for deals
  onProductTypeSelect?: (productType: any) => void; // Add handler for product type selection
  onFeelingsSelect?: (feelings: string[]) => void; // Add handler for feelings selection
}

const ChatHistory = forwardRef<
  { setWizardSelections: (value: { [key: string]: boolean }) => void },
  ChatHistoryProps
>(
  (
    {
      chatHistory,
      loading,
      historyLoading = false,
      chatEndRef,
      chatTopRef,
      onFeedback,
      onRetry,
      onAddToCart,
      cart,
      allowCart = false,
      updateQuantity,
      onProductClick,
      onDeleteMessage,
      onSuggestedQuestionClick,
      products = [],
      onProductTypeSelect,
      onFeelingsSelect,
    },
    ref
  ) => {
    const chatContainerRef = useRef<HTMLDivElement>(null);
    const [selectedFeelings, setSelectedFeelings] = useState<string[]>([]);
    const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
    const [selectedDealsCategory, setSelectedDealsCategory] = useState("ALL");
    const [dealsCategories, setDealsCategories] = useState([
      { id: "ALL", label: "ALL" },
    ]);

    // State for wizard selections
    const [wizardSelections, setWizardSelections] = useState<{
      [key: string]: boolean;
    }>({});

    // Add these at the component level with other useState declarations
    const [enhancedProducts, setEnhancedProducts] = useState<any[]>([]);
    const [isProductsEnhanced, setIsProductsEnhanced] = useState(false);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      setWizardSelections: (value: { [key: string]: boolean }) => {
        setWizardSelections(value);
      },
    }));

    const [feedbackGiven, setFeedbackGiven] = useState<{
      [key: string]: "like" | "dislike" | null;
    }>({});
    const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([]);
    const [loadingMessageIndex, setLoadingMessageIndex] = useState(0);

    // Witty loading messages that rotate while waiting for response
    const loadingMessages = [
      "Searching for the perfect products...",
      "Consulting with cannabis experts...",
      "Analyzing terpene profiles...",
      "Matching your vibes to our inventory...",
      "Calculating THC to CBD ratios...",
      "Checking local dispensary stock...",
      "Filtering for your preferences...",
      "Brewing some recommendations...",
      "Rolling up the perfect suggestions...",
      "Grinding through our database...",
      "Packing the bowl of knowledge...",
      "Lighting up our neural networks...",
    ];

    // Rotate through loading messages every 3 seconds
    useEffect(() => {
      let interval: NodeJS.Timeout;
      if (loading && !historyLoading) {
        interval = setInterval(() => {
          setLoadingMessageIndex((prev) => (prev + 1) % loadingMessages.length);
        }, 3000);
      }
      return () => clearInterval(interval);
    }, [loading, historyLoading]);

    const handleFeedback = (
      message_id: string,
      feedbackType: "like" | "dislike"
    ) => {
      onFeedback(message_id, feedbackType);
      setFeedbackGiven((prev) => ({ ...prev, [message_id]: feedbackType }));
    };

    const handleCopy = (content: string) => {
      navigator.clipboard.writeText(content).then(() => {
        console.log("Text copied to clipboard");
      });
    };

    useEffect(() => {
      // Automatically scroll to bottom when chat history changes
      chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, [chatHistory]);

    useEffect(() => {
      // Update suggested questions when chat history changes
      if (chatHistory.length > 0) {
        const lastMessage = chatHistory[chatHistory.length - 1];
        if (lastMessage.data?.suggested_next_questions) {
          setSuggestedQuestions(lastMessage.data.suggested_next_questions);
        } else {
          setSuggestedQuestions([]);
        }
      }
    }, [chatHistory]);

    const scrollToBottom = () => {
      chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    // Add a handler for feelings selection
    const handleFeelingsSelection = (feeling: string) => {
      setSelectedFeelings((prev) => {
        // If already selected, remove it
        if (prev.includes(feeling)) {
          // Ensure we don't remove the last feeling
          if (prev.length === 1) return prev;
          return prev.filter((f) => f !== feeling);
        }
        // If not selected and we have fewer than 2, add it
        else if (prev.length < 2) {
          return [...prev, feeling];
        }
        // Otherwise keep the same
        return prev;
      });
    };

    const submitFeelings = () => {
      if (selectedFeelings.length > 0 && onFeelingsSelect) {
        onFeelingsSelect(selectedFeelings);
      }
    };

    // Update deals categories when products change
    useEffect(() => {
      if (products && products.length > 0) {
        // Extract unique categories from products
        const uniqueCategories = new Set();
        uniqueCategories.add("ALL");
        products.forEach((product) => {
          if (product.category) {
            // Normalize category names
            let category = product.category.toUpperCase();

            // Handle special cases
            if (category.includes("PRE ROLL") || category.includes("PREROLL")) {
              category = "PRE-ROLLS";
            } else if (category.includes("FLOWER")) {
              category = "FLOWER";
            } else if (category.includes("VAPE") || category.includes("CART")) {
              category = "VAPE";
            }

            uniqueCategories.add(category);
          }
        });

        // Convert to array and format
        const categoryArray = Array.from(uniqueCategories).map((cat) => ({
          id: cat as string,
          label: cat as string,
        }));

        // Sort categories (keep ALL first)
        const allCategory = categoryArray.find((cat) => cat.id === "ALL");
        const otherCategories = categoryArray
          .filter((cat) => cat.id !== "ALL")
          .sort((a, b) => a.label.localeCompare(b.label));

        setDealsCategories([allCategory!, ...otherCategories.slice(0, 3)]); // Limit to 4 categories total
      }
    }, [products]);

    // Add this after other useEffect blocks to process products only once
    useEffect(() => {
      if (products && products.length > 0 && !isProductsEnhanced) {
        // Sort products to prioritize those without "_missing" in image URLs
        const sortedProducts = [...products].sort((a, b) => {
          const aHasMissing = a.image_url && a.image_url.includes("_missing");
          const bHasMissing = b.image_url && b.image_url.includes("_missing");

          if (aHasMissing && !bHasMissing) return 1;
          if (!aHasMissing && bHasMissing) return -1;
          return 0;
        });

        // Add random deals to products if they don't have them already - but do it only once
        const enhanced = sortedProducts.map((product) => {
          // If the product already has deal properties, return it as is
          if (product.discount || product.featured || product.best_value) {
            return product;
          }

          // Randomly assign deal properties (for demo purposes)
          const random = Math.random();
          if (random < 0.3) {
            const discountPercentage = Math.floor(Math.random() * 20) + 10; // 10-30% discount

            // Keep the latest_price as the real price and calculate original_price by increasing it
            // For example, if latest_price is $100 and discount is 20%, original_price should be $125
            const originalPrice =
              product.latest_price * (100 / (100 - discountPercentage));

            return {
              ...product,
              discount: true,
              original_price: originalPrice,
              discount_percentage: discountPercentage,
              best_value: random < 0.1, // Some are also "best value"
            };
          } else if (random < 0.4) {
            return {
              ...product,
              featured: true,
            };
          }

          return product;
        });

        setEnhancedProducts(enhanced);
        setIsProductsEnhanced(true);
      }
    }, [products, isProductsEnhanced]);

    // Render special message content
    const renderSpecialContent = (message: any) => {
      if (message.specialType === "deals") {
        // Don't re-sort or enhance products on every render
        // Instead use our pre-processed products

        // Filter the enhanced products based on selected category
        const filteredProducts = enhancedProducts
          .filter((product) => {
            if (selectedDealsCategory === "ALL") return true;

            const productCategory = product.category?.toUpperCase() || "";

            // Special case handling
            if (
              selectedDealsCategory === "PRE-ROLLS" &&
              (productCategory.includes("PRE ROLL") ||
                productCategory.includes("PREROLL"))
            ) {
              return true;
            }

            if (
              selectedDealsCategory === "VAPE" &&
              (productCategory.includes("VAPE") ||
                productCategory.includes("CART"))
            ) {
              return true;
            }

            return productCategory.includes(selectedDealsCategory);
          })
          // Re-apply the sorting to ensure products without "_missing" still appear first
          .sort((a, b) => {
            const aHasMissing = a.image_url && a.image_url.includes("_missing");
            const bHasMissing = b.image_url && b.image_url.includes("_missing");

            if (aHasMissing && !bHasMissing) return 1;
            if (!aHasMissing && bHasMissing) return -1;
            return 0;
          });

        // Split into featured and regular products
        const featuredProducts = filteredProducts.filter(
          (product) =>
            product.discount || product.best_value || product.featured
        );

        const regularProducts = filteredProducts.filter(
          (product) =>
            !(product.discount || product.best_value || product.featured)
        );

        // Combine for display, prioritizing featured products (but stable order)
        const productsToShow = [...featuredProducts, ...regularProducts].slice(
          0,
          5
        );

        // Determine if we're in a loading state
        const isDealsLoading =
          message.specialType === "deals" && products.length === 0;

        return (
          <div className="w-full">
            {/* TODAY'S DEALS Header */}
            <BakedBotHeading level={4} className="font-medium text-center mb-2">
              TODAY'S DEALS
            </BakedBotHeading>

            {/* Category Filter Tabs */}
            <div className="relative mb-2">
              {/* Curved line */}
              <div
                className="absolute w-full"
                style={{
                  height: "10px",
                  borderTop: "1px solid #e9a8a8",
                  borderRadius: "50%/6px 6px 0 0",
                  top: "8px",
                  zIndex: 0,
                }}
              ></div>

              <div className="pt-5 pb-1">
                <div className="relative overflow-hidden">
                  {/* Scrollable categories with hidden scrollbar */}
                  <div className="flex justify-center space-x-2 overflow-x-auto px-2 py-1 no-scrollbar">
                    {isDealsLoading
                      ? // Show skeleton loaders for categories when loading
                        Array(4)
                          .fill(0)
                          .map((_, idx) => <CategoryButtonSkeleton key={idx} />)
                      : dealsCategories.map((category) => (
                          <button
                            key={category.id}
                            className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 whitespace-nowrap ${
                              selectedDealsCategory === category.id
                                ? "bg-[#65715F] text-white"
                                : "border border-gray-300 text-gray-700 hover:bg-gray-100"
                            }`}
                            onClick={() =>
                              setSelectedDealsCategory(category.id)
                            }
                          >
                            {category.label}
                          </button>
                        ))}
                  </div>
                  {/* Fade effect on the right side */}
                  <div
                    className="absolute top-0 right-0 h-full w-12 pointer-events-none"
                    style={{
                      background:
                        "linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9) 70%, rgba(255,255,255,1))",
                    }}
                  ></div>
                </div>
              </div>
            </div>

            {/* Products display */}
            {isDealsLoading ? (
              // Show shimmer loading effect when products are loading
              <div className="overflow-x-auto">
                <div className="flex space-x-2 w-max">
                  {Array(5)
                    .fill(0)
                    .map((_, idx) => (
                      <div
                        key={`skeleton-${idx}`}
                        style={{ minWidth: "120px", maxWidth: "120px" }}
                      >
                        <ProductCardSkeleton />
                      </div>
                    ))}
                </div>
              </div>
            ) : productsToShow.length > 0 ? (
              <div className="overflow-x-auto">
                <div className="flex space-x-2 w-max pl-2">
                  {productsToShow.map((product, idx) => (
                    <div
                      key={`${product.id || product.product_id}-${idx}`}
                      style={{ minWidth: "140px", maxWidth: "140px" }}
                      className="relative"
                    >
                      {!!(
                        product.discount ||
                        product.best_value ||
                        product.featured
                      ) && (
                        <div className="absolute top-2 left-2 bg-[#65715F] text-white text-xs px-2 py-1 rounded z-10 flex items-center">
                          <FaTag className="mr-1" size={8} />
                          {product.discount
                            ? `${product.discount_percentage}% OFF`
                            : "FEATURED"}
                        </div>
                      )}
                      <ProductCard
                        product={product}
                        cart={cart}
                        updateQuantity={updateQuantity}
                        onAddToCart={onAddToCart}
                        onProductClick={onProductClick}
                        allowCart={allowCart}
                      />
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500 text-sm">
                No products found in this category.
              </div>
            )}
          </div>
        );
      } else if (message.specialType === "productType") {
        const productTypes = [
          {
            name: "Flower",
            description: "Traditional cannabis buds",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/productType1.png",
          },
          {
            name: "Pre-Roll",
            description: "Ready-to-smoke joints",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/productType2.png",
          },
          {
            name: "Vape",
            description: "Vaporizer cartridges",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Vape.png",
          },
          {
            name: "Edible",
            description: "Cannabis-infused foods",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Edible.png",
          },
          // {
          //   name: "Concentrate",
          //   description: "Cannabis-infused oils",
          //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Concentrate.png",
          // },
          // {
          //   name: "Topical",
          //   description: "Topical products",
          //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Topical.png",
          // },
          // {
          //   name: "Tincture",
          //   description: "Cannabis-infused oils",
          //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Tincture.png",
          // },
          // {
          //   name: "Accessories",
          //   description: "Cannabis accessories",
          //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Accessories.png",
          // },
        ];

        return (
          <div
            className={`w-full ${
              wizardSelections[message.message_id] ? "hidden" : ""
            }`}
          >
            {/* Pills Category-Style Navigation */}
            <div className="relative mb-2 mt-2">
              <div className="pb-1">
                <div className="flex flex-wrap justify-center gap-1 px-3">
                  {productTypes.map((type, index) => (
                    <button
                      key={index}
                      className="px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center border border-gray-300 text-gray-700 hover:bg-[#65715F]/10 hover:border-[#65715F]/50"
                      onClick={() => {
                        onProductTypeSelect && onProductTypeSelect(type);
                        //hide after selection
                        setWizardSelections((prev) => ({
                          ...prev,
                          [message.message_id]: true,
                        }));
                      }}
                    >
                      <div className="h-5 w-5 bg-[#65715F]/10 rounded-full flex items-center justify-center mr-1.5">
                        <img
                          src={type.image}
                          alt={type.name}
                          className="h-[14px] w-[14px] object-contain"
                        />
                      </div>
                      {type.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      } else if (message.specialType === "feelings") {
        const feelings = [
          {
            name: "Creative",
            description: "Induce happiness",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/creative.png",
          },
          {
            name: "Energized",
            description: "Boost imagination",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/energized.png",
          },
          {
            name: "Focused",
            description: "Increase vitality",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/focused.png",
          },
          {
            name: "Euphoric",
            description: "Enhance Concentration",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/euorphic.png",
          },
          {
            name: "Giggly",
            description: "Elevate mood",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/gigly.png",
          },
          {
            name: "Relaxed",
            description: "Induce happiness",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/relaxed.png",
          },
          {
            name: "Tingly",
            description: "Aid relaxation",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/tingly.png",
          },
          {
            name: "Stimulated",
            description: "Aid relaxation",
            image:
              "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/stimulated.png",
          },
        ];

        return (
          <div
            className={`w-full ${
              wizardSelections[message.message_id] ? "hidden" : ""
            }`}
          >
            <div className="relative mb-2 mt-2">
              <div className="pb-1">
                <div className="flex flex-wrap justify-center gap-1 px-3">
                  {feelings.map((feeling, index) => (
                    <button
                      key={index}
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center ${
                        selectedFeelings.includes(feeling.name)
                          ? "bg-[#65715F] text-white"
                          : "border border-gray-300 text-gray-700 hover:bg-[#65715F]/10 hover:border-[#65715F]/50"
                      }`}
                      onClick={() => handleFeelingsSelection(feeling.name)}
                    >
                      <div
                        className={`h-5 w-5 ${
                          selectedFeelings.includes(feeling.name)
                            ? "bg-white/20"
                            : "bg-[#65715F]/10"
                        } rounded-full flex items-center justify-center mr-1.5`}
                      >
                        <img
                          src={feeling.image}
                          alt={feeling.name}
                          className="h-[14px] w-[14px] object-contain"
                        />
                      </div>
                      {feeling.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                submitFeelings();
                //hide after selection
                setWizardSelections((prev) => ({
                  ...prev,
                  [message.message_id]: true,
                }));
              }}
              disabled={selectedFeelings.length === 0}
              className="w-full flex items-center justify-center space-x-2 p-2 rounded-lg mt-3 bg-[var(--primary-color)] text-white disabled:opacity-75"
            >
              Submit <FaLongArrowAltRight className="ml-1" />
            </button>
          </div>
        );
      } else if (
        message.specialType === "salesData" ||
        message.data?.type === "graph" ||
        message.data?.type === "salesData"
      ) {
        console.log("message.data", "HERE!!!!!!!");
        // Handle sales data visualization
        const salesData = message.data?.salesData || message.salesData;

        if (!salesData) {
          // Try to check if we have graph data with table format
          if (message.data?.graph?.graphData?.type === "table") {
            return (
              <div className="w-full">
                <TableRenderer
                  headers={message.data.graph.graphData.headers}
                  rows={message.data.graph.graphData.rows}
                  title={message.data.graph.title || "Sales Data"}
                />
              </div>
            );
          }

          // Try to check if we have direct sales_data array
          if (
            message.data?.sales_data &&
            Array.isArray(message.data.sales_data) &&
            message.data.sales_data.length > 0
          ) {
            const records = message.data.sales_data;
            // Extract headers from the first record
            const headers = Object.keys(records[0])
              .filter(
                (key) =>
                  key !== "year" && key !== "month" && key !== "year_month"
              ) // Filter out non-display fields
              .map((key) =>
                key
                  .split("_")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ")
              );

            // Create rows from records
            const rows = records.map((record) =>
              Object.entries(record)
                .filter(
                  ([key]) =>
                    key !== "year" && key !== "month" && key !== "year_month"
                ) // Filter out non-display fields
                .map(([key, value]) =>
                  typeof value === "number"
                    ? key === "revenue"
                      ? `$${value.toFixed(2)}`
                      : value.toString()
                    : String(value)
                )
            );

            return (
              <div className="w-full">
                <TableRenderer
                  headers={headers}
                  rows={rows}
                  title={
                    message.data.sales_data_type === "top_products"
                      ? "Top Products"
                      : message.data.sales_data_type === "revenue_trend"
                      ? "Revenue Trend"
                      : "Sales Data"
                  }
                />
              </div>
            );
          }

          return (
            <div className="text-center py-2 text-gray-500 text-sm">
              No sales data available.
            </div>
          );
        }

        // Check if we have table data
        if (salesData.tableData) {
          return (
            <div className="w-full">
              <TableRenderer
                headers={salesData.tableData.headers}
                rows={salesData.tableData.rows}
                title={salesData.tableData.title || "Sales Data"}
              />
            </div>
          );
        }

        // Check if we have chart data
        if (salesData.chartData) {
          return (
            <div className="w-full">
              <ChartRenderer chartData={salesData.chartData} />
            </div>
          );
        }

        // If we have raw sales records, format them as a table
        if (Array.isArray(salesData.records) && salesData.records.length > 0) {
          const records = salesData.records;
          // Extract headers from the first record
          const headers = Object.keys(records[0]).map((key) =>
            key
              .split("_")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")
          );

          // Create rows from records
          const rows = records.map((record) =>
            Object.values(record).map((value) =>
              typeof value === "number" ? value.toFixed(2) : String(value)
            )
          );

          return (
            <div className="w-full">
              <TableRenderer
                headers={headers}
                rows={rows}
                title={salesData.title || "Sales Records"}
              />
            </div>
          );
        }

        return (
          <div className="text-center py-2 text-gray-500 text-sm">
            Sales data format not recognized.
          </div>
        );
      } else if (message.type === "feelings_prompt") {
        return (
          <div className="bb-sm-feelings-container w-full mb-3">
            <BakedBotHeading level={4} className="font-medium text-center mb-2">
              How do you want to feel?
            </BakedBotHeading>

            {/* Pills Category-Style Navigation */}
            <div className="relative mb-2">
              {/* Curved line */}
              <div
                className="absolute w-full"
                style={{
                  height: "10px",
                  borderTop: "1px solid #e9a8a8",
                  borderRadius: "50%/6px 6px 0 0",
                  top: "8px",
                  zIndex: 0,
                }}
              ></div>

              <div className="pt-5 pb-1">
                <div className="flex flex-wrap justify-center gap-1 px-3">
                  {message.feelings.map((feeling, index) => (
                    <button
                      key={index}
                      onClick={() => handleFeelingsSelection(feeling.name)}
                      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center ${
                        selectedFeelings.includes(feeling.name)
                          ? "bg-[#65715F] text-white"
                          : "border border-gray-300 text-gray-700 hover:bg-[#65715F]/10 hover:border-[#65715F]/50"
                      }`}
                    >
                      <div
                        className={`h-5 w-5 ${
                          selectedFeelings.includes(feeling.name)
                            ? "bg-white/20"
                            : "bg-[#65715F]/10"
                        } rounded-full flex items-center justify-center mr-1.5`}
                      >
                        <img
                          src={feeling.image}
                          alt={feeling.name}
                          className="h-[14px] w-[14px] object-contain"
                        />
                      </div>
                      {feeling.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <button
              onClick={submitFeelings}
              disabled={selectedFeelings.length === 0}
              className={`w-full py-2 rounded-lg text-white text-sm ${
                selectedFeelings.length === 0
                  ? "bg-gray-400 opacity-50 cursor-not-allowed"
                  : "bg-[#65715F] cursor-pointer"
              }`}
            >
              Submit <FaLongArrowAltRight className="ml-1" />
            </button>
          </div>
        );
      }

      return null;
    };

    const renderMessageContent = (message: ChatMessage) => {
      let tableComponent: React.ReactNode = null;

      // Check for any type of sales data
      if (
        message.data?.graph?.graphData?.type === "table" ||
        (message.data?.sales_data &&
          Array.isArray(message.data.sales_data) &&
          message.data.sales_data.length > 0) ||
        (message.data?.type === "graph" &&
          message.data?.graph?.graphData?.type === "table") ||
        message.data?.salesData ||
        message.data?.type === "salesData" ||
        message.specialType === "salesData"
      ) {
        console.log("Sales data detected in message:", message);

        // CASE 1: Graph data with table type
        if (message.data?.graph?.graphData?.type === "table") {
          console.log("Processing graph data with table type");
          tableComponent = (
            <div className="sales-data-container mt-3 mb-3">
              <TableRenderer
                headers={message.data.graph.graphData.headers || []}
                rows={message.data.graph.graphData.rows || []}
                title={message.data.graph.title || "Sales Data"}
                className="table-highlight"
              />
            </div>
          );
        }
        // CASE 2: Direct sales_data array
        else if (
          message.data?.sales_data &&
          Array.isArray(message.data.sales_data) &&
          message.data.sales_data.length > 0
        ) {
          console.log("Processing sales_data array");
          // Extract headers from the first item's keys, filtering out metadata fields
          const records = message.data.sales_data;
          const headers = Object.keys(records[0])
            .filter(
              (key) => key !== "year" && key !== "month" && key !== "year_month"
            )
            .map((key) =>
              key
                .split("_")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ")
            );

          const rows = records.map((record) =>
            Object.entries(record)
              .filter(
                ([key]) =>
                  key !== "year" && key !== "month" && key !== "year_month"
              )
              .map(([key, value]) =>
                typeof value === "number"
                  ? key === "revenue"
                    ? `$${value.toFixed(2)}`
                    : value.toString()
                  : String(value)
              )
          );

          tableComponent = (
            <div className="sales-data-container mt-3 mb-3">
              <TableRenderer
                headers={headers}
                rows={rows}
                title={
                  message.data.sales_data_type === "top_products"
                    ? "Top Products"
                    : message.data.sales_data_type === "revenue_trend"
                    ? "Revenue Trend"
                    : "Sales Data"
                }
                className="table-highlight"
              />
            </div>
          );
        }
        // CASE 3: Sales data object
        else if (
          message.data?.salesData ||
          message.specialType === "salesData"
        ) {
          const specialContent = renderSpecialContent({
            ...message,
            specialType: "salesData",
          });
          if (specialContent) {
            tableComponent = specialContent;
          }
        }
      }

      // If this is a special message type that's NOT sales data
      if (message.specialType && message.specialType !== "salesData") {
        const specialContent = renderSpecialContent(message);
        if (specialContent) {
          return (
            <div className="bb-sm-special-message-content">
              <div className="bb-sm-message-text mb-2">
                <ReactMarkdown className="bb-sm-prose-invert">
                  {message.content}
                </ReactMarkdown>
              </div>
              {specialContent}
            </div>
          );
        }
      }

      if (message.error || message.data?.error) {
        return (
          <div className="bb-sm-error-message">
            <p>{message.content}</p>
            <button
              onClick={() => message.message_id && onRetry(message.message_id)}
              className="bb-sm-retry-button flex items-center gap-1"
            >
              <FaRedo size={12} /> Retry
            </button>
          </div>
        );
      }

      // Return content with table if we have one
      if (tableComponent) {
        return (
          <div className="bb-sm-message-text">
            <ReactMarkdown className="bb-sm-prose-invert">
              {message.content}
            </ReactMarkdown>
            <div className="sales-table-container mt-4 mb-2 border-2 border-gray-200 rounded-lg overflow-hidden shadow-md">
              {tableComponent}
            </div>
          </div>
        );
      }

      // Default case for regular messages
      return (
        <div className="bb-sm-message-text">
          <ReactMarkdown className="bb-sm-prose-invert">
            {message.content}
          </ReactMarkdown>
          {message.data?.suggested_questions &&
            message.data.suggested_questions.length > 0 && (
              <div className="bb-sm-suggested-questions mt-3">
                <div className="bb-sm-suggested-header text-xs text-gray-500 mb-1">
                  Suggested Questions:
                </div>
                <div className="flex flex-wrap gap-1">
                  {message.data.suggested_questions.map((question, idx) => (
                    <button
                      key={idx}
                      onClick={() =>
                        onSuggestedQuestionClick &&
                        onSuggestedQuestionClick(question)
                      }
                      className="bb-sm-suggested-question-pill"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            )}

          {/* Handle images */}
          {message.data?.images && message.data.images.length > 0 && (
            <div className="bb-sm-images-grid grid grid-cols-2 gap-4 mt-2">
              {message.data.images.map((image: any, index: number) => (
                <img
                  key={`${image.url || ""}-${index}`}
                  src={image.url || ""}
                  alt={image.prompt || "Generated image"}
                  className="w-full h-auto rounded-lg"
                />
              ))}
            </div>
          )}

          {/* Handle products */}
          {message.data?.products && message.data.products.length > 0 && (
            <div className="bb-sm-products-grid grid grid-cols-2 gap-2 mt-2">
              {message.data.products.map((product: any, index: number) => (
                <ProductCard
                  allowCart={allowCart}
                  key={`${product.product_name}-${index}`}
                  product={product}
                  cart={cart}
                  updateQuantity={updateQuantity}
                  onAddToCart={onAddToCart}
                  onProductClick={onProductClick}
                />
              ))}
            </div>
          )}
        </div>
      );
    };

    // Custom loading animation component
    const LoadingAnimation = () => (
      <div className="bb-sm-loading-animation">
        <div className="flex items-center">
          {/* <div className="mr-3">
            <img
              src={loadingIcon}
              className="w-6 h-6 bb-sm-loading-icon"
              alt="Loading"
            />
          </div> */}
          <div className="bb-sm-loading-text">
            {/* <p className="text-md opacity-90">
              {loadingMessages[loadingMessageIndex]}
            </p> */}
            <div className="bb-sm-loading-dots flex mt-1">
              <div className="bb-sm-dot"></div>
              <div className="bb-sm-dot"></div>
              <div className="bb-sm-dot"></div>
            </div>
          </div>
        </div>
      </div>
    );

    // Show centered loading indicator if history is loading
    if (historyLoading) {
      return (
        <div className="flex flex-col h-full justify-center items-center">
          <img
            src={loadingIcon}
            className="w-10 h-10 bb-sm-loading-icon"
            alt="Loading"
          />
          <p className="text-md mt-4 opacity-75">Loading conversation...</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col h-full">
        <style>{`
          .shimmer-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            animation: shimmer 1.5s infinite linear;
            overflow: hidden;
          }
          .shimmer {
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 0.2) 20%,
              rgba(255, 255, 255, 0.5) 50%,
              rgba(255, 255, 255, 0.2) 80%,
              rgba(255, 255, 255, 0) 100%
            );
            transform: translateX(-100%);
          }
          @keyframes shimmer {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(100%);
            }
          }
          .sales-data-container {
            display: block !important;
            visibility: visible !important;
            max-width: 100%;
            overflow-x: auto;
          }
          
          .table-highlight {
            border: 2px solid #65715F;
          }
          
          .sales-table-container {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            margin-top: 1rem;
            margin-bottom: 1rem;
          }
        `}</style>
        <div
          className="bb-sm-chat-messages flex-1 overflow-y-auto"
          ref={chatContainerRef}
        >
          <div ref={chatTopRef} />
          {chatHistory?.map((message, index) => {
            const isBot = message.type === "ai" || message.role === "ai";
            const isLoading =
              loading &&
              !historyLoading &&
              isBot &&
              index === chatHistory.length - 1;
            const messageClass = isBot
              ? `bb-sm-bot-message ${
                  wizardSelections[message.message_id] ? "bg-[#65715F]/50" : ""
                }`
              : "bb-sm-user-message";
            const hasSpecialContent =
              isBot &&
              message.specialType &&
              !wizardSelections[message.message_id];

            return (
              <div
                className={`flex ${
                  isBot ? "justify-start" : "justify-end"
                } mb-4`}
                key={index}
              >
                <div
                  className={`bb-sm-message-container ${
                    isBot ? "bb-sm-bot-container" : ""
                  } ${hasSpecialContent ? "bb-sm-special-container" : ""}`}
                >
                  <div
                    className={`bb-sm-message ${messageClass} ${
                      hasSpecialContent ? "bb-sm-special-message" : ""
                    }`}
                  >
                    {isLoading ? (
                      <LoadingAnimation />
                    ) : (
                      renderMessageContent(message)
                    )}
                  </div>
                  {index > 0 &&
                    isBot &&
                    !isLoading &&
                    !message.error &&
                    !message.specialType && (
                      <div className="bb-sm-feedback-buttons">
                        <div className="bb-sm-right-buttons">
                          <button
                            onClick={() =>
                              handleFeedback(message.message_id, "like")
                            }
                            className={`bb-sm-feedback-button ${
                              feedbackGiven[message.message_id] === "like"
                                ? "bb-sm-feedback-given"
                                : ""
                            }`}
                            disabled={
                              feedbackGiven[message.message_id] === null
                            }
                          >
                            <FaThumbsUp size={12} />
                          </button>
                          <button
                            onClick={() =>
                              handleFeedback(message.message_id, "dislike")
                            }
                            className={`bb-sm-feedback-button ${
                              feedbackGiven[message.message_id] === "dislike"
                                ? "bb-sm-feedback-given"
                                : ""
                            }`}
                            disabled={
                              feedbackGiven[message.message_id] === null
                            }
                          >
                            <FaThumbsDown size={12} />
                          </button>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            );
          })}
          <div ref={chatEndRef} />
        </div>
      </div>
    );
  }
);

export default ChatHistory;
