import {
  FaMinus,
  FaPlus,
  FaClock,
  FaFire,
  FaStar,
  FaPercent,
} from "react-icons/fa";
import { Spinner } from ".";
import { memo, useContext, useRef, useState, useEffect } from "react";
import { useCart } from "./CartContext";
import ProductCard from "../../components/ProductCard";

const DealsView: React.FC<any> = memo(({ products, error, isLoading }) => {
  const { cart, addToCart, updateQuantity } = useCart();
  const [selectedCategory, setSelectedCategory] = useState("ALL");
  const [categories, setCategories] = useState([{ id: "ALL", label: "ALL" }]);

  // Dynamically generate categories based on products
  useEffect(() => {
    if (products && products.length > 0) {
      // Extract unique categories from products
      const uniqueCategories = new Set();
      uniqueCategories.add("ALL");

      products.forEach((product) => {
        if (product.category) {
          // Normalize category names
          let category = product.category.toUpperCase();

          // Handle special cases
          if (category.includes("PRE ROLL") || category.includes("PREROLL")) {
            category = "PRE-ROLLS";
          } else if (category.includes("FLOWER")) {
            category = "FLOWER";
          } else if (category.includes("VAPE") || category.includes("CART")) {
            category = "VAPE";
          }

          uniqueCategories.add(category);
        }
      });

      // Convert to array and format
      const categoryArray = Array.from(uniqueCategories).map((cat) => ({
        id: cat as string,
        label: cat as string,
      }));

      // Sort categories (keep ALL first)
      const allCategory = categoryArray.find((cat) => cat.id === "ALL");
      const otherCategories = categoryArray
        .filter((cat) => cat.id !== "ALL")
        .sort((a, b) => a.label.localeCompare(b.label));

      setCategories([allCategory!, ...otherCategories]);
    }
  }, [products]);

  // Filter products based on selected category
  const filteredProducts = products.filter((product) => {
    if (selectedCategory === "ALL") return true;

    // Match category names (case-insensitive)
    const productCategory = product.category?.toUpperCase() || "";

    // Handle special cases for matching
    if (
      selectedCategory === "PRE-ROLLS" &&
      (productCategory.includes("PRE ROLL") ||
        productCategory.includes("PREROLL"))
    ) {
      return true;
    }

    if (
      selectedCategory === "VAPE" &&
      (productCategory.includes("VAPE") || productCategory.includes("CART"))
    ) {
      return true;
    }

    return productCategory.includes(selectedCategory);
  });

  // Add random deals to products if they don't have them already
  const enhancedProducts = filteredProducts.map((product) => {
    // If the product already has deal properties, return it as is
    if (product.discount || product.featured || product.best_value) {
      return product;
    }

    // Randomly assign deal properties (for demo purposes)
    const random = Math.random();
    if (random < 0.3) {
      const discountPercentage = Math.floor(Math.random() * 20) + 10; // 10-30% discount
      const originalPrice =
        product.latest_price * (100 / (100 - discountPercentage));

      return {
        ...product,
        discount: true,
        original_price: originalPrice,
        discount_percentage: discountPercentage,
        best_value: random < 0.1, // Some are also "best value"
      };
    } else if (random < 0.4) {
      return {
        ...product,
        featured: true,
      };
    }

    return product;
  });

  // Split products into featured (with discount or best value) and regular
  const featuredProducts = enhancedProducts.filter(
    (product) => product.discount || product.best_value || product.featured
  );

  const regularProducts = enhancedProducts.filter(
    (product) => !(product.discount || product.best_value || product.featured)
  );

  return (
    <>
      <div className="bb-sm-store-view h-full flex flex-col">
        <div className="mb-2">
          {/* TODAY'S DEALS Header */}
          <div className="flex flex-col items-center">
            <div className="text-center font-bold text-xl text-[#65715F] mt-3 mb-1">
              TODAY'S DEALS
            </div>
          </div>

          {/* Category Filter Tabs */}
          <div className="relative">
            <div className="relative mb-2 mt-4">
              {/* Scrollable categories with hidden scrollbar */}
              <div className="relative overflow-hidden">
                <div className="flex gap-2 overflow-x-auto no-scrollbar px-2 py-1">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 whitespace-nowrap ${
                        selectedCategory === category.id
                          ? "bg-[#65715F] text-white"
                          : "border border-gray-300 text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={() => setSelectedCategory(category.id)}
                    >
                      {category.label}
                    </button>
                  ))}
                </div>
                {/* Fade effect on the right side */}
                <div
                  className="absolute top-0 right-0 h-full w-12 pointer-events-none"
                  style={{
                    background:
                      "linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9) 70%, rgba(255,255,255,1))",
                  }}
                ></div>
                {/* Fade effect on the left side */}
                <div
                  className="absolute top-0 left-0 h-full w-12 pointer-events-none"
                  style={{
                    background:
                      "linear-gradient(to left, rgba(255,255,255,0), rgba(255,255,255,0.9) 70%, rgba(255,255,255,1))",
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="bb-sm-error-message border border-red-400 text-red-700 px-4 py-3 rounded relative m-4">
            <strong className="font-bold">Error:</strong>
            <span className="block sm:inline"> {error}</span>
          </div>
        )}

        {isLoading ? (
          <div className="bb-sm-loading-container flex justify-center items-center h-64">
            <Spinner />
          </div>
        ) : (
          <div className="flex-1">
            {/* Featured product section header */}
            {featuredProducts.length > 0 && (
              <div className="flex items-center px-3 mb-2">
                <div className="flex items-center">
                  <FaStar className="text-yellow-500 mr-1" size={14} />
                  <span className="font-semibold text-sm">Featured Deals</span>
                </div>
                <div className="ml-auto text-xs text-[#65715F]">
                  <span className="font-medium">Save big today!</span>
                </div>
              </div>
            )}

            {/* Horizontal scrolling featured products */}
            {featuredProducts.length > 0 && (
              <div className="mb-3">
                <div className="overflow-x-auto">
                  <div className="flex space-x-2 w-max px-2">
                    {featuredProducts.map((product, idx) => (
                      <div
                        key={`featured-${
                          product.id || product.product_id
                        }-${idx}`}
                        style={{ minWidth: "140px", maxWidth: "140px" }}
                      >
                        <ProductCard
                          product={product}
                          cart={cart}
                          updateQuantity={updateQuantity}
                          onAddToCart={addToCart}
                          allowCart={true}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* "More Products" section header */}
            {regularProducts.length > 0 && (
              <div className="flex items-center px-3 mb-2 mt-4">
                <div className="flex-1 h-px bg-gray-200"></div>
                <span className="px-2 text-sm font-medium text-gray-500">
                  More Products
                </span>
                <div className="flex-1 h-px bg-gray-200"></div>
              </div>
            )}

            {/* Grid display for regular products */}
            {filteredProducts.length > 0 ? (
              <div className="bb-sm-bakedbot-product-grid grid grid-cols-2 gap-2 px-2">
                {regularProducts.length > 0
                  ? regularProducts.map((product, idx) => (
                      <ProductCard
                        key={`grid-${product.id || product.product_id}-${idx}`}
                        product={product}
                        cart={cart}
                        updateQuantity={updateQuantity}
                        onAddToCart={addToCart}
                        allowCart={true}
                      />
                    ))
                  : featuredProducts.length === 0 && (
                      <div className="col-span-2 text-center py-4 text-gray-500 text-sm">
                        No additional products found in this category.
                      </div>
                    )}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 text-sm">
                No products found in this category.
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
});

export default DealsView;
