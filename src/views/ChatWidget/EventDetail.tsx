import React from "react";
import { FaArrowLeft } from "react-icons/fa";
import moment from "moment";
import { Event } from "../../utils/api";

interface EventDetailsProps {
  event: Event;
  setSelectedItem: React.Dispatch<React.SetStateAction<Event | null>>;
}
const EventDetails: React.FC<EventDetailsProps> = ({
  event,
  setSelectedItem,
}) => {
  return (
    <div className="max-w-lg bg-white rounded-lg shadow-md flex flex-col h-full max-h-[600px]">
      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Info Banner */}
        <div className="mx-2 mt-2 bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-2 rounded shadow-sm text-xs">
          <span className="font-medium">Event Information:</span> This event is
          provided by {event.source}. Use the link below to view full details
          and purchase tickets.
        </div>

        <div className="mx-2 mt-2">
          <div className="relative pb-[56.25%] rounded-md overflow-hidden">
            <img
              src={
                event.image ||
                "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/events/default-event.png"
              }
              alt={event.event_name}
              className="absolute inset-0 w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src =
                  "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/events/default-event.png";
              }}
            />
          </div>
        </div>

        <div className="px-2 py-3 space-y-2">
          <div className="text-sm">
            <span className="font-medium">Event Name:</span> {event.event_name}
          </div>
          <div className="text-sm">
            <span className="font-medium">Date:</span>{" "}
            {moment(event.start_time).format("MMMM Do, YYYY")}
          </div>
          <div className="text-sm">
            <span className="font-medium">Time:</span>{" "}
            {moment(event.start_time).format("h:mm A")}
          </div>
          <div className="text-sm">
            <span className="font-medium">Location:</span> {event.address}
          </div>
          <div className="text-sm">
            <span className="font-medium">City:</span> {event.city},{" "}
            {event.state}
          </div>
          {event.starting_price && (
            <div className="text-sm">
              <span className="font-medium">Starting Price:</span>{" "}
              {event.starting_price}
            </div>
          )}
          {event.host && (
            <div className="text-sm">
              <span className="font-medium">Host:</span> {event.host}
            </div>
          )}
          {event.category && event.category.length > 0 && (
            <div className="text-sm">
              <span className="font-medium">Categories:</span>{" "}
              {event.category.join(", ")}
            </div>
          )}
          {event.timezone && (
            <div className="text-sm">
              <span className="font-medium">Timezone:</span> {event.timezone}
            </div>
          )}
        </div>
      </div>

      {/* Sticky Action Buttons */}
      <div className="border-t bg-white p-2 space-y-2 rounded-b-lg">
        {event.url && (
          <a
            href={event.url}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-[var(--primary-color)] flex items-center justify-center py-2 px-3 text-white font-semibold rounded-lg w-full hover:opacity-90 text-sm"
          >
            <span>View Details & Tickets</span>
          </a>
        )}
      </div>
    </div>
  );
};

export default EventDetails;
