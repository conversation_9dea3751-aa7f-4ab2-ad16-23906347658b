import React, { useState, useEffect, useCallback } from "react";
import EventDetails from "./EventDetail";
import moment from "moment";
import BakedBotHeading from "../../components/BakedBotHeading";
import { searchEvents, Event, EventsSearchRequest } from "../../utils/api";
import useAuth from "../../hooks/useAuth";

// Props interface for location data
interface EventListProps {
  userState?: string | null;
  selectedEvent?: Event | null;
  setSelectedEvent?: React.Dispatch<React.SetStateAction<Event | null>>;
}

const EventList: React.FC<EventListProps> = ({
  userState,
  selectedEvent = null,
  setSelectedEvent,
}) => {
  // Use internal state if no external state management is provided
  const [internalSelectedEvent, setInternalSelectedEvent] =
    useState<Event | null>(null);
  const currentSelectedEvent =
    selectedEvent !== undefined ? selectedEvent : internalSelectedEvent;
  const currentSetSelectedEvent = setSelectedEvent || setInternalSelectedEvent;

  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const { user } = useAuth();

  // Fetch events based on user location
  const fetchEvents = useCallback(
    async (query?: string) => {
      setLoading(true);
      setError(null);

      try {
        const token = user ? await user.getIdToken() : null;
        const searchParams: EventsSearchRequest = {
          limit: 20,
          page: 1,
        };

        // Add location filters if available
        if (userState) {
          searchParams.state = userState;
        }

        // Add search query if provided
        if (query && query.trim()) {
          searchParams.query = query.trim();
        }

        const response = await searchEvents(searchParams, token);
        setEvents(response.events);
      } catch (err) {
        console.error("Error fetching events:", err);
        setError("Failed to load events. Please try again.");
      } finally {
        setLoading(false);
      }
    },
    [userState, user]
  );

  // Load events on component mount and when location changes
  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchEvents(searchQuery);
  };

  const handleRetry = () => {
    fetchEvents(searchQuery);
  };

  return (
    <>
      {!currentSelectedEvent ? (
        <div className="px-4 overflow-y-auto overflow-x-hidden mt-3">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="mb-4">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search events..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-[var(--primary-color)] text-white rounded-lg hover:opacity-90 disabled:opacity-50"
              >
                {loading ? "..." : "Search"}
              </button>
            </div>
          </form>

          {/* Location Banner */}
          {userState && (
            <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-3 mb-4 rounded shadow-sm">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="font-medium">
                  Showing events in {userState}
                </span>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--primary-color)]"></div>
              <span className="ml-2">Loading events...</span>
            </div>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm">{error}</p>
                  <button
                    onClick={handleRetry}
                    className="mt-2 text-sm underline hover:no-underline"
                  >
                    Try again
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Events Grid */}
          {!loading && !error && events.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-full">
              {events.map((event) => (
                <div
                  key={event.id}
                  onClick={() => currentSetSelectedEvent(event)}
                  className="cursor-pointer bg-white rounded-lg shadow-md overflow-hidden w-full min-w-0"
                >
                  <div className="relative pb-[56.25%] max-w-full">
                    <img
                      src={
                        event.image ||
                        "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/events/default-event.png"
                      }
                      alt={event.event_name}
                      className="absolute inset-0 w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src =
                          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/events/default-event.png";
                      }}
                    />
                  </div>
                  <div className="p-4">
                    <p className="text-sm text-gray-500">
                      {moment(event.start_time).format("MMM Do, h:mm A")}
                    </p>
                    <BakedBotHeading
                      level={3}
                      className="text-lg font-semibold mt-1"
                    >
                      {event.event_name}
                    </BakedBotHeading>
                    <p className="text-gray-600 mt-1 text-sm">
                      {event.address}
                    </p>
                    {event.starting_price && (
                      <p className="text-sm text-green-600 mt-1 font-medium">
                        From {event.starting_price}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* No Events Found */}
          {!loading && !error && events.length === 0 && (
            <div className="text-center py-8">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3a4 4 0 118 0v4M8 7H4a1 1 0 00-1 1v9a1 1 0 001 1h16a1 1 0 001-1V8a1 1 0 00-1-1h-4"
                />
              </svg>
              <BakedBotHeading
                level={3}
                className="mt-2 text-xl font-medium text-gray-900"
              >
                No events found
              </BakedBotHeading>
              <p className="mt-1 text-sm text-gray-500">
                {userState
                  ? `No events found in ${userState}. Try searching for events in nearby areas.`
                  : "No events found. Try adjusting your search criteria."}
              </p>
            </div>
          )}
        </div>
      ) : (
        <EventDetails
          event={currentSelectedEvent}
          setSelectedItem={currentSetSelectedEvent}
        />
      )}
    </>
  );
};

export default EventList;
