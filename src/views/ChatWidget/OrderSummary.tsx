import { useCart } from "./CartContext";
import { useState } from "react";
import useAuth from "../../hooks/useAuth";
import Swal from "sweetalert2";
import {
  FaMinus,
  FaPlus,
  FaRegTrashAlt,
  FaLongArrowAltLeft,
  FaTimes,
} from "react-icons/fa";
import BakedBotHeading from "../../components/BakedBotHeading";

interface CheckoutViewProps {
  navigateTo: (view: any) => void;
  setCurrentView: (view: any) => void;
  contactInfo: {
    email: string;
    phone: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    birth_date?: string;
  };
  setContactInfo: (info: {
    email: string;
    phone: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    birth_date?: string;
  }) => void;
  hasValidEmail: boolean;
}

const validateEmail = (email: string) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
};

const CheckoutView: React.FC<CheckoutViewProps> = ({
  navigateTo,
  setCurrentView,
  contactInfo,
  setContactInfo,
  hasValidEmail,
}) => {
  const { cart, updateQuantity, removeFromCart, handleCheckout } = useCart();
  const { user } = useAuth();

  const [isEditingInfo, setIsEditingInfo] = useState<boolean>(
    user ? false : true
  );
  const [isLoading, setIsLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);

  const isCartEmpty = Object.keys(cart).length === 0;

  // When not editing, fall back to the user's account info if available.
  const displayedEmail = isEditingInfo
    ? contactInfo.email
    : contactInfo.email || user?.email || "";

  // Handle name logic - prioritize firstName/lastName, fall back to name or user displayName
  let firstName = contactInfo.firstName || "";
  let lastName = contactInfo.lastName || "";

  // If no firstName/lastName but we have name or user displayName, split it
  if (!firstName && !lastName) {
    const fallbackName = contactInfo.name || user?.displayName || "";
    const nameParts = fallbackName.split(" ");
    firstName = nameParts[0] || "";
    lastName = nameParts.slice(1).join(" ") || "";
  }

  const isEmailValid = validateEmail(displayedEmail);
  const isFormValid =
    !isCartEmpty &&
    isEmailValid &&
    firstName.trim() !== "" &&
    lastName.trim() !== "" &&
    contactInfo.birth_date;

  // Toggle edit mode
  const handleToggleEdit = () => {
    setIsEditingInfo((prev) => !prev);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;
    setIsLoading(true);

    try {
      const success = await handleCheckout({
        email: displayedEmail,
        phone: contactInfo.phone,
        name: `${firstName} ${lastName}`.trim(),
        firstName: firstName,
        lastName: lastName,
        birth_date: contactInfo.birth_date,
        couponCode: couponCode.trim(),
      });

      if (success) {
        setShowCheckoutModal(false);
        setCurrentView("order-confirm");
      }
    } catch (error) {
      setShowCheckoutModal(false);
      setCurrentView("order-confirm");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-full p-4 overflow-y-scroll bg-gray-50">
      {isCartEmpty ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <p className="text-gray-500 mb-4">Your cart is empty</p>
          <button
            onClick={() => navigateTo("deals")}
            className="text-primary-color hover:underline"
          >
            Continue Shopping
          </button>
        </div>
      ) : (
        <>
          <BakedBotHeading
            level={3}
            className="text-xl font-bold mb-4 text-gray-800"
          >
            Order Summary
          </BakedBotHeading>
          <div className="space-y-2 mb-4">
            {Object.entries(cart).map(
              ([productId, { product, quantity }]: any) => (
                <div
                  key={productId}
                  className="text-sm bg-white p-2 rounded-lg shadow-sm grid grid-cols-[64px_1fr] grid-rows-[auto_1fr] gap-2"
                >
                  {/* Image - spans both rows */}
                  <img
                    src={product.image_url}
                    alt={product.product_name}
                    className="w-16 h-16 object-cover rounded-md row-span-2"
                  />

                  {/* Title - spans full width of second column */}
                  <div className="col-span-1 min-w-0">
                    <span className="text-gray-800 font-semibold truncate block">
                      {product.product_name}{" "}
                      {product.brand_name && (
                        <span className="text-xs text-gray-500">
                          by {product.brand_name}
                        </span>
                      )}
                    </span>
                  </div>

                  {/* Bottom row with two columns */}
                  <div className="grid grid-cols-[1fr_auto] gap-2 items-center">
                    {/* THC/CBD and Price */}
                    <div className="flex flex-col">
                      <p className="font-normal text-xs opacity-40">
                        THC: {product.percentage_thc ?? 0} | CBD:{" "}
                        {product.percentage_cbd ?? 0}
                      </p>
                      <span className="font-semibold">
                        ${(product.latest_price * quantity).toFixed(2)}
                      </span>
                    </div>

                    {/* Controls */}
                    <div className="flex items-center gap-3 flex-shrink-0">
                      {/* Quantity Stepper */}
                      <div className="flex items-center rounded-lg bg-gray-100 h-9">
                        <button
                          onClick={() => updateQuantity(productId, -1)}
                          className="w-9 h-full flex items-center justify-center text-gray-600 hover:bg-gray-200 rounded-l-lg transition-colors"
                        >
                          <FaMinus size={12} />
                        </button>
                        <span className="w-10 text-center font-medium text-gray-800 text-base">
                          {quantity.toString().padStart(2, "0")}
                        </span>
                        <button
                          onClick={() => updateQuantity(productId, 1)}
                          className="w-9 h-full flex items-center justify-center text-gray-600 hover:bg-gray-200 rounded-r-lg transition-colors"
                        >
                          <FaPlus size={12} />
                        </button>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={() => removeFromCart(productId)}
                        className="w-9 h-9 flex items-center justify-center bg-gray-200 text-gray-600 rounded-lg hover:bg-red-500 hover:text-white transition-colors"
                        aria-label="Remove item"
                      >
                        <FaRegTrashAlt size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              )
            )}
          </div>
          <div className="flex justify-between font-bold text-lg mb-6 text-gray-800">
            <span>Total:</span>
            <span>
              $
              {Object.values(cart)
                .reduce(
                  (sum, { product, quantity }) =>
                    sum + product.latest_price * quantity,
                  0
                )
                .toFixed(2)}
            </span>
          </div>

          {/* Coupon Code */}
          <div className="flex items-center border border-gray-200 rounded-lg overflow-hidden mb-6">
            <input
              type="text"
              style={{ border: "none", outline: "none" }}
              placeholder="Coupon Code"
              value={couponCode}
              onChange={(e) => setCouponCode(e.target.value)}
              className="flex-1 p-3 placeholder:text-sm border-none focus:outline-none placeholder-gray-400"
            />
            <button
              className="bb-sm-redeem-button rounded-r-lg ml-auto px-4 py-3 text-sm font-semibold"
              onClick={(e) => {
                e.preventDefault();
                if (couponCode.trim()) {
                  // You could add validation or apply discount logic here
                }
              }}
            >
              Redeem
            </button>
          </div>

          {/* Modern Contact Information Section */}
          <div className="mt-6 p-5 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between mb-3">
              <BakedBotHeading
                level={4}
                className="text-lg font-semibold text-gray-800"
              >
                Your Information
              </BakedBotHeading>
              <button
                onClick={handleToggleEdit}
                className="text-sm text-primary-color hover:underline"
              >
                {isEditingInfo ? "Done" : "Edit"}
              </button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* First Name Field */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-600 mb-1">
                  First Name
                </label>
                {isEditingInfo ? (
                  <input
                    type="text"
                    placeholder="Enter your first name"
                    value={firstName}
                    onChange={(e) =>
                      setContactInfo({
                        ...contactInfo,
                        firstName: e.target.value,
                      })
                    }
                    className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                  />
                ) : (
                  <div className="p-2 border rounded bg-white text-sm">
                    {firstName}
                  </div>
                )}
              </div>
              {/* Last Name Field */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-600 mb-1">
                  Last Name
                </label>
                {isEditingInfo ? (
                  <input
                    type="text"
                    placeholder="Enter your last name"
                    value={lastName}
                    onChange={(e) =>
                      setContactInfo({
                        ...contactInfo,
                        lastName: e.target.value,
                      })
                    }
                    className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                  />
                ) : (
                  <div className="p-2 border rounded bg-white text-sm">
                    {lastName}
                  </div>
                )}
              </div>
              {/* Email Field */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-600 mb-1">
                  Email
                </label>
                {isEditingInfo ? (
                  <input
                    type="email"
                    placeholder="Enter your email"
                    value={contactInfo.email}
                    onChange={(e) =>
                      setContactInfo({
                        ...contactInfo,
                        email: e.target.value,
                      })
                    }
                    className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                  />
                ) : (
                  <div className="p-2 border rounded bg-white text-sm">
                    {displayedEmail}
                  </div>
                )}
              </div>
              {/* Phone Field */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-600 mb-1">
                  Phone (Optional)
                </label>
                <input
                  type="tel"
                  placeholder="Phone number"
                  value={contactInfo.phone}
                  onChange={(e) =>
                    setContactInfo({ ...contactInfo, phone: e.target.value })
                  }
                  className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                />
              </div>
              {/* DOB Field */}
              <div className="flex flex-col">
                <label className="text-sm font-medium text-gray-600 mb-1">
                  Date of Birth
                </label>
                <input
                  type="date"
                  placeholder="YYYY-MM-DD"
                  value={contactInfo.birth_date || ""}
                  onChange={(e) =>
                    setContactInfo({
                      ...contactInfo,
                      birth_date: e.target.value,
                    })
                  }
                  className="w-full p-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                />
              </div>
            </div>
          </div>

          {!isEmailValid && (
            <p className="text-red-500 text-sm mt-2">
              Please provide a valid email address to continue
            </p>
          )}

          {/* Place Order Button */}
          <button
            type="button"
            disabled={isCartEmpty}
            onClick={() => setShowCheckoutModal(true)}
            className="flex-1 bb-sm-place-order-button w-full py-3 rounded-lg text-lg font-semibold flex justify-center items-center disabled:opacity-50 mt-8 transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-color"
          >
            Checkout
          </button>
        </>
      )}

      {/* Checkout Modal */}
      {showCheckoutModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setShowCheckoutModal(false)}
          />
          <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <BakedBotHeading level={2} className="text-xl font-bold">
                Checkout
              </BakedBotHeading>
              <button
                onClick={() => setShowCheckoutModal(false)}
                className="p-2 hover:bg-gray-100 rounded-full"
                aria-label="Close checkout modal"
              >
                <FaTimes />
              </button>
            </div>

            <div className="flex flex-col lg:flex-row max-h-[calc(90vh-80px)]">
              {/* Order Summary - Left Side */}
              <div className="lg:w-1/2 p-4 border-r">
                <BakedBotHeading
                  level={3}
                  className="text-lg font-semibold mb-4"
                >
                  Order Summary
                </BakedBotHeading>
                <div className="max-h-96 overflow-y-auto space-y-2">
                  {Object.entries(cart).map(
                    ([productId, { product, quantity }]: any) => (
                      <div
                        key={productId}
                        className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                      >
                        <img
                          src={product.image_url}
                          alt={product.product_name}
                          className="w-12 h-12 object-cover rounded"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {product.product_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            ${product.latest_price} × {quantity}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center bg-white rounded border">
                            <button
                              onClick={() => updateQuantity(productId, -1)}
                              className="p-1 hover:bg-gray-100"
                            >
                              <FaMinus size={10} />
                            </button>
                            <span className="px-2 text-sm">{quantity}</span>
                            <button
                              onClick={() => updateQuantity(productId, 1)}
                              className="p-1 hover:bg-gray-100"
                            >
                              <FaPlus size={10} />
                            </button>
                          </div>
                          <span className="font-semibold text-sm">
                            ${(product.latest_price * quantity).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    )
                  )}
                </div>

                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Total:</span>
                    <span>
                      $
                      {Object.values(cart)
                        .reduce(
                          (sum, { product, quantity }) =>
                            sum + product.latest_price * quantity,
                          0
                        )
                        .toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Customer Information - Right Side */}
              <div className="lg:w-1/2 p-4">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <BakedBotHeading
                    level={3}
                    className="text-lg font-semibold mb-4"
                  >
                    Customer Information
                  </BakedBotHeading>

                  <div className="grid grid-cols-2 gap-3">
                    <input
                      type="text"
                      placeholder="First Name"
                      value={firstName}
                      onChange={(e) =>
                        setContactInfo({
                          ...contactInfo,
                          firstName: e.target.value,
                        })
                      }
                      className="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                      required
                    />
                    <input
                      type="text"
                      placeholder="Last Name"
                      value={lastName}
                      onChange={(e) =>
                        setContactInfo({
                          ...contactInfo,
                          lastName: e.target.value,
                        })
                      }
                      className="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                      required
                    />
                  </div>

                  <input
                    type="email"
                    placeholder="Email"
                    value={contactInfo.email}
                    onChange={(e) =>
                      setContactInfo({
                        ...contactInfo,
                        email: e.target.value,
                      })
                    }
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                    required
                  />

                  <input
                    type="tel"
                    placeholder="Phone (Optional)"
                    value={contactInfo.phone}
                    onChange={(e) =>
                      setContactInfo({ ...contactInfo, phone: e.target.value })
                    }
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                  />

                  <div>
                    <label className="block text-sm font-medium text-gray-600 mb-1">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      value={contactInfo.birth_date || ""}
                      onChange={(e) =>
                        setContactInfo({
                          ...contactInfo,
                          birth_date: e.target.value,
                        })
                      }
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                      required
                    />
                  </div>

                  <input
                    type="text"
                    placeholder="Coupon Code (optional)"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                  />

                  {!isEmailValid && (
                    <p className="text-red-500 text-sm">
                      Please provide a valid email address to continue
                    </p>
                  )}

                  <button
                    type="submit"
                    disabled={isLoading || !isFormValid}
                    className="w-full bg-primary-color text-white py-3 px-4 rounded-md text-lg font-semibold hover:bg-primary-color/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isLoading
                      ? "Placing Order..."
                      : `Place Order - $${Object.values(cart)
                          .reduce(
                            (sum, { product, quantity }) =>
                              sum + product.latest_price * quantity,
                            0
                          )
                          .toFixed(2)}`}
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CheckoutView;
