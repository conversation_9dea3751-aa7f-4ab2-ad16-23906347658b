import {
  FaLongArrowAltRight,
  FaMinus,
  FaPlus,
  FaRegTrashAlt,
} from "react-icons/fa";
import { useCart } from "../CartContext";
import BakedBotHeading from "../../../components/BakedBotHeading";

const CartView = ({ navigateTo }) => {
  const { cart, updateQuantity, removeFromCart } = useCart();
  return (
    <div className="bb-sm-cart-view flex flex-col justify-between overflow-y-auto h-full">
      {Object.keys(cart).length === 0 ? (
        <p className="px-4">Your cart is empty.</p>
      ) : (
        <>
          <div className="px-4 pb-4">
            {Object.entries(cart).map(([productId, { product, quantity }]) => (
              <div
                key={productId}
                className="flex items-center justify-between bg-white py-3 rounded-lg shadow-md"
              >
                {/* Product Image */}
                <img
                  src={product.image_url}
                  alt={product.product_name}
                  className="w-16 h-16 object-cover rounded-lg"
                />

                {/* Product Details */}
                <div className="flex flex-col flex-grow pl-2">
                  <BakedBotHeading level={3} className="font-medium text-xl">
                    {product.product_name}
                  </BakedBotHeading>
                  <p className="font-normal text-sm py-1 opacity-40">
                    THC: {product.percentage_thc ?? 0} | CBD:{" "}
                    {product.percentage_cbd ?? 0}
                  </p>
                  <span className="text-xl font-medium">
                    ${(product.latest_price * quantity).toFixed(2)}
                  </span>
                </div>

                {/* Quantity Controls */}
                <div className="border rounded-lg opacity-60 border-opacity-100 flex items-center space-x-2 mr-4">
                  <button
                    onClick={() => updateQuantity(productId, -1)}
                    className="p-2 w-8 h-8 flex items-center justify-center text-gray-700 hover:bg-gray-200"
                  >
                    <FaMinus size={12} />
                  </button>
                  <span className="text-lg font-medium">
                    {quantity.toString().padStart(2, "0")}
                  </span>
                  <button
                    onClick={() => updateQuantity(productId, 1)}
                    className="p-2 w-8 h-8 flex items-center justify-center text-gray-700 hover:bg-gray-200"
                  >
                    <FaPlus size={12} />
                  </button>
                </div>

                {/* Remove Button */}
                <button
                  onClick={() => removeFromCart(productId)}
                  className="bg-red-100 p-2 rounded-lg text-red-500 hover:bg-red-200 transition"
                  aria-label="Remove item"
                >
                  <FaRegTrashAlt size={16} />
                </button>
              </div>
            ))}
          </div>
          <div className="bb-sm-cart-summary py-4 border-t border-gray-7000">
            <div className="font-normal flex justify-between mb-6 text-lg">
              <span>Subtotal</span>
              <span>
                $
                {Object.values(cart)
                  .reduce(
                    (sum, { product, quantity }) =>
                      sum + product.latest_price * quantity,
                    0
                  )
                  .toFixed(2)}
              </span>
            </div>
            {/* <div className="flex justify-between mb-2 text-gray-400">
                            <span>Discount</span>
                            <span>-$6.89</span>
                        </div> */}
            <div className="flex justify-between font-semibold text-lg mb-4 text-lg">
              <span>Total</span>
              <span>
                $
                {Object.values(cart)
                  .reduce(
                    (sum, { product, quantity }) =>
                      sum + product.latest_price * quantity,
                    0
                  )
                  .toFixed(2)}
              </span>
            </div>
            <button
              onClick={() => navigateTo("checkOut")}
              className="bb-sm-checkout-button w-full py-4 flex items-center justify-center space-x-2 rounded-lg text-lg font-semibold"
            >
              <span>Checkout</span>
              <FaLongArrowAltRight />
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default CartView;
