import { memo, useState } from "react";
import { FaLongArrowAltRight } from "react-icons/fa";
import BakedBotHeading from "../../../components/BakedBotHeading";

type Windows =
  | "chat"
  | "product"
  | "settings"
  | "cart"
  | "checkOut"
  | "feel"
  | "deals"
  | "order-confirm"
  | "events"
  | "product-type";

interface FeelingScreenProps {
  selectedProductType: any | null;
  setCurrentView: React.Dispatch<React.SetStateAction<Windows>>;
  setPrompts: React.Dispatch<React.SetStateAction<string>>;
  setShouldPlay: React.Dispatch<React.SetStateAction<boolean>>;
}

const FeelingsScreen: React.FC<FeelingScreenProps> = memo(
  ({ selectedProductType, setCurrentView, setPrompts, setShouldPlay }) => {
    const feelings = [
      {
        name: "Creative",
        description: "Induce happiness",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/creative.png",
      },
      {
        name: "Energized",
        description: "Boost imagination",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/energized.png",
      },
      {
        name: "Focused",
        description: "Increase vitality",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/focused.png",
      },
      {
        name: "Euphoric",
        description: "Enhance Concentration",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/euorphic.png",
      },
      {
        name: "Giggly",
        description: "Elevate mood",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/gigly.png",
      },
      {
        name: "Relaxed",
        description: "Induce happiness",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/relaxed.png",
      },
      {
        name: "Tingly",
        description: "Aid relaxation",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/tingly.png",
      },
      {
        name: "Stimulated",
        description: "Aid relaxation",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/stimulated.png",
      },
    ];
    const [selectedFeelings, setSelectedFeelings] = useState<string[]>([]);

    const handleSelect = (feel) => {
      if (selectedFeelings.includes(feel)) {
        if (selectedFeelings.length === 1) return;
        setSelectedFeelings(selectedFeelings.filter((f) => f !== feel));
      } else {
        if (selectedFeelings.length >= 2) return;
        setSelectedFeelings([...selectedFeelings, feel]);
      }
    };

    const handleClick = () => {
      setCurrentView("chat");
      setPrompts(
        `Show me the ${
          selectedProductType.name
        } that makes me feel ${selectedFeelings.join(" and ")}`
      );
      setShouldPlay(true);
    };
    return (
      <div className="flex flex-col justify-between h-full overflow-y-scroll mt-3">
        <BakedBotHeading
          level={3}
          className="py-1 text-[17px] font-medium text-center"
        >
          How do you want to feel?
        </BakedBotHeading>
        <p className="pt-1 pb-2 text-center">Select up to two effects</p>

        <div className="flex-1 overflow-y-auto py-2">
          {/* Pills Category-Style Navigation */}
          <div className="relative mb-2">
            {/* Curved line */}
            <div
              className="absolute w-full"
              style={{
                height: "10px",
                borderTop: "1px solid #e9a8a8",
                borderRadius: "50%/6px 6px 0 0",
                top: "8px",
                zIndex: 0,
              }}
            ></div>

            <div className="pt-5 pb-1">
              <div className="flex flex-wrap justify-center gap-1 px-3">
                {feelings?.map((feel, index) => (
                  <button
                    key={index}
                    className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center ${
                      selectedFeelings.includes(feel.name)
                        ? "bg-[#65715F] text-white"
                        : "border border-gray-300 text-gray-700 hover:bg-[#65715F]/10 hover:border-[#65715F]/50"
                    }`}
                    onClick={() => handleSelect(feel.name)}
                  >
                    <div
                      className={`h-5 w-5 ${
                        selectedFeelings.includes(feel.name)
                          ? "bg-white/20"
                          : "bg-[#65715F]/10"
                      } rounded-full flex items-center justify-center mr-1.5`}
                    >
                      <img
                        src={feel.image}
                        alt={feel.name}
                        className="h-[14px] w-[14px] object-contain"
                      />
                    </div>
                    {feel.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="px-4 pb-4">
          <button
            onClick={handleClick}
            disabled={selectedFeelings.length === 0}
            className={`w-full py-2 rounded-lg text-white transition-opacity ${
              selectedFeelings.length === 0
                ? "bg-gray-400 opacity-50 cursor-not-allowed"
                : "bg-[#65715F] opacity-100 cursor-pointer"
            }`}
          >
            Continue
          </button>
        </div>
      </div>
    );
  }
);

export default FeelingsScreen;
