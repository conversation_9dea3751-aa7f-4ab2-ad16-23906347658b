import { ArrowLeft } from "iconsax-react";
import React, { useState } from "react";
import BakedBotHeading from "../../../components/BakedBotHeading";

const OrderConfirmation: React.FC<any> = ({ setCurrentView }) => {
  const [confirmed, setConfirmed] = useState(false);

  return (
    <div className="flex flex-col items-center justify-center h-screen p-6">
      {!confirmed ? (
        <div className="text-center max-w-md w-full">
          <img
            src="https://bakedbot-3e8a803d7a8a.herokuapp.com/images/pointing.png"
            alt="Order Confirmation"
            className="mx-auto h-[220px]"
          />
          <BakedBotHeading level={2} className="text-2xl font-medium">
            Order Confirmation
          </BakedBotHeading>
          <p className="text-gray-600 mt-2">
            Your order will be ready for pick up in 15 minutes. Use code{" "}
            <span className="font-bold">"UltraHigh"</span> to save on your next
            order.
          </p>
          <div className="flex gap-4 mt-6">
            <button
              className="w-full p-2 border-2 border-[var(--primary-color)] text-[var(--primary-color)] font-semibold rounded-lg"
              onClick={() => setCurrentView("checkOut")}
            >
              Cancel
            </button>
            <button
              className="w-full p-2 bg-[var(--primary-color)] text-white rounded-lg font-semibold"
              onClick={() => setConfirmed(true)}
            >
              Confirm
            </button>
          </div>
        </div>
      ) : (
        <div className="text-center max-w-md w-full">
          <img
            src="https://bakedbot-3e8a803d7a8a.herokuapp.com/images/tech_smokey.png"
            alt="Thank You"
            className="h-[220px] mx-auto mb-4"
          />
          <BakedBotHeading level={2} className="text-2xl font-semibold">
            Thank you! For The Order
          </BakedBotHeading>

          <button
            className="flex-1 bb-sm-place-order-button w-full py-3 rounded-lg text-lg font-semibold flex justify-center items-center disabled:opacity-50 mt-8"
            onClick={() => setCurrentView("deals")}
          >
            <ArrowLeft size={16} /> Back to Shop
          </button>
        </div>
      )}
    </div>
  );
};

export default OrderConfirmation;
