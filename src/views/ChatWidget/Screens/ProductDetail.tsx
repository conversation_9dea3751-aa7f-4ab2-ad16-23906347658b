import React, { useContext } from "react";
import { Product } from "../../../utils/api";
import { FaMinus, FaPlus, FaArrowLeft } from "react-icons/fa";
import { useCart } from "../CartContext";
import BakedBotHeading from "../../../components/BakedBotHeading";

interface ProductDetailProps {
  product?: Product | null;
  navigateTo: (path: any) => void;
}

const ProductDetailView: React.FC<ProductDetailProps> = ({
  product,
  navigateTo,
}) => {
  const { cart, addToCart, updateQuantity } = useCart();
  if (!product) {
    return null;
  }
  return (
    <div className="flex flex-col h-full">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto p-4 pb-24">
        {/* Product Header - Image and Basic Info */}
        <div className="flex flex-col mb-4">
          <div className="flex justify-center items-center mb-4">
            <div className="relative w-full h-60 rounded-lg overflow-hidden shadow-md">
              <img
                className="w-full h-full object-contain"
                src={product.image_url}
                alt={product.product_name}
              />
              {product.recreational && (
                <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                  Recreational
                </div>
              )}
              {product.medical && (
                <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                  Medical
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col mb-3">
            {product.brand_name && (
              <span className="text-sm text-gray-500 mb-1">
                {product.brand_name}
              </span>
            )}
            <div className="flex justify-between items-start">
              <BakedBotHeading level={1} className="text-xl font-bold flex-1">
                {product.raw_product_name}
              </BakedBotHeading>
              <div className="text-xl font-bold">
                ${product.latest_price?.toFixed(2)}
              </div>
            </div>
            <div className="flex items-center mt-1">
              <span className="text-sm text-gray-500">{product.category}</span>
              {product.display_weight && (
                <span className="text-sm bg-gray-200 rounded-full px-2 py-0.5 ml-2">
                  {product.display_weight}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-3 gap-2 mb-4">
          {product.percentage_thc !== null && (
            <div className="bg-gray-100 rounded-lg p-3 text-center">
              <div className="text-lg font-bold">{product.percentage_thc}%</div>
              <div className="text-xs text-gray-500">THC</div>
            </div>
          )}
          {product.percentage_cbd !== null && (
            <div className="bg-gray-100 rounded-lg p-3 text-center">
              <div className="text-lg font-bold">{product.percentage_cbd}%</div>
              <div className="text-xs text-gray-500">CBD</div>
            </div>
          )}
          {product.quantity_per_package !== null && (
            <div className="bg-gray-100 rounded-lg p-3 text-center">
              <div className="text-lg font-bold">
                {product.quantity_per_package}
              </div>
              <div className="text-xs text-gray-500">Per Package</div>
            </div>
          )}
        </div>

        {/* Product Description */}
        {product.description && (
          <div className="mb-4">
            <BakedBotHeading level={2} className="text-lg font-semibold mb-2">
              Description
            </BakedBotHeading>
            <p className="text-sm text-gray-700">{product.description}</p>
          </div>
        )}

        {/* Effects/Tags */}
        {product.product_tags && product.product_tags.length > 0 && (
          <div className="mb-4">
            <BakedBotHeading level={2} className="text-lg font-semibold mb-2">
              Effects
            </BakedBotHeading>
            <div className="flex flex-wrap gap-2">
              {product.product_tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-gray-200 text-gray-800 px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Additional Details */}
        <div className="mb-4">
          <BakedBotHeading level={2} className="text-lg font-semibold mb-2">
            Details
          </BakedBotHeading>
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="grid grid-cols-2 gap-y-2 text-sm">
              {product.subcategory && (
                <>
                  <div className="text-gray-500">Subcategory</div>
                  <div>{product.subcategory}</div>
                </>
              )}
              {product.mg_thc !== null && (
                <>
                  <div className="text-gray-500">THC Content</div>
                  <div>{product.mg_thc}mg</div>
                </>
              )}
              {product.mg_cbd !== null && (
                <>
                  <div className="text-gray-500">CBD Content</div>
                  <div>{product.mg_cbd}mg</div>
                </>
              )}
              {product.menu_provider && (
                <>
                  <div className="text-gray-500">Provider</div>
                  <div>{product.menu_provider}</div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add to Cart Section - Fixed at bottom but within the component */}
      <div className="bg-white p-4 shadow-md border-t">
        {cart[product.product_id ?? product.id] ? (
          <div className="flex items-center justify-between">
            <div className="text-lg font-bold">
              Total: $
              {(
                product.latest_price *
                cart[product.product_id ?? product.id].quantity
              ).toFixed(2)}
            </div>
            <div className="flex items-center border rounded-lg overflow-hidden bg-gray-50">
              <button
                onClick={() =>
                  updateQuantity(product.product_id ?? product.id, -1)
                }
                className="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-gray-200 transition-colors"
                disabled={cart[product.product_id ?? product.id].quantity <= 1}
              >
                <FaMinus size={12} />
              </button>
              <span className="w-10 text-center font-medium">
                {cart[product.product_id ?? product.id].quantity}
              </span>
              <button
                onClick={() =>
                  updateQuantity(product.product_id ?? product.id, 1)
                }
                className="w-10 h-10 flex items-center justify-center bg-gray-100 hover:bg-gray-200 transition-colors"
              >
                <FaPlus size={12} />
              </button>
            </div>
          </div>
        ) : (
          <button
            className="w-full py-3 bg-[var(--primary-color)] hover:opacity-90 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
            onClick={() => addToCart(product)}
          >
            Add To Cart
          </button>
        )}
      </div>
    </div>
  );
};

export default ProductDetailView;
