import { memo } from "react";
import BakedBotHeading from "../../../components/BakedBotHeading";

const ProductType: React.FC<any> = memo(
  ({ setSelectedProductType, navigateTo }) => {
    const productTypes = [
      {
        name: "Flower",
        description: "Traditional cannabis buds",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/productType1.png",
      },
      {
        name: "Pre-Roll",
        description: "Ready-to-smoke joints",
        image:
          "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/productType2.png",
      },
      {
        name: "Vape",
        description: "Vaporizer cartridges",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Vape.png",
      },
      {
        name: "Edible",
        description: "Cannabis-infused foods",
        image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/Edible.png",
      },
      // {
      //   name: "Concentrate",
      //   description: "Potent extracts",
      //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/concentrate.png",
      // },
      // {
      //   name: "Tincture",
      //   description: "Liquid extracts",
      //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/tincture.png",
      // },
      // {
      //   name: "Topical",
      //   description: "Skin applications",
      //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/topical.png",
      // },
      // {
      //   name: "Accessories",
      //   description: "Smoking devices",
      //   image: "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/accessories.png",
      // },
    ];

    return (
      <div className="flex flex-col justify-between overflow-y-scroll mt-3">
        <BakedBotHeading
          level={3}
          className="py-1 text-[17px] font-medium text-center"
        >
          What type of product are you looking for?
        </BakedBotHeading>
        <p className="pt-1 pb-2 text-center">Select a product category</p>

        <div className="flex-1 overflow-y-auto py-2">
          {/* Pills Category-Style Navigation */}
          <div className="relative mb-2">
            {/* Curved line */}
            <div
              className="absolute w-full"
              style={{
                height: "10px",
                borderTop: "1px solid #e9a8a8",
                borderRadius: "50%/6px 6px 0 0",
                top: "8px",
                zIndex: 0,
              }}
            ></div>

            <div className="pt-5 pb-1">
              <div className="flex flex-wrap justify-center gap-1 px-3">
                {productTypes.map((type, index) => (
                  <button
                    key={index}
                    className="px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center border border-gray-300 text-gray-700 hover:bg-[#65715F]/10 hover:border-[#65715F]/50"
                    onClick={() => {
                      setSelectedProductType(type);
                      navigateTo("feel");
                    }}
                  >
                    <div className="h-5 w-5 bg-[#65715F]/10 rounded-full flex items-center justify-center mr-1.5">
                      <img
                        src={type.image}
                        alt={type.name}
                        className="h-[14px] w-[14px] object-contain"
                      />
                    </div>
                    {type.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default ProductType;
