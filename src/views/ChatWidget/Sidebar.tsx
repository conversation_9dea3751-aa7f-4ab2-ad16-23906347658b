import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaShoppingBag,
  FaCalendarAlt,
  FaBox,
  FaCog,
  FaTimes,
} from "react-icons/fa";
import { useCart } from "./CartContext";
import { Orders } from "../../components/Orders/Orders";
import ProfileCard from "../../components/Profile/ProfileCard";
import ProfileSettings from "../../components/Profile/ProfileSettings";
import BakedBotHeading from "../../components/BakedBotHeading";
import { useAuth } from "../../contexts/AuthContext";

// Use variables for the remote image URLs rather than imports
const robotIcon =
  "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/pointing.png";
const notLoggedInIcon =
  "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/blunt-smokey-sm.png";

// Define the props for the Sidebar component
interface SidebarProps {
  isMenuOpen: boolean;
  isLoggedIn: boolean;
  chats: Array<{ chat_id: string; name: string }>;
  activeChatId: string | null;
  onLoadChatHistory: (chatId: string | null) => void;
  onLogin: () => void;
  onViewSettings: () => void;
  onViewStore: () => void;
  onViewEvents: () => void;
  onViewOrders: () => void;
  onViewProfile: () => void;
  onRenameChat: (chatId: string, newName: string) => void;
  onDeleteChat: (chatId: string) => void;
  setIsMenuOpen: React.Dispatch<React.SetStateAction<any>>;
}

const Sidebar: React.FC<SidebarProps> = ({
  isMenuOpen,
  isLoggedIn,
  chats,
  activeChatId,
  onLoadChatHistory,
  onLogin,
  onViewSettings,
  onViewStore,
  onViewEvents,
  onViewOrders,
  onViewProfile,
  onRenameChat,
  onDeleteChat,
  setIsMenuOpen,
}) => {
  const [editingChatId, setEditingChatId] = React.useState<string | null>(null);
  const [newChatName, setNewChatName] = React.useState("");
  const renameInputRef = React.useRef<HTMLInputElement>(null);
  const renameContainerRef = React.useRef<HTMLDivElement>(null);
  const [contextMenu, setContextMenu] = React.useState<{
    x: number;
    y: number;
    chatId: string;
  } | null>(null);

  // Get user info from FederatedAuth context
  const { user } = useAuth();

  // Focus the input when a chat is being renamed
  React.useEffect(() => {
    if (editingChatId && renameInputRef.current) {
      renameInputRef.current.focus();
    }
  }, [editingChatId]);

  // Add click outside handling for the rename container
  React.useEffect(() => {
    if (!editingChatId) return;
    const handleClickOutside = (e: MouseEvent) => {
      if (
        renameContainerRef.current &&
        !renameContainerRef.current.contains(e.target as Node)
      ) {
        setEditingChatId(null);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editingChatId]);

  // Handle saving the renamed chat
  const handleSaveRename = () => {
    if (editingChatId && newChatName.trim()) {
      onRenameChat(editingChatId, newChatName.trim());
      setEditingChatId(null);
      setNewChatName("");
    }
  };

  // Handle right-click on chat history item
  const handleContextMenu = (e: React.MouseEvent, chatId: string) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent event bubbling
    setContextMenu({
      x: e.clientX,
      y: e.clientY,
      chatId,
    });
  };

  // Close context menu when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // Always close the context menu on any click outside it
      const contextMenuElement = document.querySelector(".bb-sm-context-menu");
      if (
        !contextMenuElement ||
        !contextMenuElement.contains(e.target as Node)
      ) {
        setContextMenu(null);
      }
    };

    if (contextMenu) {
      // Add the event listener with a small delay
      const timer = setTimeout(() => {
        document.addEventListener("mousedown", handleClickOutside);
      }, 100);

      return () => {
        clearTimeout(timer);
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }

    return undefined;
  }, [contextMenu]);

  return (
    <div className={`bb-sm-side-menu ${isMenuOpen ? "bb-sm-open" : ""}`}>
      <div className="bb-sm-side-menu-header">
        <button
          className="ml-auto font-semibold"
          onClick={() => setIsMenuOpen(false)}
        >
          X
        </button>
        <div className="bb-sm-robot-icon-container">
          <img
            src={notLoggedInIcon}
            alt="Smokey the Budtender"
            className="bb-sm-robot-icon"
          />
        </div>

        <div className="flex flex-col items-center mb-4">
          <div
            className="bakedbot-heading text-xl font-medium mb-2 text-center"
            style={{ fontWeight: 500, fontSize: "1.25rem" }}
          >
            Smokey
          </div>
          <p className="text-sm text-gray-600 text-center">
            Your personal budtender assistant
          </p>
        </div>

        <button
          className="bb-sm-new-chat-button text-white bg-primary-color py-2 px-4 rounded-md transition-colors w-full flex items-center justify-center"
          onClick={() => {
            onLoadChatHistory(null);
            setIsMenuOpen(false);
          }}
        >
          New Chat
        </button>
      </div>

      <div className="bb-sm-side-menu-content">
        {isLoggedIn ? (
          <>
            <div
              className="bakedbot-heading text-lg font-medium mb-3 mt-3"
              style={{ fontWeight: 500, fontSize: "1.125rem" }}
            >
              Chat History
            </div>
            <div className="bb-sm-chat-history-scroll">
              {chats.length > 0 ? (
                chats.map((chat) => (
                  <div key={chat.chat_id} className="bb-sm-chat-item-container">
                    {editingChatId === chat.chat_id ? (
                      <div
                        className="bb-sm-chat-rename-input flex items-center"
                        ref={renameContainerRef}
                      >
                        <input
                          ref={renameInputRef}
                          type="text"
                          className="text-sm h-10 flex-1"
                          value={newChatName}
                          onChange={(e) => setNewChatName(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") handleSaveRename();
                            if (e.key === "Escape") setEditingChatId(null);
                          }}
                        />
                        <button
                          onClick={handleSaveRename}
                          className="ml-2 text-primary-color"
                        >
                          <FaCheck size={16} />
                        </button>
                      </div>
                    ) : (
                      <div className="bb-sm-chat-item-wrapper">
                        <button
                          onClick={() => onLoadChatHistory(chat.chat_id)}
                          className={`bb-sm-menu-item text-md ${
                            activeChatId === chat.chat_id ? "bb-sm-active" : ""
                          }`}
                        >
                          {chat.name}
                        </button>
                        <button
                          className="bb-sm-chat-options-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleContextMenu(e, chat.chat_id);
                          }}
                          aria-label="Chat options"
                        >
                          <FaEllipsisV className="text-secondary-color" />
                        </button>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-center text-gray-500 py-4">
                  No conversations yet.
                </p>
              )}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center w-full">
            <div className="bg-gray-50 p-4 rounded-lg mb-6 w-full">
              <p className="text-sm text-gray-700 mb-2">
                <strong>Start chatting now!</strong> No login required.
              </p>
              <p className="text-xs text-gray-600">
                Sign in to save your chat history and access it later.
              </p>
            </div>
          </div>
        )}
      </div>

      {contextMenu && (
        <div
          className="bb-sm-context-menu"
          style={{
            top: contextMenu.y,
            left: contextMenu.x,
            position: "fixed",
            zIndex: 1500,
          }}
          onClick={(e) => {
            // Prevent the click from propagating to the document
            e.stopPropagation();
          }}
        >
          <button
            onClick={(e) => {
              e.stopPropagation();
              console.log(
                "Rename button clicked for chat ID:",
                contextMenu.chatId
              );
              // Set rename mode and populate with current name.
              const chat = chats.find((c) => c.chat_id === contextMenu.chatId);
              if (chat) {
                setNewChatName(chat.name);
                setEditingChatId(chat.chat_id);
              } else {
                console.error("Chat not found for ID:", contextMenu.chatId);
              }
              setContextMenu(null);
            }}
          >
            Rename
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              console.log(
                "Delete button clicked for chat ID:",
                contextMenu.chatId
              );
              onDeleteChat(contextMenu.chatId);
              setContextMenu(null);
            }}
          >
            Delete
          </button>
        </div>
      )}

      <div className="bb-sm-side-menu-footer mt-auto p-4 border-t">
        {isLoggedIn ? (
          <div className="flex flex-col gap-3">
            {/* Navigation Buttons */}
            <div className="flex flex-row gap-2 justify-center mb-3">
              <button
                className="bb-sm-nav-button flex-1 py-2 px-3 bg-primary-color text-white rounded-md flex items-center justify-center gap-2"
                onClick={onViewStore}
              >
                <FaShoppingBag size={16} />
                <span>Shop</span>
              </button>
              <button
                className="bb-sm-nav-button flex-1 py-2 px-3 bg-primary-color text-white rounded-md flex items-center justify-center gap-2"
                onClick={onViewEvents}
              >
                <FaCalendarAlt size={16} />
                <span>Events</span>
              </button>
            </div>

            {/* User Profile Section */}
            <div className="bb-sm-user-profile p-2 bg-gray-50 rounded-lg">
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                  <FaUser className="text-gray-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <BakedBotHeading
                    level={3}
                    className="text-sm font-semibold truncate"
                  >
                    {user?.displayName || "Welcome back!"}
                  </BakedBotHeading>
                  {user?.email && (
                    <p className="text-xs text-gray-500 truncate">
                      {user.email}
                    </p>
                  )}
                </div>
              </div>

              {/* User Menu Buttons */}
              <div className="flex flex-col gap-2">
                <button
                  className="flex items-center gap-2 w-full p-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  onClick={onViewProfile}
                >
                  <FaUser size={14} />
                  My Profile
                </button>
                <button
                  className="flex items-center gap-2 w-full p-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  onClick={onViewOrders}
                >
                  <FaBox size={14} />
                  My Orders
                </button>
                <button
                  className="flex items-center gap-2 w-full p-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  onClick={onViewSettings}
                >
                  <FaCog size={14} />
                  Settings
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-3">
            {/* Navigation Buttons for logged out users */}
            <div className="flex flex-row gap-2 justify-center mb-3">
              <button
                className="bb-sm-nav-button flex-1 py-2 px-3 bg-primary-color text-white rounded-md flex items-center justify-center gap-2"
                onClick={onViewStore}
              >
                <FaShoppingBag size={16} />
                <span>Shop</span>
              </button>
              <button
                className="bb-sm-nav-button flex-1 py-2 px-3 bg-primary-color text-white rounded-md flex items-center justify-center gap-2"
                onClick={onViewEvents}
              >
                <FaCalendarAlt size={16} />
                <span>Events</span>
              </button>
            </div>

            <p className="text-center text-xs text-gray-500">
              Powered by BakedBot AI
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
