// This file is deprecated. All API functions have been moved to src/utils/api.ts for centralization.
//
// Functions moved:
// - renameChat -> src/utils/api.ts (updated to use BakedBot API with PUT /chats/{id})
// - saveThemeSettings -> src/utils/api.ts
// - getThemeSettings -> src/utils/api.ts
// - Product and ProductResponse types -> src/utils/api.ts
// - fetchProducts -> src/utils/api.ts (uses BakedBot API /public/products)
// - searchProducts -> src/utils/api.ts (uses BakedBot API /public/products/search)
// - checkout -> src/utils/api.ts (uses BakedBot API /orders/checkout)
//
// Please import these functions from src/utils/api.ts instead.
//
// The new API structure uses:
// - BakedBot API key authentication from window.BakedBotConfig.apiKey
// - Consistent error handling and logging
// - Proper TypeScript typing
