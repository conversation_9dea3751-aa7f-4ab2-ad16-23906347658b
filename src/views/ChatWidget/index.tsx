import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  memo,
  useContext,
  useMemo,
} from "react";
import axios from "axios";
// Use a variable for the loading icon URL rather than an import
const loadingIcon =
  "https://bakedbot-3e8a803d7a8a.herokuapp.com/images/loading-spinner-white.gif";
import Swal from "sweetalert2";
import ChatHistory from "./ChatHistory";
import "./main.css";
import useAuth from "../../hooks/useAuth";
import {
  getChats,
  getChatMessages,
  sendMessage,
  renameChat,
  deleteChat,
  recordFeedback,
  retryMessage,
  debugAuthToken,
  BASE_URL,
} from "../../utils/api";
import { signInWithPopup, GoogleAuthProvider } from "firebase/auth";
import { auth } from "../../config/firebase-config";
import {
  FaStore,
  FaArrowLeft,
  FaPaperPlane,
  FaChevronLeft,
  FaChevronRight,
  FaShoppingCart,
  FaMinus,
  FaPlus,
  FaRegTrashAlt,
  FaLongArrowAltRight,
  FaHome,
  FaTimes,
} from "react-icons/fa"; // Import the store icon and back arrow icon
import { HiMiniBars3CenterLeft } from "react-icons/hi2";
import SettingsPage from "./settings";
import { CartContext } from "./CartContext";
import {
  Product,
  ProductResponse,
  fetchProducts as apiFetchProducts,
  searchProducts,
  getThemeSettings,
  Event,
} from "../../utils/api";
// Removed react-router-dom dependency
import { ThemeSettings } from "./settings";
import Sidebar from "./Sidebar";
import EventList from "./Events";
import { ArrowLeft } from "iconsax-react";
import { FaWandMagicSparkles } from "react-icons/fa6";
import DealsView from "./DealsView";
import CheckoutView from "./OrderSummary";
import OrderConfirmation from "./Screens/OrderConfirmation";
import CartView from "./Screens/CartView";
import FeelingsScreen from "./Screens/Feelings";
import ProductDetailView from "./Screens/ProductDetail";
import ProductType from "./Screens/ProductType";
import { useCart } from "./CartContext";
import BakedBotHeading from "../../components/BakedBotHeading";
import { Orders } from "../../components/Orders";
import { Profile } from "../../components/Profile";

console.log("BakedBot Debug: ChatWidget module loading");

export const Spinner: React.FC = () => (
  <div className="bb-sm-spinner">
    <div className="bb-sm-bounce1"></div>
    <div className="bb-sm-bounce2"></div>
    <div className="bb-sm-bounce3"></div>
  </div>
);

// Add this helper function at the top of your file, outside the component
const getStateAbbreviation = (state: string): string => {
  const stateAbbreviations: { [key: string]: string } = {
    Alabama: "AL",
    Alaska: "AK",
    Arizona: "AZ",
    Arkansas: "AR",
    California: "CA",
    Colorado: "CO",
    Connecticut: "CT",
    Delaware: "DE",
    Florida: "FL",
    Georgia: "GA",
    Hawaii: "HI",
    Idaho: "ID",
    Illinois: "IL",
    Indiana: "IN",
    Iowa: "IA",
    Kansas: "KS",
    Kentucky: "KY",
    Louisiana: "LA",
    Maine: "ME",
    Maryland: "MD",
    Massachusetts: "MA",
    Michigan: "MI",
    Minnesota: "MN",
    Mississippi: "MS",
    Missouri: "MO",
    Montana: "MT",
    Nebraska: "NE",
    Nevada: "NV",
    "New Hampshire": "NH",
    "New Jersey": "NJ",
    "New Mexico": "NM",
    "New York": "NY",
    "North Carolina": "NC",
    "North Dakota": "ND",
    Ohio: "OH",
    Oklahoma: "OK",
    Oregon: "OR",
    Pennsylvania: "PA",
    "Rhode Island": "RI",
    "South Carolina": "SC",
    "South Dakota": "SD",
    Tennessee: "TN",
    Texas: "TX",
    Utah: "UT",
    Vermont: "VT",
    Virginia: "VA",
    Washington: "WA",
    "West Virginia": "WV",
    Wisconsin: "WI",
    Wyoming: "WY",
  };
  return stateAbbreviations[state] || state;
};

//add props
interface ChatWidgetProps {
  skipVerify?: boolean;
  view?: string;
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({
  skipVerify = false,
  view = "chat",
}) => {
  console.log(
    "BakedBot Debug: ChatWidget component rendering with view:",
    view
  );

  // Get customerID from URL path or fallback to config
  let customerID: string | undefined;

  // Try to get customerID from URL path like /chat/customerID
  const pathMatch = window.location.pathname.match(/\/chat\/([^/]+)/);
  if (pathMatch) {
    customerID = pathMatch[1];
  } else {
    console.log("BakedBot Debug: No customerID in URL path, using fallback");
  }

  // Fallback to config or default value if customerID is not available
  if (!customerID) {
    const config = (window as any).BakedBotConfig || {};
    customerID = config.siteIdentifier || "default";
    console.log("BakedBot Debug: Using fallback customerID:", customerID);
  }

  const [isAllowed, setIsAllowed] = useState<boolean | null>(true);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [showPopup, setShowPopup] = useState<boolean>(true);
  useEffect(() => {
    console.log("BakedBot Debug: ChatWidget mounted, container ready");

    const verifyOrigin = async () => {
      console.log(
        "BakedBot Debug: verifyOrigin running, skipVerify:",
        skipVerify
      );

      // Use the customerID from above or a fallback
      let customerId = customerID;
      if (!customerId) {
        console.log("BakedBot Debug: No customerID, using default value");
        customerId = "default";
      }

      if (skipVerify) {
        console.log(
          "BakedBot Debug: Skipping verification as skipVerify is true"
        );
        setIsAllowed(true);
        return;
      }

      try {
        const customerData = { allowedOrigins: ["*"] };
        if (customerData) {
          const allowedOrigins: string[] = customerData.allowedOrigins || [];
          const currentOrigin = window.location.origin;

          if (
            allowedOrigins.includes(currentOrigin) ||
            allowedOrigins.includes("*")
          ) {
            console.log("Origin is allowed");
            setIsAllowed(true);
          } else {
            console.warn(
              `Origin ${currentOrigin} is not allowed for customerID ${customerId}`
            );
            setIsAllowed(false);
          }
        } else {
          console.warn(`No customer found with ID: ${customerId}`);
          setIsAllowed(false);
        }
      } catch (error) {
        console.error("Error fetching customer data:", error);
        setIsAllowed(false);
      }
    };

    !skipVerify && verifyOrigin();
  }, [customerID]);

  const { displayName, photoURL, user } = useAuth();
  const { cart, addToCart, updateQuantity, removeFromCart, handleCheckout } =
    useCart();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState(1);
  const [prompts, setPrompts] = useState<string>("");
  const [chats, setChats] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [chatHistory, setChatHistory] = useState<
    {
      type: string;
      content: string;
      message_id: string;
      error?: boolean;
      data?: {
        suggested_next_questions?: string[];
        images?: any[];
        products?: any[];
      };
      specialType?: string;
      isStreaming?: boolean;
    }[]
  >([{ type: "ai", content: "Hey, how can I help?", message_id: "1" }]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeChatId, setActiveChatId] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isNewChat, setIsNewChat] = useState(true);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [products, setProducts] = useState<any[]>([]); // Add state for products
  const [searchQuery, setSearchQuery] = useState(""); // Add state for search query
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    chatId: string;
  } | null>(null);
  const [editingChatId, setEditingChatId] = useState<string | null>(null);
  const [newChatName, setNewChatName] = useState("");
  const renameInputRef = useRef<HTMLInputElement>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userLocation, setUserLocation] =
    useState<GeolocationCoordinates | null>(null);
  const [selectedProductType, setSelectedProductType] = useState<any>(null);
  const [userCity, setUserCity] = useState<string | null>(null);
  const [userState, setUserState] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [showCheckoutModal, setShowCheckoutModal] = useState(false);
  const [showOrdersModal, setShowOrdersModal] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);

  type Windows =
    | "chat"
    | "product"
    | "settings"
    | "cart"
    | "checkOut"
    | "feel"
    | "deals"
    | "order-confirm"
    | "events"
    | "product-type";

  const [currentView, setCurrentView] = useState<Windows>("chat");
  const [previousView, setPreviousView] = useState<Windows | null>(null);

  const [shouldPlay, setShouldPlay] = useState<boolean>(false);

  // Initialize the view based on the view prop
  useEffect(() => {
    // Only change if the view is a valid Windows type
    if (
      view &&
      [
        "chat",
        "deals",
        "events",
        "product",
        "product-type",
        "cart",
        "checkOut",
        "settings",
        "feel",
        "order-confirm",
      ].includes(view)
    ) {
      setCurrentView(view as Windows);

      // Reset appropriate states when view changes
      if (view === "deals") {
        // For deals view
        setSearchQuery("");
      } else if (view === "events") {
        // For events view
        if (userLocation) {
          // If we have user location, we could potentially trigger event fetching here
        }
      }
    }
  }, [view]); // This effect will run whenever the view prop changes

  const [settings, setSettings] = useState<ThemeSettings>({
    defaultTheme: "custom",
    botVoice: "male",
    allowedSites: [],
    defaultLanguage: "english",
    colors: {
      primaryColor: "#65715F",
      secondaryColor: "#00766D",
      backgroundColor: "#FFFFFF",
      headerColor: "#FFFFFF",
      textColor: "#2C2C2C",
      textSecondaryColor: "#00000066",
    },
  });
  function isLightColor(color: string): boolean {
    const hex = color.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128;
  }

  const applyTheme = (theme: ThemeSettings) => {
    const root = document.documentElement;
    root.style.setProperty("--primary-color", theme.colors.primaryColor);
    root.style.setProperty("--secondary-color", theme.colors.secondaryColor);
    root.style.setProperty("--background-color", theme.colors.backgroundColor);
    root.style.setProperty("--header-color", theme.colors.headerColor);
    root.style.setProperty("--text-color", theme.colors.textColor);
    root.style.setProperty(
      "--text-secondary-color",
      theme.colors.textSecondaryColor
    );
    root.style.setProperty(
      "--footer-text-color",
      isLightColor(theme.colors.backgroundColor) ? "#333333" : "#CCCCCC"
    );
  };

  useEffect(() => {
    applyTheme(settings);
  }, [settings]);

  useEffect(() => {
    if (shouldPlay) {
      playHandler();
      setShouldPlay(false);
    }
  }, [shouldPlay]);

  const handleViewSettings = () => {
    navigateTo("settings");
    setIsMenuOpen(false);
  };

  const handleSettingsClose = (signOut?: boolean) => {
    if (signOut) {
      loadChatHistory(null);
    }
    navigateTo("chat");
  };

  const handleSettingsSave = (newSettings: ThemeSettings) => {
    setSettings(newSettings);
  };

  const handleProductClick = (product) => {
    navigateTo("product");
    setSelectedProduct(product);
  };

  const handleBack = () => {
    navigateTo("chat");
    setSelectedProduct(null);
  };

  const fetchChatMessages = useCallback(async () => {
    if (!currentChatId || chatHistory.length > 0) return;
    try {
      const token = user ? await user.getIdToken() : null;
      const messages = await getChatMessages(currentChatId, token);
      setChatHistory(messages);
    } catch (error) {
      console.error("Error fetching chat messages:", error);
    }
  }, [currentChatId, user, chatHistory]);

  const isFetchingChatsRef = useRef<boolean>(false);
  const chatsLoadedRef = useRef<boolean>(false);

  const fetchUserChats = useCallback(async () => {
    try {
      if (!user) return;

      // Use a ref to track if we're already fetching
      if (isFetchingChatsRef.current) return;

      try {
        isFetchingChatsRef.current = true;
        const token = user ? await user.getIdToken() : null;
        const fetchedChats = await getChats(token);
        console.log("fetchedChats", fetchedChats);
        if (fetchedChats.length > 0) {
          setChats(fetchedChats);
        }
      } catch (error) {
        console.error("Error fetching user chats:", error);
      } finally {
        isFetchingChatsRef.current = false;
      }
    } catch (error) {
      console.error("Error fetching user chats:", error);
    }
  }, [user]);

  const footerTextColor = isLightColor(settings.colors.backgroundColor)
    ? "#333333"
    : "#CCCCCC";

  // Define this first before loadChatHistory references it
  const navigateTo = (newView: Windows) => {
    if (newView !== currentView) {
      setPreviousView(currentView);
      setCurrentView(newView);
    }
  };

  // Load chat history from a specific chat ID, or create a new chat if chatId is null
  const loadChatHistory = useCallback(
    async (chatId: string | null) => {
      // Use historyLoading instead of loading to not trigger the loading animation
      setHistoryLoading(true);
      navigateTo("chat");
      setIsMenuOpen(false);
      if (chatId === null) {
        // For new chats: reset state and scroll to top
        setIsNewChat(true);

        // Prevent excessive API calls
        if (!isFetchingChatsRef.current) {
          fetchUserChats();
        }

        setActiveChatId(null);
        setCurrentChatId(null);
        setChatHistory([
          {
            type: "ai",
            content: "",
            message_id: "2",
            specialType: "deals",
          },
          {
            type: "ai",
            content: "What type of product are you looking for?",
            message_id: "3",
            specialType: "productType",
          },
        ]);
        // Reset selection states
        setSelectedProductType(null);
        setSelectedFeelings([]);
        setShowFeelingSelector(false);
        setSelectionComplete(false);

        // Reset scroll position for new chats - ensure this runs after state updates
      } else {
        // For existing chats: load messages and scroll to bottom
        setIsNewChat(false);
        setActiveChatId(chatId);
        setCurrentChatId(chatId);
        try {
          const token = user ? await user.getIdToken() : null;
          const messages = await getChatMessages(chatId, token);
          setChatHistory(messages);
        } catch (error) {
          console.error("Error loading chat history:", error);
        }
      }
      setHistoryLoading(false);
    },
    [user, navigateTo, fetchUserChats, isFetchingChatsRef]
  );

  // Add back authentication useEffect
  useEffect(() => {
    // Use the user object from useAuth hook to determine login state
    setIsLoggedIn(!!user);

    // Only fetch chats once on initial authentication
    if (user && user.uid && !chatsLoadedRef.current) {
      // Debug auth token for troubleshooting
      debugAuthToken();

      // Load chat history
      fetchUserChats();
      chatsLoadedRef.current = true;
    }
  }, [user, fetchUserChats]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.valueAsNumber);
  };

  const handleModalBox = () => {
    setIsModalOpen(!isModalOpen);
  };

  const toggleMenu = () => {
    if (isMenuOpen) {
      setIsMenuOpen(false);
    } else if (currentView === "events" && selectedEvent) {
      // If we're on event details, go back to events list
      setSelectedEvent(null);
    } else if (currentView !== "chat") {
      if (previousView) {
        setCurrentView(previousView);
        setPreviousView("chat");
      } else {
        setCurrentView("chat");
      }
    } else {
      setIsMenuOpen(true);
    }
  };

  const handleViewChat = () => {
    loadChatHistory(null);
    navigateTo("chat");
    setIsMenuOpen(false);

    // Reset wizard selections when starting a new chat
    if (chatHistoryRef.current) {
      chatHistoryRef.current.setWizardSelections({});
    }
  };

  const handleViewStore = () => {
    navigateTo("deals");
    setIsMenuOpen(false);
  };

  const handleViewEvents = () => {
    navigateTo("events");
    setIsMenuOpen(false);
  };

  const handleViewOrders = () => {
    setShowOrdersModal(true);
    setIsMenuOpen(false);
  };

  const handleViewProfile = () => {
    setShowProfileModal(true);
    setIsMenuOpen(false);
  };

  const handleViewProductType = () => {
    navigateTo("product-type");
    setIsMenuOpen(false);
  };

  const isFetchingRef = useRef<boolean>(false);
  const isFetchingThemeRef = useRef<boolean>(false);

  useEffect(() => {
    const fetchThemeSettings = async () => {
      // Don't fetch if user is not available
      if (!user) return;

      // Use a ref to track if we're already fetching theme settings
      if (isFetchingThemeRef.current) return;

      try {
        isFetchingThemeRef.current = true;
        const token = user ? await user.getIdToken() : null;
        const settings = await getThemeSettings(token);
        if (settings) {
          setSettings(settings);
        }
      } catch (error) {
        console.error("Error fetching theme settings:", error);
      } finally {
        isFetchingThemeRef.current = false;
      }
    };
    // fetchThemeSettings();
  }, [user]);

  const getUserLocation = useCallback(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          setUserLocation(position.coords);
          try {
            const response = await axios.get(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}`
            );
            const city =
              response.data.address.city ||
              response.data.address.town ||
              response.data.address.village;
            const state =
              response.data.address.state || response.data.address.country;
            setUserCity(city);
            setUserState(state ? getStateAbbreviation(state) : null);
          } catch (error) {
            console.error("Error getting location details:", error);
          }
        },
        (error) => {
          console.error("Error getting user location:", error);
        }
      );
    } else {
      console.error("Geolocation is not supported by this browser.");
      // Swal.fire(
      //   "Error",
      //   "Geolocation is not supported by your browser.",
      //   "error"
      // );
    }
  }, []);

  useEffect(() => {
    getUserLocation();
  }, [getUserLocation]);

  const playHandler = async () => {
    if ((!prompts && !selectionComplete) || loading) return;
    setIsNewChat(false);

    let messageContent = prompts;

    // If selections are complete, create a formatted message
    if (
      selectionComplete &&
      selectedProductType &&
      selectedFeelings.length > 0
    ) {
      messageContent = `Show me ${
        selectedProductType.name
      } products that make me feel ${selectedFeelings.join(" and ")}`;
    }

    if (
      prompts.toLowerCase().includes("find new location") &&
      userCity &&
      userState
    ) {
      messageContent = `Find a new location in ${userCity}, ${userState}`;
    } else if (
      prompts.toLowerCase().includes("find my location") &&
      userLocation
    ) {
      messageContent += ` (User's location: Latitude ${userLocation.latitude}, Longitude ${userLocation.longitude})`;
      if (userCity && userState) {
        messageContent += `, City: ${userCity}, State: ${userState}`;
      }
    }

    const tempMessageId = Date.now().toString();
    const responseMessageId = tempMessageId + "_response";

    const userMessage = {
      type: "human",
      content: messageContent,
      message_id: tempMessageId,
    };

    const initialResponseMessage = {
      type: "ai",
      content: "",
      message_id: responseMessageId,
      error: false,
      isStreaming: true, // Add a flag for streaming messages
    };

    // Add both messages to chat history
    setChatHistory((prevHistory) => [
      ...prevHistory,
      userMessage,
      initialResponseMessage,
    ]);

    setPrompts("");
    setLoading(true);

    try {
      const token = await user?.getIdToken();

      // Setup WebSocket streaming callback
      const onStreamUpdate = (partialResponse: any) => {
        setChatHistory((prevHistory) => {
          const updatedHistory = [...prevHistory];
          const loadingIndex = updatedHistory.findIndex(
            (msg) => msg.message_id === responseMessageId
          );

          if (loadingIndex !== -1) {
            // Keep isStreaming flag true while receiving updates
            updatedHistory[loadingIndex] = {
              ...partialResponse,
              message_id: responseMessageId,
              isStreaming: true,
            };
          }
          return updatedHistory;
        });
      };

      // Format location as a string if available
      let locationStr: string | undefined = undefined;
      if (userLocation && userCity && userState) {
        locationStr = `${userCity}, ${userState} (${userLocation.latitude},${userLocation.longitude})`;
      } else if (userCity && userState) {
        locationStr = `${userCity}, ${userState}`;
      }

      // Call sendMessage which now tries WebSocket first with HTTP fallback
      const response = await sendMessage(
        messageContent,
        "general", // Default agent ID
        currentChatId,
        token,
        onStreamUpdate,
        locationStr
      );

      // Final update - replace streaming message with final response
      setChatHistory((prevHistory) => {
        const updatedHistory = [...prevHistory];
        const loadingIndex = updatedHistory.findIndex(
          (msg) => msg.message_id === responseMessageId
        );
        if (loadingIndex !== -1) {
          // Remove the streaming flag for the final update
          const finalResponse = {
            ...response,
            message_id: response.message_id || responseMessageId,
            isStreaming: false,
          };
          updatedHistory[loadingIndex] = finalResponse;
        }
        return updatedHistory;
      });

      if (response.chat_id) {
        setCurrentChatId(response.chat_id);
        setActiveChatId(response.chat_id);

        // Delay this call for 5 seconds to wait for server to update
        setTimeout(() => {
          fetchUserChats();
        }, 5000);
      }
    } catch (error: any) {
      console.error("Chat error:", error);
      setChatHistory((prevHistory) => {
        const updatedHistory = [...prevHistory];
        const loadingIndex = updatedHistory.findIndex(
          (msg) => msg.message_id === responseMessageId
        );
        if (loadingIndex !== -1) {
          updatedHistory[loadingIndex] = {
            type: "ai",
            content: "Sorry, I encountered an error. Please try again.",
            message_id: responseMessageId,
            error: true,
            isStreaming: false,
          };
        }
        return updatedHistory;
      });
    } finally {
      setLoading(false);
      // Reset selection states after sending
      if (selectionComplete) {
        setSelectedProductType(null);
        setSelectedFeelings([]);
        setShowFeelingSelector(false);
        setSelectionComplete(false);
      }
    }
  };

  const handleRetry = async (message_id: string) => {
    // Find the failed message and its corresponding user message
    const messageIndex = chatHistory.findIndex(
      (msg) => msg.message_id === message_id
    );
    if (messageIndex <= 0) return;

    const userMessage = chatHistory[messageIndex - 1];
    if (!userMessage || userMessage.type !== "human") return;

    // Remove the failed message
    setChatHistory((prevHistory) => {
      const newHistory = [...prevHistory];
      newHistory[messageIndex] = {
        type: "ai",
        content: "",
        message_id: message_id,
        error: false,
        isStreaming: true,
      };
      return newHistory;
    });

    setLoading(true);
    try {
      const token = await user?.getIdToken();

      // Setup streaming callback for retry
      const onStreamUpdate = (partialResponse: any) => {
        setChatHistory((prevHistory) => {
          const updatedHistory = [...prevHistory];
          const retryIndex = updatedHistory.findIndex(
            (msg) => msg.message_id === message_id
          );
          if (retryIndex !== -1) {
            updatedHistory[retryIndex] = {
              ...partialResponse,
              message_id: message_id,
              isStreaming: true,
            };
          }
          return updatedHistory;
        });
      };

      // Format location as a string if available
      let locationStr: string | undefined = undefined;
      if (userLocation && userCity && userState) {
        locationStr = `${userCity}, ${userState} (${userLocation.latitude},${userLocation.longitude})`;
      } else if (userCity && userState) {
        locationStr = `${userCity}, ${userState}`;
      }

      const response = await sendMessage(
        userMessage.content,
        "general", // Default agent ID
        currentChatId,
        token,
        onStreamUpdate,
        locationStr
      );

      setChatHistory((prevHistory) => {
        const newHistory = [...prevHistory];
        const retryIndex = newHistory.findIndex(
          (msg) => msg.message_id === message_id
        );
        if (retryIndex !== -1) {
          newHistory[retryIndex] = {
            ...response,
            message_id: response.message_id || message_id,
            isStreaming: false,
          };
        }
        return newHistory;
      });

      if (response.chat_id) {
        setCurrentChatId(response.chat_id);
        setActiveChatId(response.chat_id);
        fetchUserChats();
      }
    } catch (error) {
      console.error("Retry error:", error);
      setChatHistory((prevHistory) => {
        const newHistory = [...prevHistory];
        const retryIndex = newHistory.findIndex(
          (msg) => msg.message_id === message_id
        );
        if (retryIndex !== -1) {
          newHistory[retryIndex] = {
            type: "ai",
            content: "Sorry, I encountered an error. Please try again.",
            message_id: message_id,
            error: true,
            isStreaming: false,
          };
        }
        return newHistory;
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteMessage = (message_id: string) => {
    setChatHistory((prevHistory) => {
      const messageIndex = prevHistory.findIndex(
        (msg) => msg.message_id === message_id
      );
      if (messageIndex === -1) return prevHistory;

      // Remove both the error message and the user's message
      const newHistory = [...prevHistory];
      newHistory.splice(messageIndex - 1, 2);
      return newHistory;
    });
  };

  const chatEndRef = useRef<HTMLDivElement | null>(null);
  const chatTopRef = useRef<HTMLDivElement | null>(null);

  // Add a ref for the loadChatHistory function
  const loadChatHistoryRef = useRef(loadChatHistory);

  // Update the ref when loadChatHistory changes
  useEffect(() => {
    loadChatHistoryRef.current = loadChatHistory;
  }, [loadChatHistory]);

  // Add a new useEffect to initialize with a new chat on first load
  useEffect(() => {
    // Start with a new chat when the component first loads
    loadChatHistory(null);
  }, []); // Empty dependency array ensures this only runs once on mount

  // Simple scrolling effect - scroll to bottom whenever chat history changes
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory]);

  function capitalizeFirstLetter(string: string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  const LoginForm: React.FC<{ onLogin: () => void }> = ({ onLogin }) => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      try {
        onLogin();
      } catch (error) {
        console.error("Error signing in:", error);
      }
    };

    const handleGoogleSignIn = async () => {
      try {
        const provider = new GoogleAuthProvider();
        await signInWithPopup(auth, provider);
        onLogin();
      } catch (error) {
        console.error("Error signing in with Google:", error);
      }
    };

    return (
      <form
        onSubmit={handleSubmit}
        className="bb-sm-login-form flex flex-col gap-6 w-full max-w-md"
      >
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Email"
          className="bb-sm-login-input p-3 rounded bg-gray-700 text-lg"
        />
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Password"
          className="bb-sm-login-input p-3 rounded bg-gray-700 text-lg"
        />
        <button
          type="submit"
          className="bb-sm-login-button border-2 border-white rounded-md p-3 text-lg hover:bg-white hover:text-gray-800 transition-colors"
        >
          Login
        </button>
        <div className="text-center my-4 text-lg">or</div>
        <button
          type="button"
          onClick={handleGoogleSignIn}
          className="bb-sm-google-login-button border-2 border-white rounded-md p-3 text-lg flex items-center justify-center hover:bg-white hover:text-gray-800 transition-colors"
        >
          <img
            src="https://bakedbot-3e8a803d7a8a.herokuapp.com/images/google-icon.png"
            alt="Google"
            className="w-6 h-6 mr-2"
          />
          Login with Google
        </button>
      </form>
    );
  };

  const handleOutsideClick = useCallback(
    (e: MouseEvent) => {
      const mainArea = document.querySelector(".bb-sm-main-area");
      const header = document.querySelector(".bb-sm-chat-header");
      const contextMenu = document.querySelector(".bb-sm-context-menu");
      const chatRenameInput = document.querySelector(
        ".bb-sm-chat-rename-input"
      );
      if (
        mainArea &&
        mainArea.contains(e.target as Node) &&
        header &&
        !header.contains(e.target as Node) &&
        isMenuOpen
      ) {
        setIsMenuOpen(false);
      }
      if (contextMenu && !contextMenu.contains(e.target as Node)) {
        setContextMenu(null);
        setEditingChatId(null);
      }
      if (chatRenameInput && !chatRenameInput.contains(e.target as Node)) {
        setEditingChatId(null);
      }
    },
    [isMenuOpen]
  );

  useEffect(() => {
    document.addEventListener("mousedown", handleOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [handleOutsideClick]);

  const fetchProducts = useCallback(async (page = 1) => {
    setIsLoading(true);
    setError(null);
    try {
      console.log("fetchProducts called");
      const response = await apiFetchProducts(page);
      setProducts(response.products);
      setTotalPages(response.pagination.total_pages);
      setTotalProducts(response.pagination.total);
      setCurrentPage(page);
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("Failed to load products. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProducts(); // Fetch products on component mount
  }, [fetchProducts]);

  const handleSearch = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    if (!searchQuery) return;
    try {
      const response = await searchProducts(searchQuery, page);
      setProducts(response.products);
      setTotalPages(response.pagination.total_pages);
      setTotalProducts(response.pagination.total);
      setCurrentPage(page);
    } catch (error) {
      console.error("Error searching products:", error);
      setError("Failed to search products. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRenameChat = async (chatId: string, newName?: string) => {
    console.log("Rename chat called with ID:", chatId, "and name:", newName);

    if (newName) {
      // This is the save operation
      try {
        const token = user ? await user.getIdToken() : null;
        await renameChat(chatId, newName, token);

        // Update local state
        setChats((prevChats) =>
          prevChats.map((chat: any) =>
            chat.chat_id === chatId ? { ...chat, name: newName } : chat
          )
        );
        setEditingChatId(null);
      } catch (error) {
        console.error("Error renaming chat:", error);
        // Swal.fire({
        //   title: "Error",
        //   text: "Failed to rename chat. Please try again.",
        //   icon: "error",
        //   confirmButtonText: "OK",
        // });
      }
    } else {
      // This is the initial rename setup
      console.log("Setting up rename for chat:", chatId);
      setEditingChatId(chatId);
      const currentChat = chats.find((chat: any) => chat.chat_id === chatId);
      setNewChatName(currentChat?.name || "");
      setContextMenu(null);
    }
  };

  // Focus input when editing starts
  useEffect(() => {
    if (editingChatId && renameInputRef.current) {
      renameInputRef.current.focus();
    }
  }, [editingChatId]);

  const handleDeleteChat = async (chatId: string) => {
    console.log("Delete chat called with ID:", chatId);

    try {
      const token = user ? await user.getIdToken() : null;
      await deleteChat(chatId, token);

      // Update local state
      setChats((prevChats) =>
        prevChats.filter((chat: any) => chat.chat_id !== chatId)
      );

      // If the deleted chat is the active one, load a new chat
      if (activeChatId === chatId) {
        loadChatHistory(null);
      }
    } catch (error) {
      console.error("Error deleting chat:", error);
      Swal.fire({
        title: "Error",
        text: "Failed to delete chat. Please try again.",
        icon: "error",
        confirmButtonText: "OK",
      });
    }
  };

  const handleFeedback = async (
    message_id: string,
    feedbackType: "like" | "dislike"
  ) => {
    try {
      if (!user) return;
      const token = await user.getIdToken();
      const response = await recordFeedback(message_id, feedbackType, token);
      if (!response.ok) {
        throw new Error("Failed to record feedback");
      }
      console.log(
        `Feedback recorded: ${feedbackType} for message ${message_id}`
      );
    } catch (error) {
      console.error("Error recording feedback:", error);
    }
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product);
  };

  const handleSuggestedQuestionClick = (question: string) => {
    setPrompts(question);
    playHandler();
  };

  // Update contact info state initialization
  const [contactInfo, setContactInfo] = useState<{
    email: string;
    phone: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    birth_date?: string;
  }>(() => ({
    email: user?.email || "",
    phone: "",
    name: "",
    firstName: "",
    lastName: "",
    birth_date: "",
  }));

  // Update the hasValidEmail computation to ensure it returns a boolean
  const hasValidEmail = useMemo(() => {
    return Boolean(
      contactInfo.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contactInfo.email)
    );
  }, [contactInfo.email]);

  // Update contact info when user changes or logs in
  useEffect(() => {
    if (user?.email && !contactInfo.email) {
      setContactInfo((prev) => ({
        ...prev,
        email: user.email || "", // Ensure email is always a string
      }));
    }
  }, [user]);

  // Add new states to track user selections
  const [selectedFeelings, setSelectedFeelings] = useState<string[]>([]);
  const [showFeelingSelector, setShowFeelingSelector] =
    useState<boolean>(false);
  const [selectionComplete, setSelectionComplete] = useState<boolean>(false);

  // Add ref for ChatHistory component
  const chatHistoryRef = useRef<{
    setWizardSelections: (value: { [key: string]: boolean }) => void;
  } | null>(null);

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const HeaderNames: Record<string, string> = {
    store: "Chat",
    cart: "Checkout",
    feel: "Chat",
    chat: "Chat",
    new: "Chat",
    main: "Deals of the day",
    checkOut: "Checkout",
    events: "Events near you",
    "product-type": "Product types",
    // Add more mappings as needed
  };

  const getViewName = (view: string): string => {
    return HeaderNames[view] || "";
  };

  // Add a handler for product type selection
  const handleProductTypeSelect = (productType: any) => {
    setSelectedProductType(productType);
    setChatHistory((prevHistory) => {
      const updatedHistory = [...prevHistory];

      // Update product type selection message if it exists
      const productTypeIndex = updatedHistory.findIndex(
        (msg) => msg.specialType === "productType"
      );

      if (productTypeIndex >= 0) {
        updatedHistory[productTypeIndex] = {
          ...updatedHistory[productTypeIndex],
          content: `Selected product: ${productType.name}`,
        };

        // Add the feelings selector if not already present
        if (!showFeelingSelector) {
          updatedHistory.push({
            type: "ai",
            content: "How do you want to feel? (select up to 2)",
            message_id: Date.now().toString(),
            specialType: "feelings",
          });
          setShowFeelingSelector(true);
        }
      }

      return updatedHistory;
    });
  };

  // Update this function to better work with our chat flow
  const handleFeelingsSelect = (feelings: string[]) => {
    setSelectedFeelings(feelings);
    setSelectionComplete(true);

    // Update chat history to show feelings selection
    setChatHistory((prevHistory) => {
      const updatedHistory = [...prevHistory];
      // Find the feelings message
      const feelingsIndex = updatedHistory.findIndex(
        (msg) => msg.specialType === "feelings"
      );

      if (feelingsIndex !== -1) {
        // Update the content to show selection
        updatedHistory[feelingsIndex] = {
          ...updatedHistory[feelingsIndex],
          content: `Selected feelings: ${feelings.join(", ")}`,
        };
      }

      return updatedHistory;
    });

    // Create a formatted message based on selections
    const messageContent = `Show me ${
      selectedProductType.name
    } products that make me feel ${feelings.join(" and ")}`;

    // Add user message to chat
    const tempMessageId = Date.now().toString();
    const userMessage = {
      type: "human",
      content: messageContent,
      message_id: tempMessageId,
    };
    const loadingMessage = {
      type: "ai",
      content: "loading",
      message_id: tempMessageId + "_response",
      error: false,
    };

    // Add messages to chat history
    setChatHistory((prevHistory) => [
      ...prevHistory,
      userMessage,
      loadingMessage,
    ]);

    // Send the message to backend
    setLoading(true);
    (async () => {
      try {
        const token = await user?.getIdToken();

        // Format location as a string if available
        let locationStr: string | undefined = undefined;
        if (userLocation && userCity && userState) {
          locationStr = `${userCity}, ${userState} (${userLocation.latitude},${userLocation.longitude})`;
        } else if (userCity && userState) {
          locationStr = `${userCity}, ${userState}`;
        }

        const response = await sendMessage(
          messageContent,
          "general", // Default agent ID
          currentChatId,
          token,
          undefined, // No stream update callback needed here
          locationStr
        );

        setChatHistory((prevHistory) => {
          const updatedHistory = [...prevHistory];
          const loadingIndex = updatedHistory.findIndex(
            (msg) => msg.message_id === tempMessageId + "_response"
          );
          if (loadingIndex !== -1) {
            updatedHistory[loadingIndex] = response;
          }
          return updatedHistory;
        });

        if (response.chat_id) {
          setCurrentChatId(response.chat_id);
          setActiveChatId(response.chat_id);
          fetchUserChats();
        }
      } catch (error: any) {
        console.error("Chat error:", error);
        setChatHistory((prevHistory) => {
          const updatedHistory = [...prevHistory];
          const loadingIndex = updatedHistory.findIndex(
            (msg) => msg.message_id === tempMessageId + "_response"
          );
          if (loadingIndex !== -1) {
            updatedHistory[loadingIndex] = {
              type: "ai",
              content: "Sorry, I encountered an error. Please try again.",
              message_id: tempMessageId + "_response",
              error: true,
            };
          }
          return updatedHistory;
        });
      } finally {
        setLoading(false);
        setSelectedProductType(null);
        setSelectedFeelings([]);
        setShowFeelingSelector(false);
        setSelectionComplete(false);
      }
    })();
  };

  const toggleView = (view: Windows) => {
    setCurrentView(view);

    if (isOpen) {
      document.querySelector(".chat-popup")?.classList.add("fade-out");
      setTimeout(() => {
        setIsOpen(false);
        setShowPopup(true);
      }, 300);
    } else {
      setIsOpen(true);
      setShowPopup(false);
    }
  };

  // Email validation function
  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  // Checkout form submission handler
  const handleCheckoutSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (Object.keys(cart).length === 0) return;

    // Handle name logic - prioritize firstName/lastName, fall back to name or user displayName
    let firstName = contactInfo.firstName || "";
    let lastName = contactInfo.lastName || "";

    // If no firstName/lastName but we have name or user displayName, split it
    if (!firstName && !lastName) {
      const fallbackName = contactInfo.name || user?.displayName || "";
      const nameParts = fallbackName.split(" ");
      firstName = nameParts[0] || "";
      lastName = nameParts.slice(1).join(" ") || "";
    }

    const isEmailValid = validateEmail(contactInfo.email);
    const isFormValid =
      isEmailValid &&
      firstName.trim() !== "" &&
      lastName.trim() !== "" &&
      contactInfo.birth_date;

    if (!isFormValid) return;

    setIsCheckoutLoading(true);

    try {
      const success = await handleCheckout({
        email: contactInfo.email,
        phone: contactInfo.phone,
        name: `${firstName} ${lastName}`.trim(),
        firstName: firstName,
        lastName: lastName,
        birth_date: contactInfo.birth_date,
        couponCode: couponCode.trim(),
      });

      if (success) {
        setShowCheckoutModal(false);
        setCurrentView("order-confirm");
      }
    } catch (error) {
      setShowCheckoutModal(false);
      setCurrentView("order-confirm");
    } finally {
      setIsCheckoutLoading(false);
    }
  };

  return (
    <>
      {!isOpen ? (
        <>
          {showPopup && (
            <div className="chat-popup fade-in">
              <button className="close-btn" onClick={() => setShowPopup(false)}>
                ×
              </button>
              <button
                className="chat-option"
                onClick={() => toggleView("chat")}
              >
                Deals of the day
              </button>
              <button
                className="chat-option"
                onClick={() => toggleView("events")}
              >
                Events near me
              </button>
            </div>
          )}
          <button
            onClick={() => toggleView("chat")}
            className="toggle-button"
          ></button>
        </>
      ) : (
        <button
          onClick={() => {
            setIsOpen(false);
            setShowPopup(true);
          }}
          className="toggle-button close-toggle-button"
        >
          <svg
            className="close-icon"
            viewBox="0 0 24 24"
            width="24"
            height="24"
            fill="white"
          >
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </button>
      )}
      {isOpen && (
        <div className="bb-sm-chat-widget bb-sm-body">
          {true && (
            <div className="absolute right-2 bottom-20 flex justify-center items-center z-50 bb-sm-animate-open">
              <div className="bb-sm-chat-container p-0 pb-2 rounded-lg shadow-lg relative max-h-[calc(100vh-2rem)] overflow-hidden">
                <div className="md:flex md:flex-row flex-col gap-3 h-full max-h-full lg:min-w-[500px] p-2">
                  {/* Chat area */}
                  <div className="h-full w-full md:w-4/4 relative rounded-md p-2 flex flex-col overflow-hidden bb-sm-main-area">
                    <div className="bb-sm-chat-header flex items-center justify-between">
                      <div className="flex">
                        <button
                          className={`bb-sm-hamburger-menu ${
                            isMenuOpen || currentView !== "deals"
                              ? "bb-sm-open"
                              : ""
                          }`}
                          onClick={toggleMenu}
                        >
                          {currentView !== "chat" ? (
                            <FaArrowLeft />
                          ) : (
                            <>
                              {!isMenuOpen && (
                                <HiMiniBars3CenterLeft fontWeight={"bolder"} />
                              )}
                            </>
                          )}
                        </button>
                      </div>
                      <p className="p-1 text-[1.05rem] font-bold flex-1">
                        {getViewName(currentView || "")}
                      </p>
                      <div className="flex flex-row gap-5 justify-end items-center flex-1">
                        {/* <button 
                      className="px-5 p-1 bg-primary-color rounded"
                      onClick={()=>setCurrentView('chat')}><FaWandMagicSparkles color="white" /></button> */}
                        {Object.keys(cart).length > 0 && (
                          <div className="bg-primary-color text-white rounded text-base p-[6px]">
                            <button
                              className="flex justify-between  whitespace-nowrap min-w-max"
                              onClick={() => setShowCheckoutModal(true)}
                            >
                              Checkout Now
                              <span className="bb-sm-cart-count self-center text-sm">
                                {Object.values(cart).reduce(
                                  (sum, { product, quantity }) =>
                                    sum + quantity,
                                  0
                                )}
                              </span>
                            </button>
                          </div>
                        )}
                        {/* <button
                      className="bb-sm-header-icon"
                      onClick={handleViewProductType}
                    >
                      <FaWandMagicSparkles size={20} />
                    </button> */}
                        <button
                          className="bb-sm-header-icon"
                          onClick={handleViewChat}
                        >
                          <FaHome size={20} />
                        </button>
                        {/* <div className="bb-sm-cart-icon-container bb-sm-header-icon">
                      <button className="" onClick={() => navigateTo("cart")}>
                        <FaShoppingCart size={20} />
                        {Object.keys(cart).length > 0 && (
                          <span className="bb-sm-cart-count">
                            {Object.values(cart).reduce(
                              (sum, { product, quantity }) => sum + quantity,
                              0
                            )}
                          </span>
                        )}
                      </button>
                    </div> */}
                        {/* <button
                      className="bb-sm-close-button bb-sm-header-icon"
                      onClick={handleModalBox}
                    ></button> */}
                      </div>
                    </div>
                    {currentView === "deals" && (
                      <DealsView
                        products={products}
                        error={error}
                        isLoading={isLoading}
                      />
                    )}
                    {currentView === "product-type" && (
                      <ProductType
                        setSelectedProductType={setSelectedProductType}
                        navigateTo={navigateTo}
                      />
                    )}
                    {currentView === "product" && (
                      <ProductDetailView
                        product={selectedProduct}
                        navigateTo={navigateTo}
                      />
                    )}
                    {currentView === "feel" && (
                      <FeelingsScreen
                        selectedProductType={selectedProductType}
                        setCurrentView={setCurrentView}
                        setPrompts={setPrompts}
                        setShouldPlay={setShouldPlay}
                      />
                    )}
                    {currentView === "settings" && (
                      <SettingsPage
                        onClose={handleSettingsClose}
                        onSave={handleSettingsSave}
                        initialSettings={settings}
                      />
                    )}
                    {currentView === "cart" && (
                      <CartView navigateTo={navigateTo} />
                    )}
                    {currentView === "checkOut" && (
                      <CheckoutView
                        navigateTo={navigateTo as (view: Windows) => void}
                        setCurrentView={
                          setCurrentView as (view: Windows) => void
                        }
                        contactInfo={contactInfo}
                        setContactInfo={setContactInfo}
                        hasValidEmail={hasValidEmail}
                      />
                    )}
                    {currentView === "order-confirm" && (
                      <OrderConfirmation setCurrentView={setCurrentView} />
                    )}
                    {currentView === "events" && (
                      <EventList
                        userState={userState}
                        selectedEvent={selectedEvent}
                        setSelectedEvent={setSelectedEvent}
                      />
                    )}
                    {currentView === "chat" &&
                      (!isAllowed ? (
                        <div className="flex justify-center items-center h-full flex-col">
                          <BakedBotHeading
                            level={1}
                            className="text-2xl font-bold"
                          >
                            This widget is not allowed on this site.
                          </BakedBotHeading>
                          <p>Login to add this site to your whitelist.</p>
                        </div>
                      ) : (
                        <div className="bb-sm-chat-messages">
                          <ChatHistory
                            ref={chatHistoryRef}
                            allowCart={true}
                            chatHistory={chatHistory}
                            loading={loading}
                            historyLoading={historyLoading}
                            cart={cart}
                            updateQuantity={updateQuantity}
                            chatEndRef={chatEndRef}
                            chatTopRef={chatTopRef}
                            onFeedback={handleFeedback}
                            onRetry={handleRetry}
                            onAddToCart={handleAddToCart}
                            onProductClick={(product) => {
                              setSelectedProduct(product);
                              navigateTo("product");
                            }}
                            onSuggestedQuestionClick={
                              handleSuggestedQuestionClick
                            }
                            products={products}
                            onProductTypeSelect={handleProductTypeSelect}
                            onFeelingsSelect={handleFeelingsSelect}
                          />
                        </div>
                      ))}

                    {(currentView == "chat" || currentView == "deals") &&
                      isAllowed && (
                        <div className="bb-sm-chat-input">
                          {/* {chatHistory.length > 0 &&
                        !loading &&
                        chatHistory[chatHistory.length - 1]?.data
                          ?.suggested_next_questions && (
                          <div className="bb-sm-quick-responses-container">
                            {chatHistory[
                              chatHistory.length - 1
                            ]?.data?.suggested_next_questions?.map(
                              (question: string, index: number) => (
                                <button
                                  key={`quick-response-${index}`}
                                  className="bb-sm-quick-response-button"
                                  onClick={() =>
                                    handleSuggestedQuestionClick(question)
                                  }
                                >
                                  {question}
                                </button>
                              )
                            )}
                          </div>
                        )} */}
                          <textarea
                            className="resize-none w-full placeholder-text-secondary p-2 min-h-[40px] max-h-[120px] overflow-y-auto"
                            onKeyDown={(e) => {
                              if (e.key === "Enter" && !e.shiftKey) {
                                if (currentView === "chat") {
                                  e.preventDefault();
                                  playHandler();
                                }
                              }
                            }}
                            placeholder={
                              currentView === "chat"
                                ? "Ask me anything..."
                                : "Search here..."
                            }
                            value={
                              currentView === "chat" ? prompts : searchQuery
                            }
                            onChange={(e) => {
                              if (currentView === "chat") {
                                setPrompts(e.target.value);
                                const target = e.target as HTMLTextAreaElement;
                                target.style.height = "40px";
                                const newHeight = Math.min(
                                  Math.max(target.scrollHeight, 40),
                                  120
                                );
                                target.style.height = `${newHeight}px`;
                                const chatInput = target.closest(
                                  ".bb-sm-chat-input"
                                ) as HTMLElement;
                                if (chatInput) {
                                  chatInput.style.minHeight = `${
                                    newHeight + 24
                                  }px`; // 24px for padding
                                }
                              }
                            }}
                            rows={1}
                          />
                          <button
                            onClick={playHandler}
                            disabled={loading}
                            className="bb-sm-send-button"
                          >
                            {loading ? (
                              <img
                                src={loadingIcon}
                                className="w-5 h-5"
                                alt="Loading"
                              />
                            ) : (
                              <FaPaperPlane />
                            )}
                          </button>
                        </div>
                      )}
                  </div>

                  {/* Side menu */}
                  <Sidebar
                    isMenuOpen={isMenuOpen}
                    isLoggedIn={isLoggedIn}
                    chats={chats}
                    activeChatId={activeChatId}
                    onLoadChatHistory={loadChatHistory}
                    onLogin={handleLogin}
                    onViewSettings={handleViewSettings}
                    onViewStore={handleViewStore}
                    onViewEvents={handleViewEvents}
                    onViewOrders={handleViewOrders}
                    onViewProfile={handleViewProfile}
                    onRenameChat={handleRenameChat}
                    onDeleteChat={handleDeleteChat}
                    setIsMenuOpen={setIsMenuOpen}
                  />
                </div>
                <p
                  className="bb-sm-chat-footer mb-2 h-0 p-0 text-sm text-center"
                  style={{ color: footerTextColor }}
                >
                  Powered by BakedBot AI
                </p>
              </div>
            </div>
          )}

          {/* Checkout Modal */}
          {showCheckoutModal && (
            <div className="fixed inset-0 z-50 flex items-center justify-center">
              <div
                className="absolute inset-0 bg-black bg-opacity-50"
                onClick={() => setShowCheckoutModal(false)}
              />
              <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
                <div className="flex items-center justify-between p-4 border-b">
                  <BakedBotHeading level={2} className="text-xl font-bold">
                    Checkout
                  </BakedBotHeading>
                  <button
                    onClick={() => setShowCheckoutModal(false)}
                    className="p-2 hover:bg-gray-100 rounded-full"
                    aria-label="Close checkout modal"
                  >
                    <FaTimes />
                  </button>
                </div>

                <div className="flex flex-col lg:flex-row max-h-[calc(90vh-80px)]">
                  {/* Order Summary - Left Side */}
                  <div className="lg:w-1/2 p-4 border-r">
                    <BakedBotHeading
                      level={3}
                      className="text-lg font-semibold mb-4"
                    >
                      Order Summary
                    </BakedBotHeading>
                    <div className="max-h-96 overflow-y-auto space-y-2">
                      {Object.entries(cart).map(
                        ([productId, { product, quantity }]: any) => (
                          <div
                            key={productId}
                            className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                          >
                            <img
                              src={product.image_url}
                              alt={product.product_name}
                              className="w-12 h-12 object-cover rounded"
                            />
                            <div className="flex-1 min-w-0">
                              <p className="font-medium truncate">
                                {product.product_name}
                              </p>
                              <p className="text-xs text-gray-500">
                                ${product.latest_price} × {quantity}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="flex items-center bg-white rounded border">
                                <button
                                  onClick={() => updateQuantity(productId, -1)}
                                  className="p-1 hover:bg-gray-100"
                                >
                                  <FaMinus size={10} />
                                </button>
                                <span className="px-2 text-sm">{quantity}</span>
                                <button
                                  onClick={() => updateQuantity(productId, 1)}
                                  className="p-1 hover:bg-gray-100"
                                >
                                  <FaPlus size={10} />
                                </button>
                              </div>
                              <span className="font-semibold text-sm">
                                ${(product.latest_price * quantity).toFixed(2)}
                              </span>
                            </div>
                          </div>
                        )
                      )}
                    </div>

                    <div className="mt-4 pt-4 border-t">
                      <div className="flex justify-between items-center text-lg font-bold">
                        <span>Total:</span>
                        <span>
                          $
                          {Object.values(cart)
                            .reduce(
                              (sum, { product, quantity }) =>
                                sum + product.latest_price * quantity,
                              0
                            )
                            .toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Customer Information - Right Side */}
                  <div className="lg:w-1/2 p-4">
                    <form onSubmit={handleCheckoutSubmit} className="space-y-4">
                      <BakedBotHeading
                        level={3}
                        className="text-lg font-semibold mb-4"
                      >
                        Customer Information
                      </BakedBotHeading>

                      <div className="grid grid-cols-2 gap-3">
                        <input
                          type="text"
                          placeholder="First Name"
                          value={contactInfo.firstName || ""}
                          onChange={(e) =>
                            setContactInfo({
                              ...contactInfo,
                              firstName: e.target.value,
                            })
                          }
                          className="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                          required
                        />
                        <input
                          type="text"
                          placeholder="Last Name"
                          value={contactInfo.lastName || ""}
                          onChange={(e) =>
                            setContactInfo({
                              ...contactInfo,
                              lastName: e.target.value,
                            })
                          }
                          className="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                          required
                        />
                      </div>

                      <input
                        type="email"
                        placeholder="Email"
                        value={contactInfo.email}
                        onChange={(e) =>
                          setContactInfo({
                            ...contactInfo,
                            email: e.target.value,
                          })
                        }
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                        required
                      />

                      <input
                        type="tel"
                        placeholder="Phone (Optional)"
                        value={contactInfo.phone}
                        onChange={(e) =>
                          setContactInfo({
                            ...contactInfo,
                            phone: e.target.value,
                          })
                        }
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                      />

                      <div>
                        <label className="block text-sm font-medium text-gray-600 mb-1">
                          Date of Birth
                        </label>
                        <input
                          type="date"
                          value={contactInfo.birth_date || ""}
                          onChange={(e) =>
                            setContactInfo({
                              ...contactInfo,
                              birth_date: e.target.value,
                            })
                          }
                          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                          required
                        />
                      </div>

                      <input
                        type="text"
                        placeholder="Coupon Code (optional)"
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-color/50 focus:border-primary-color"
                      />

                      {!hasValidEmail && (
                        <p className="text-red-500 text-sm">
                          Please provide a valid email address to continue
                        </p>
                      )}

                      <button
                        type="submit"
                        disabled={
                          isCheckoutLoading ||
                          !hasValidEmail ||
                          !contactInfo.firstName ||
                          !contactInfo.lastName ||
                          !contactInfo.birth_date
                        }
                        className="w-full bg-primary-color text-white py-3 px-4 rounded-md text-lg font-semibold hover:bg-primary-color/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        {isCheckoutLoading
                          ? "Placing Order..."
                          : `Place Order - $${Object.values(cart)
                              .reduce(
                                (sum, { product, quantity }) =>
                                  sum + product.latest_price * quantity,
                                0
                              )
                              .toFixed(2)}`}
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          )}

          {contextMenu && (
            <div
              className="bb-sm-context-menu"
              style={{ top: contextMenu.y, left: contextMenu.x }}
            >
              <button
                className="text-md"
                onClick={() => handleRenameChat(contextMenu.chatId)}
              >
                Rename
              </button>
              <button
                className="text-md"
                onClick={() => handleDeleteChat(contextMenu.chatId)}
              >
                Delete
              </button>
            </div>
          )}

          {/* Orders Modal */}
          {showOrdersModal && (
            <Orders onClose={() => setShowOrdersModal(false)} />
          )}

          {/* Profile Modal */}
          {showProfileModal && (
            <Profile onClose={() => setShowProfileModal(false)} />
          )}
        </div>
      )}
    </>
  );
};

export default ChatWidget;
