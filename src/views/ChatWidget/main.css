:root {
  /* Theme settings */
  --primary-color: #22AD85;
  --secondary-color: #24504A;
  --background-color: #FFFFFF;
  --header-color: #FFFFFF;
  --text-color: #2C2C2C;
  --text-secondary-color: #1E1E1E;
  --button-text-color: #FFFFFF;

  /* Derived colors */
  --button-color: var(--primary-color);
  --button-hover-color: var(--secondary-color);
  --input-background: var(--header-color);
  --border-color: rgba(0,0,0,0.1);
}

@tailwind base;
@tailwind components;
@tailwind utilities;

.text-color{
  color: var(--text-color);
}
.text-secondary-color{
  color: var(--text-secondary-color);
}

.bg-primary-color{
  background-color: var(--primary-color);
}

.bg-secondary-color{
  background-color: var(--secondary-color);
}


.simplebar-content {
  height: -webkit-fill-available;
}

/* Base styles */
* {
  @apply box-border p-0 m-0;
}

.bb-sm-body {
  @apply font-jost text-white font-normal text-3xl;
  color: var(--text-color);
}

/* Component styles */
.bb-sm-li-customer-name {
  color: var(--text-color);
  margin-bottom: 5px;
  background-color: var(--surface-color);
  width: 50%;
  border-radius: 5px;
  padding: 5px;
  display: flex;
}

.bb-sm-li-customer-name label {
  width: 100%;
  margin-left: 5px;
}

/* Chat widget styles */
.bb-sm-chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  max-height: 40vh;
  z-index: 999999;
}

@media (max-width: 768px) {
  .bb-sm-chat-widget {
    visibility: visible;
  }
}

@keyframes bb-sm-openAnimation {
  0% {
    transform: translate(100%, 100%) scale(0.1);
    opacity: 0;
  }
  20% {
    transform: translate(100%, 100%) scale(0.2);
    opacity: 0.4;
  }
  50% {
    transform: translate(0, 0) scale(0.5);
    opacity: 0.7;
  }
  70% {
    transform: translate(0, 0) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translate(0, 0) scale(1);
  }
}

.bb-sm-animate-open {
  animation: bb-sm-openAnimation 0.5s ease-out;
}

/* Message styles */
.bb-sm-message {
  padding: 12px 16px;
  border-radius: 12px;
  max-width: 70%;
  word-wrap: break-word;
  margin-bottom: 8px;
  overflow-x: auto;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
}

.bb-sm-user-message {
  background-color: var(--primary-color);
  color: var(--background-color);
  align-self: flex-end;
  box-shadow: 
    0 2px 5px rgba(101, 113, 95, 0.2),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.bb-sm-bot-message {
  /* background-color: var(--secondary-color); */
  /* color: var(--background-color); */
  align-self: flex-start;
  border: 1px solid var(--border-color);
}

/* Chat container styles */
.bb-sm-chat-container {
  background-color: var(--background-color);
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  box-shadow: 
    0 12px 28px 0 rgba(0, 0, 0, 0.2),
    0 2px 4px 0 rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .bb-sm-chat-container {
    width: 90vw;
    max-width: none;
  }
}

.bb-sm-chat-header {
  background-color: var(--header-color);
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* margin: -10px -10px 0; */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  min-height: 40px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); */
}

.bb-sm-chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  background-color: var(--background-color);
  position: relative;
  z-index: 1;
}

.bb-sm-chat-input {
  background-color: var(--background-color);
  display: flex;
  align-items: center;
  min-height: 40px;
}

.bb-sm-body input, .bb-sm body textarea {
  outline: none;
  color: var(--text-color);
  background-color: var(--input-background);
  border: var(--border-color);
  border: 1px solid rgba(0, 0, 0, 0.3);
}

.bb-sm-chat-input textarea {
  flex-grow: 1;
  color: var(--text-color);
  background-color: var(--input-background);
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 25px;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  overflow-y: auto;
  height: auto;
  box-shadow: 
    inset 0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.bb-sm-chat-input textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 2px rgba(101, 113, 95, 0.2),
    inset 0 1px 3px rgba(0, 0, 0, 0.1);
  outline: none;
}

.bb-sm-input-color{
  
  color: var(--text-color);
  background-color: var(--input-background);
  border: var(--border-color);
}


.bb-sm-chat-input button {
  background-color: var(--button-color);
  color: var(--button-text-color);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 44px;
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Hamburger menu styles */
.bb-sm-hamburger-menu {
  width: 20px;
  height: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 20;
  position: relative;
}

.bb-sm-hamburger-menu div {
  width: 20px;
  height: 2px;
  background: var(--text-color);
  border-radius: 10px;
  transition: all 0.3s linear;
  position: relative;
  transform-origin: 1px;
}

.bb-sm-hamburger-menu:hover div {
  background: var(--primary-color);
}

.bb-sm-hamburger-menu.bb-sm-open div {
  width: 16px;
  transition: width 0.3s ease, height 0.3s ease; 
}

.bb-sm-hamburger-menu.bb-sm-open div:first-child {
  transform: rotate(45deg) translate(4px, 4px);
}

.bb-sm-hamburger-menu.bb-sm-open div:nth-child(2) {
  opacity: 0;
}

.bb-sm-hamburger-menu.bb-sm-open div:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Side Menu Styles */
.bb-sm-side-menu {
  position: absolute;
  top: 0;
  left: -100%;
  width: auto;
  height: 100%;
  background-color: var(--header-color);
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
  z-index: 15;
  overflow: hidden;
}

.bb-sm-side-menu.bb-sm-open {
  box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.5);
  padding-right: 10px;
  left: 0;
}

@media (min-width: 768px) {
  .bb-sm-side-menu {
    /* width: 250px; */
    /* left: -250px; */
  }
}

.bb-sm-side-menu-header {
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.bb-sm-robot-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
}

.bb-sm-robot-icon {
  width: 100px;
  height: 100px;
  object-fit: contain;
  border-radius: 50%;
  max-width: 200px;
}

.bb-sm-chat-history-title {
  text-align: center;
  margin-bottom: 16px;
  font-weight: bold;
  font-size: 18px;
  color: var(--text-color);
}

.bb-sm-back-button,
.bb-sm-edit-button {
  background: none;
  border: none;
  color: var(--text-secondary-color);
  cursor: pointer;
  padding: 8px;
}

.bb-sm-side-menu-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 16px;
}

.bb-sm-chat-history-scroll {
  overflow-y: auto;
}

.bb-sm-menu-item {
  background: none;
  border: none;
  color: var(--text-color);
  padding: 12px 16px;
  text-align: left;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 4px;
}

.bb-sm-menu-item.bb-sm-active,
.bb-sm-menu-item:hover {
  background-color: var(--secondary-color);
  color: var(--text-secondary-color);
}

.bb-sm-side-menu-footer {
  padding: 16px 16px 0;
}

.bb-sm-side-menu-footer h3 {
  color: var(--text-color);
  margin-bottom: 8px;
}

.bb-sm-featured-product {
  margin-bottom: 16px;
}

.bb-sm-featured-products {
  margin-top: 16px;
}

.bb-sm-add-to-cart-button {
  background-color: var(--primary-color);
  color: var(--button-text-color);
  border-radius: 8px;
  width: 100%;
  transition: background-color 0.2s ease;
}

.bb-sm-settings-button,
.bb-sm-settingss-button {
  background-color: var(--button-color);
  color: var(--text-secondary-color);
  border: none;
  padding: 5px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.bb-sm-settings-button {
  width: 100%;
}

.bb-sm-settingss-button {
  width: 60%;
  display: block;
  margin: 0 auto;
}


/* Right Panel Styles */
.bb-sm-right-panel {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px 8px 0;
  height: 100%;
}

.bb-sm-right-panel .bb-sm-panel-container {
  background-color: var(--primary-color);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.bb-sm-right-panel .bb-sm-product-info {
  background-color: var(--text-color);
  color: var(--secondary-color);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.bb-sm-right-panel .bb-sm-product-info h3,
.bb-sm-right-panel .bb-sm-product-info p {
  color: var(--secondary-color);
}

.bb-sm-right-panel .bb-sm-product-info img {
  border-radius: 8px;
}

.bb-sm-right-panel .bb-sm-desired-effects,
.bb-sm-right-panel .bb-sm-community-reviews {
  background-color: var(--primary-color);
  border-radius: 8px;
  padding: 12px;
}

.bb-sm-right-panel h4 {
  color: var(--text-color);
  margin-bottom: 8px;
}

.bb-sm-right-panel p {
  color: var(--text-color);
}

.bb-sm-right-panel .bb-sm-effects-icons label {
  color: var(--text-color);
}

.bb-sm-right-panel .bb-sm-effects-icons input[type="range"] {
  accent-color: var(--menu-hover-color);
}

.bb-sm-right-panel .bb-sm-effects-icons .bb-sm-flex span {
  color: var(--text-color);
}

.bb-sm-right-panel .bb-sm-reviews-list .bb-sm-review {
  background-color: var(--menu-hover-color);
  color: var(--text-color);
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
}

/* Ensure the chat container takes full height */
.bb-sm-chat-widget .bb-sm-animate-open > div {
  height: 100%;
}

/* Adjust the height of the main container */
.bb-sm-chat-widget .bb-sm-animate-open > div > div:first-child {
  height: calc(80vh - 2rem);
  max-height: 900px;
}

/* Ensure the right column has the same height as the left column */
.bb-sm-chat-widget .bb-sm-animate-open > div > div > div:last-child {
  height: 100%;
}

/* New chat view styles */
.bb-sm-new-chat-view {
  background-color: var(--background-color);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  overflow-y: auto;
}

/* Add a new class for the content wrapper */
.bb-sm-new-chat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  width: 100%;
  max-width: 400px;
  margin: auto;
}

/* Update the existing classes */
.bb-sm-new-chat-title {
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-align: center;
}

.bb-sm-new-chat-description {
  color: var(--text-color);
  font-size: 0.9rem;
  text-align: center;
  margin-bottom: 1rem;
}

.bb-sm-new-chat-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  width: 100%;
}

.bb-sm-new-chat-button {
  background-color: var(--button-color);
  color: var(--button-text-color);
  padding: 0.5rem;
  border-radius: 8px;
  font-size: 0.8rem;
  text-align: center;
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.bb-sm-new-chat-button-icon {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

/* Ensure the chat container takes full height */
.bb-sm-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Adjust the main area to take remaining space */
.bb-sm-main-area {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Ensure the chat messages area takes remaining space */
.bb-sm-chat-messages {
  flex-grow: 1;
  overflow-y: auto;
}

.bb-sm-send-button {
  background-color: var(--button-color);
  border: none;
  text-align: center;
  transition: background-color 0.3s ease;
  color: var(--button-text-color);
}

/* Styling for product list in bot messages */
.bb-sm-bot-message .bb-sm-prose {
  /* color: var(--text-secondary-color); */
}
.bb-sm-bot-message h3,
.bb-sm-bot-message strong {
  color: var(--text-secondary-color);
  font-weight: bold;
}
.bb-sm-bot-message p {
  margin-bottom: 0.5em;
}

.bb-sm-bot-message h3 {
  font-size: 1.2em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.bb-sm-bot-message ul {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 1em;
}

.bb-sm-bot-message ul li {
  margin-bottom: 0em;
}

.bb-sm-bot-message ul li strong {
  font-weight: bold;
  font-size: 1em;
}

.bb-sm-bot-message img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.bb-sm-bot-message hr {
  border: none;
  border-top: 1px solid var(--border-color);
  margin: 1em 0;
}

/* Store view styles */
.bb-sm-store-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  max-height: 100vh;
}
.bb-sm-store-header {

}

.bb-sm-search-bar {
  display: flex;
  margin-bottom: 10px;
}

.bb-sm-search-bar input {
  flex-grow: 1;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-background);
  color: var(--input-text-color);
}

.bb-sm-filters {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.bb-sm-filters button {
  padding: 5px 10px;
  border-radius: 15px;
  background-color: var(--input-background);
  border: none;
  color: var(--text-color);
}

.bb-sm-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.bb-sm-bakedbot-product-grid {
  --grid-cols: 2;
}

@media (min-width: 768px) {
  .bb-sm-bakedbot-product-grid {
    --grid-cols: 3;
  }
}

.bb-sm-product-item {
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Ensure space between items */
  height: 100%; /* Allow the item to take full height */
  border: 1px solid var(--border-color);
  border-radius: 8px;
  /* padding: 10px; */
}

.bb-sm-product-item img {
  width: 100%;
  height: 75px;
  object-fit: cover;
  border-radius: 4px;
}

.bb-sm-add-to-cart-button {
  margin-top: auto; /* Push the button to the bottom */
}

.bb-sm-pagination-button {
  background-color: var(--primary-color);
  color: var(--text-color);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bb-sm-pagination-button:hover:not(:disabled) {
  background-color: var(--menu-hover-color);
}

.bb-sm-pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading spinner */
.bb-sm-spinner {
  width: 70px;
  text-align: center;
}

.bb-sm-spinner > div {
  width: 18px;
  height: 18px;
  background-color: var(--text-color);
  border-radius: 100%;
  display: inline-block;
  animation: bb-sm-sk-bouncedelay 1.4s infinite ease-in-out both;
}

.bb-sm-spinner .bb-sm-bounce1 {
  animation-delay: -0.32s;
}

.bb-sm-spinner .bb-sm-bounce2 {
  animation-delay: -0.16s;
}

@keyframes bb-sm-sk-bouncedelay {
  0%, 80%, 100% { 
    transform: scale(0);
  } 40% { 
    transform: scale(1.0);
  }
}

.bb-sm-error-message {
  /* background-color: var(--secondary-color); */
  border: none;
  padding: 12px 16px;
  border-radius: 12px;
  margin-bottom: 8px;
  /* box-shadow: 0 2px 5px rgba(0, 118, 109, 0.2); */
}

.bb-sm-error-message p {
  /* color: var(--background-color); */
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.bb-sm-error-actions {
  display: flex;
  gap: 8px;
}

.bb-sm-retry-button {
  /* background-color: transparent; */
  /* color: var(--background-color); */
  border: 1px solid var(--border-color);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.bb-sm-retry-button:hover {
  background-color: var(--background-color);
  color: var(--secondary-color);
}

.bb-sm-delete-button {
  background-color: #ff4444;
  color: white;
}

.bb-sm-retry-button:hover,
.bb-sm-delete-button:hover {
  opacity: 0.8;
}

.bb-sm-loading-container {
  height: 16rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.bb-sm-product-detail-image {
  width: auto;
  height: 200px;
  object-fit: contain;
  border-radius: 4px;
}

.bb-sm-data-container > div {
  background-color: var(--surface-color);
  padding: 5px;
  border-radius: 5px;
  min-width: 25%;
}

/* Chat item styles */
.bb-sm-chat-item-container {
  position: relative;
}

.bb-sm-chat-item-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
}

.bb-sm-chat-options-button {
  opacity: 0.3;
  transition: opacity 0.2s ease;
  padding: 5px;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
}

.bb-sm-chat-item-wrapper:hover .bb-sm-chat-options-button {
  opacity: 1;
}

/* Make sure the options button is always visible on touch devices */
@media (hover: none) {
  .bb-sm-chat-options-button {
    opacity: 1;
  }
}

.bb-sm-context-menu {
  position: fixed;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px 0;
  z-index: 1500;
  min-width: 140px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.bb-sm-context-menu button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-color);
  font-size: 14px;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.bb-sm-context-menu button:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
}

.bb-sm-context-menu button:active {
  background-color: #e5e5e5;
}

.bb-sm-chat-rename-input {
  display: flex;
  align-items: center;
  padding: 0 15px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.bb-sm-chat-rename-input input {
  flex-grow: 1;
  margin-right: 5px;
  padding-left: 5px;
  background-color: var(--input-background);
  color: var(--input-text-color);
}

/* Settings page styles */
.bb-sm-settings-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
  background-color: var(--background-color);
  color: var(--text-color);
}

.bb-sm-settings-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.bb-sm-settings-content {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Add a new class for the scrollable content area */
.bb-sm-settings-scrollable-content {
  flex-grow: 1;
  overflow-y: auto;
  padding-bottom: 1rem; /* Add some padding at the bottom */
}

.bb-sm-setting-item {
  margin-bottom: 1rem;
}

.bb-sm-setting-item label {
  display: block;
  margin-bottom: 0.5rem;
}

.bb-sm-setting-item select {
  width: 100%;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: var(--input-background);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.bb-sm-settings-footer {
  margin-top: auto; 
  padding-top: 1rem;
  background-color: var(--background-color);
}

.bb-sm-save-button {
  width: 100%;
  padding: 0.5rem;
  background-color: var(--button-color);
  color: var(--text-secondary-color);
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.bb-sm-save-button:hover {
  background-color: var(--button-hover-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bb-sm-bot-message h3 {
    font-size: 1.1em;
  }

  .bb-sm-bot-message ul li {
    font-size: 0.9em;
  }
}

/* Close button styles */
.bb-sm-close-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 20;
  position: relative;
}

.bb-sm-close-button::before,
.bb-sm-close-button::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background: var(--text-color);
  border-radius: 10px;
  transition: all 0.3s linear;
}

.bb-sm-close-button::before {
  transform: rotate(45deg);
}

.bb-sm-close-button::after {
  transform: rotate(-45deg);
}

.bb-sm-close-button:hover::before,
.bb-sm-close-button:hover::after {
  background: var(--primary-color);
}

.bb-sm-chat-footer {
  color: var(--text-secondary-color);
}

.bb-sm-view-store-button {
  display: flex;
  align-items: center;
  background-color: var(--button-color);
  color: var(--text-secondary-color);
  font-size: 12px;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.bb-sm-view-store-button:hover {
  background-color: var(--button-hover-color);
}

.bb-sm-view-store-button svg {
  margin-right: 4px;
  font-size: 14px;
}

/* Update the cart icon container and count styles */
.bb-sm-cart-icon-container {
  position: relative;
  display: flex;
  height: 28px; /* Adjust this value as needed */
}
.bb-sm-header-icon:hover{
  color: var(--primary-color);
}

.bb-sm-cart-count {
  background-color:#D0E1C7;
  color: var(--button-color);
  border-radius: 30%;
  padding: 0px 6px;
  margin-left: 8px;
}

.bb-sm-cart-view,
.bb-sm-checkout-view {
}

.bb-sm-cart-item {
  padding: 0.5rem;
}

.bb-sm-remove-from-cart-button,
.bb-sm-checkout-button,
.bb-sm-place-order-button {
  background-color: var(--primary-color);
  color: white;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.bb-sm-remove-from-cart-button:hover,
.bb-sm-checkout-button:hover,
.bb-sm-place-order-button:hover {
  background-color: var(--secondary-color);
}

.bb-sm-remove-button {
  color: var(--text-secondary-color);
  cursor: pointer;
}

.bb-sm-remove-button:hover {
  color: var(--primary-color);
}

.bb-sm-quantity-selector {
  margin-top: 0.2rem;
  background-color: rgba(101, 113, 95, 0.1);
}

.bb-sm-quantity-button {
  color: var(--primary-color);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.bb-sm-quantity-button:hover {
  background-color: var(--primary-color);
  color: #FFFFFF;
}

.bb-sm-delete-button{
  background-color: #ff4500;
  color: var(--text-color);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
}

.bb-sm-delete-button:hover {
  color: var(--primary-color);
}


/* Update these styles in your main.css file */

.bb-sm-message-container {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.bb-sm-bot-container {
  align-items: flex-start;
}

.bb-sm-feedback-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2px;
  width: 100%;
}

.bb-sm-left-buttons,
.bb-sm-right-buttons {
  display: flex;
  gap: 8px;
}

.bb-sm-left-buttons{
  margin-left: 5px;
}

.bb-sm-feedback-button {
  background: none;
  border: none;
  color: var(--text-secondary-color);
  opacity: 0.6;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.bb-sm-feedback-button:hover {
  opacity: 1;
}

.bb-sm-feedback-button.bb-sm-feedback-given {
  opacity: 1;
  color: var(--primary-color);
}

.bb-sm-feedback-button:disabled {
  cursor: not-allowed;
}

.bb-sm-bot-message,
.bb-sm-user-message {
  max-width: 100%;
}

.bb-sm-user-message {
  align-self: flex-end;
}

/* Toggle container styles */
.bb-sm-toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bb-sm-toggle-label {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.7;
  transition: opacity 0.3s;
}

.bb-sm-toggle-active {
  opacity: 1;
  font-weight: bold;
}

/* Switch styles */
.bb-sm-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  background-color: var(--secondary-color);
  border-radius: 34px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bb-sm-switch-on {
  background-color: var(--primary-color);
}

.bb-sm-switch-slider {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: var(--text-secondary-color);
  border-radius: 50%;
  transition: 0.3s;
}

.bb-sm-switch-on .bb-sm-switch-slider {
  transform: translateX(26px);
}

/* Admin settings styles */
.bb-sm-admin-settings {
  background-color: var(--surface-color);
  border-radius: 8px;
  margin-bottom: 24px;
}

/* Toggle container styles */
.bb-sm-toggle-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.bb-sm-toggle-label {
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.7;
  transition: opacity 0.3s, color 0.3s;
}

.bb-sm-toggle-active {
  opacity: 1;
  color: var(--primary-color);
  font-weight: 600;
}

/* Switch styles */
.bb-sm-switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 28px;
  background-color: var(--secondary-color);
  border-radius: 14px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bb-sm-switch-on {
  background-color: var(--primary-color);
}

.bb-sm-switch-slider {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: var(--text-secondary-color);
  border-radius: 50%;
  transition: 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bb-sm-switch-on .bb-sm-switch-slider {
  transform: translateX(24px);
}

/* Input styles */
.bb-sm-input-color {
  background-color: var(--input-background);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  transition: border-color 0.3s, box-shadow 0.3s;
}

.bb-sm-input-color:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(34, 173, 133, 0.2);
  outline: none;
}

.bb-sm-accordion-button {
  background-color: var(--input-background);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 10px;
}
.bb-sm-theme-settings{
  margin-bottom: 5rem;
}

.bb-sm-scroll-to-bottom {
  position: absolute;
  bottom: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-color);
  color: var(--text-secondary-color);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.3s ease;
  opacity: 0.7;
}

.bb-sm-scroll-to-bottom:hover {
  opacity: 1;
}
.bb-sm-product-card{
  width: 100%;
  padding: 0.4rem;
}

/* Product grid within chat messages */
.bb-sm-chat-messages .bb-sm-bakedbot-product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  width: 100%;
}

.bb-sm-chat-messages .bb-sm-product-item {
  border: 1px solid var(--border-color);
  border-radius: 6px;
  overflow: hidden;
}

.bb-sm-chat-messages .bb-sm-product-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
}

.bb-sm-chat-messages .bb-sm-product-item-content {
  padding: 6px;
}

.bb-sm-chat-messages .bb-sm-product-name {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bb-sm-chat-messages .bb-sm-product-price,
.bb-sm-chat-messages .bb-sm-product-weight {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
}

.bb-sm-chat-messages .bb-sm-quantity-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.bb-sm-chat-messages .bb-sm-quantity-button {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  /* background: var(--primary-color); */
}

.bb-sm-chat-messages .bb-sm-add-to-cart-button {
  width: 100%;
  padding: 4px;
  font-size: 12px;
  border-radius: 4px;
  background: var(--primary-color);
}

/* Add these styles to your main.css file */

.bb-sm-retailer-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--header-color);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  transition: transform 0.2s ease;
}

.bb-sm-retailer-card:hover {
  transform: translateY(-2px);
}

.bb-sm-retailer-info {
  flex: 1;
}

.bb-sm-retailer-name {
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-color);
}

.bb-sm-retailer-location {
  font-size: 0.9em;
  color: var(--text-secondary-color);
}

.bb-sm-map-button {
  background-color: var(--primary-color);
  color: var(--text-color);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-left: 12px;
}

.bb-sm-map-button:hover {
  background-color: var(--secondary-color);
}

.bb-sm-retailers-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}


.bb-sm-next-button {
  background-color: var(--button-color);
  border: none;
  text-align: center;
  transition: background-color 0.3s ease;
  color: var(--button-text-color);
}

.bb-sm-place-order-button {
  background-color: var(--button-color);
  border: none;
  text-align: center;
  transition: background-color 0.3s ease;
  color: var(--button-text-color);
}

.bb-sm-redeem-button {
  background-color: var(--button-color);
  border: none;
  text-align: center;
  transition: background-color 0.3s ease;
  color: var(--button-text-color);
}

.bb-sm-redeem-button:hover {
  background-color: var(--secondary-color);
}

.bb-sm-message-data {
  margin-top: 8px;
}

.bb-sm-images-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  width: 100%;
}

.bb-sm-images-grid img {
  width: 100%;
  height: auto;
  min-height: 200px;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.2s;
  cursor: pointer;
}

.bb-sm-images-grid img:hover {
  transform: scale(1.02);
}

.bb-sm-suggested-questions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid rgba(var(--text-color-rgb), 0.1);
}

.bb-sm-suggested-question-button {
  background-color: var(--primary-color);
  color: var(--background-color);
  transition: all 0.2s;
  font-size: 13px;
}

.bb-sm-suggested-question-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.bb-sm-message-text {
  margin-bottom: 8px;
}

.bb-sm-products-grid {
  margin-top: 12px;
}

/* Quick Response Container */
.bb-sm-quick-responses-container {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  padding: 8px 12px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: transparent;
}

.bb-sm-quick-responses-container::-webkit-scrollbar {
  display: none;
}

/* Quick Response Button */
.bb-sm-quick-response-button {
  padding: 8px 16px;
  background-color: rgba(82, 82, 82, 0.9);
  color: #FFFFFF;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bb-sm-quick-response-button:hover {
  background-color: rgba(102, 102, 102, 0.95);
  transform: translateY(-1px);
}

/* Remove old quick responses styles */
.bb-sm-quick-responses {
  display: none;
}

/* Chat Input Container */
.bb-sm-chat-input {
  position: relative;
  padding: 12px;
  background-color: var(--background-color);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
}

/* Add styles for special message containers */
.bb-sm-special-container {
  min-width: 90%;
  height: 100%;
}

.bb-sm-special-message {
  max-width: 100%;
  width: 100%;
  padding: 10px;
}

.bb-sm-special-message-content {
  width: 100%;
}

/* Styles for product cards in special messages */
.bb-sm-special-message .bb-sm-product-item {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Loading animation styles */
.bb-sm-loading-animation {
  padding: 8px 0;
  width: 100%;
}

.bb-sm-loading-animation img{
  filter: invert(0.5);
}
.bb-sm-loading-text {
  flex: 1;
}

.bb-sm-loading-dots {
  display: flex;
  gap: 4px;
}

.bb-sm-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--primary-color);
  opacity: 0.7;
  animation: bb-sm-pulse 1.5s infinite ease-in-out;
}

.bb-sm-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.bb-sm-dot:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes bb-sm-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.bb-sm-loading-icon {
  animation: bb-sm-spin 1.5s infinite linear;
}

@keyframes bb-sm-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade-in animation for loading messages */
.bb-sm-loading-text p {
  animation: bb-sm-fade-in 0.5s ease-in-out;
}

@keyframes bb-sm-fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

/* Toggle Button Animation */
.toggle-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 100px;
  border: none;
  cursor: pointer;
  z-index: 10000;
  background-size: contain;
  transition: transform 0.3s ease, width 0.3s ease, height 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Close toggle button styles */
.close-toggle-button {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background: #00A67D !important;
  bottom: 20px !important;
  right: 20px !important;
  transition: all 0.2s ease !important;
  align-items: center !important;
  justify-content: center !important;
  display: flex !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  z-index: 10000 !important;
}

.close-toggle-button:hover {
  transform: scale(1.1) !important;
  background: #008e6a !important;
}

.close-icon {
  width: 24px;
  height: 24px;
}

/* Popup Animation */
.chat-popup {
  position: fixed;
  bottom: 100px;
  right: 90px;
  width: 220px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  padding: 10px;
  z-index: 10001;
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.fade-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.fade-out {
  opacity: 0 !important;
  transform: translateY(10px) !important;
}

/* Close Button */
.close-btn {
  align-self: end;
  border: none;
  background: transparent;
  margin: 0 0 6px 0;
  cursor: pointer;
  font-size: 14px;
  padding: 3px 6px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #000;
  transform: scale(1.05);
}

/* Chat Option Buttons */
.chat-option {
  display: flex;
  width: 100%;
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 8px;
  background-color: #6b735f;
  color: white;
  text-align: center;
  border: none;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.chat-option:hover {
  background-color: #5a6151;
  transform: translateY(-1px);
}

.chat-option:active {
  transform: translateY(0);
}

.chat-option::after {
  content: "";
}

/* New styles for sidebar and login */
.bb-sm-new-chat-button {
  transition: all 0.2s ease;
  font-weight: 500;
  margin-bottom: 8px;
}

.bb-sm-new-chat-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.bb-sm-google-login-button {
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.bb-sm-google-login-button:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bb-sm-side-menu-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 12px;
  margin-top: auto;
}

/* Add responsive width for the sidebar */
@media (min-width: 480px) {
  .bb-sm-side-menu.bb-sm-open {
    width: 320px;
  }
}

@media (max-width: 479px) {
  .bb-sm-side-menu.bb-sm-open {
    width: 90vw;
    max-width: 320px;
  }
}

/* Tablet-specific fixes for Events component */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Ensure the main chat container doesn't force a min-width on tablets */
  .bb-sm-chat-container {
    width: 95vw !important;
    max-width: 95vw !important;
  }
  
  /* Fix events grid to prevent horizontal overflow */
  .bb-sm-main-area {
    max-width: 100%;
    overflow-x: hidden;
  }
  
  /* Ensure event cards don't cause layout issues */
  .bb-sm-main-area .grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  }
  
  /* Fix aspect ratio containers on tablets */
  .bb-sm-main-area [class*="pb-[56.25%]"] {
    max-width: 100%;
    width: 100%;
  }
  
  /* Specifically target events grid layout */
  .bb-sm-main-area div[class*="grid-cols-"] {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
  }
}