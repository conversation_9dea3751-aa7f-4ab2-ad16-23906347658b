/* HeadlessHomepage Component Styles */
.bakedbot-headless-homepage {
  --primary-color: #065f46;
  --secondary-color: #10b981;
  --background-color: #ffffff;
  --text-color: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --hover-color: #f3f4f6;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  height: auto;
  min-height: auto;
  overflow: visible;
  max-width: 1400px;
  margin: 0 auto;
  /* Ensure smooth transitions during enhancement */
  transition: all 0.2s ease;
}

/* Typography Overrides - Prevent WordPress Theme Interference */
.bakedbot-headless-homepage h1,
.bakedbot-headless-homepage h2,
.bakedbot-headless-homepage h3,
.bakedbot-headless-homepage h4,
.bakedbot-headless-homepage h5,
.bakedbot-headless-homepage h6,
.bakedbot-headless-homepage h1,
.bakedbot-headless-homepage h2,
.bakedbot-headless-homepage h3,
.bakedbot-headless-homepage h4,
.bakedbot-headless-homepage h5,
.bakedbot-headless-homepage h6 {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: inherit !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-decoration: none !important;
  text-shadow: none !important;
  font-style: normal !important;
}

.bakedbot-headless-homepage h1,
.bakedbot-headless-homepage h1 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: var(--primary-color) !important;
}

.bakedbot-headless-homepage h2,
.bakedbot-headless-homepage h2 {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  color: inherit !important;
}

.bakedbot-headless-homepage h3,
.bakedbot-headless-homepage h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}


.bakedbot-headless-homepage h5,
.bakedbot-headless-homepage h5 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

.bakedbot-headless-homepage h6,
.bakedbot-headless-homepage h6 {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: var(--text-color) !important;
}

/* Special heading styles for homepage sections */
.bakedbot-headless-homepage .hero-title,
.bakedbot-headless-homepage .hero-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: white !important;
  line-height: 1.2 !important;
  margin: 0 0 1rem 0 !important;
}

.bakedbot-headless-homepage .hero-subtitle,
.bakedbot-headless-homepage .hero-subtitle {
  font-size: 1.125rem !important;
  font-weight: 400 !important;
  color: inherit !important;
  opacity: 0.9 !important;
  line-height: 1.5 !important;
  margin: 0 0 2rem 0 !important;
}

.bakedbot-headless-homepage .site-title,
.bakedbot-headless-homepage .site-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: var(--primary-color) !important;
  margin: 0 !important;
}

.bakedbot-headless-homepage .section-header h4,
.bakedbot-headless-homepage .section-header h4 {
  font-size: 1rem !important;
  font-weight: 700 !important;
  color: inherit !important;
  margin: 0 !important;
}

/* Text elements */
.bakedbot-headless-homepage p,
.bakedbot-headless-homepage div,
.bakedbot-headless-homepage label,
.bakedbot-headless-homepage p,
.bakedbot-headless-homepage div,
.bakedbot-headless-homepage label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: inherit !important;
  line-height: 1.5 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-decoration: none !important;
  text-shadow: none !important;
  font-style: normal !important;
}

/* Links */
.bakedbot-headless-homepage a,
.bakedbot-headless-homepage a {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: var(--primary-color) !important;
  text-decoration: none !important;
  font-weight: inherit !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
}

.bakedbot-headless-homepage a:hover,
.bakedbot-headless-homepage a:focus,
.bakedbot-headless-homepage a:hover,
.bakedbot-headless-homepage a:focus {
  color: var(--secondary-color) !important;
  text-decoration: none !important;
}

/* Buttons */
.bakedbot-headless-homepage button,
.bakedbot-headless-homepage input[type="button"],
.bakedbot-headless-homepage input[type="submit"],
.bakedbot-headless-homepage button,
.bakedbot-headless-homepage input[type="button"],
.bakedbot-headless-homepage input[type="submit"] {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-weight: 500 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
  line-height: 1.5 !important;
}

/* Form elements */
.bakedbot-headless-homepage input,
.bakedbot-headless-homepage select,
.bakedbot-headless-homepage textarea,
.bakedbot-headless-homepage input,
.bakedbot-headless-homepage select,
.bakedbot-headless-homepage textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: inherit !important;
  font-weight: 400 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
  line-height: 1.5 !important;
}

/* Progressive Enhancement Support */
.bakedbot-headless-homepage.enhanced {
  /* Mark when React has taken over */
  position: relative;
}

/* Enhanced component styling */
.bakedbot-headless-homepage.enhanced .cart-button,
.bakedbot-headless-homepage.enhanced .add-to-cart-btn {
  pointer-events: auto;
  opacity: 1;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

/* Dark theme support */
.bakedbot-headless-homepage.theme-dark {
  --background-color: #111827;
  --text-color: #f9fafb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
  --hover-color: #1f2937;
}

/* Loading state */
.bakedbot-headless-homepage.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.bakedbot-loading-spinner {
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
}

/* Header */
.bakedbot-homepage-header {
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow);
}

.bakedbot-header-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.bakedbot-header-left {
  flex-shrink: 0;
}

.bakedbot-site-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.bakedbot-header-center {
  flex: 1;
  max-width: 600px;
}

.bakedbot-search-form {
  width: 100%;
}

.bakedbot-search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--background-color);
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  transition: border-color 0.2s;
}

.bakedbot-search-container:focus-within {
  border-color: var(--primary-color);
}

.bakedbot-search-icon {
  position: absolute;
  left: 1rem;
  color: var(--text-secondary);
  z-index: 1;
}

.bakedbot-search-input {
  flex: 1;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: none;
  background: transparent;
  font-size: 1rem;
  outline: none;
  color: var(--text-color);
}

.bakedbot-search-input::placeholder {
  color: var(--text-secondary);
}

.bakedbot-search-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 0.5rem 0.5rem 0;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bakedbot-search-button:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bakedbot-cart-button {
  position: relative;
  padding: 0.75rem;
  background: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bakedbot-cart-button:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.bakedbot-cart-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  background: var(--secondary-color);
  color: white !important;
  border-radius: 50%;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Main Content */
.bakedbot-homepage-main {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  position: relative;
  z-index: 1;
  height: auto;
  min-height: auto;
  overflow: visible;
}

/* Desktop/Tablet Main Container */
@media (min-width: 769px) {
  .bakedbot-homepage-main {
    padding: 1.5rem 2rem;
  }
}

@media (min-width: 1024px) {
  .bakedbot-homepage-main {
    padding: 2rem;
    max-width: 1400px;
  }
}

/* Hide view all categories button on desktop - show all categories */
@media (min-width: 769px) {
  .bakedbot-view-all-categories-btn {
    display: none;
  }
}

/* Hero Section */
.bakedbot-hero-section {
  margin-bottom: 3rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 1rem;
  overflow: hidden;
  color: white;
}

.bakedbot-hero-content {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 2rem;
  padding: 3rem 2rem;
}

.bakedbot-hero-text {
  max-width: 500px;
}

.bakedbot-hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.bakedbot-hero-subtitle {
  font-size: 1.125rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  line-height: 1.5;
}

.bakedbot-hero-cta {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: white;
  color: var(--primary-color);
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.bakedbot-hero-cta:hover {
  transform: translateY(-2px);
}

.bakedbot-hero-image img {
  max-width: 300px;
  height: auto;
  border-radius: 0.5rem;
}

/* Lucky's Hero Styles */
.bakedbot-luckys-hero {
  position: relative;
  margin-bottom: 2rem;
  border-radius: 0;
  padding: 0;
  overflow: hidden;
  height: 400px;
  background: none;
}

.bakedbot-hero-banner {
  position: relative;
  width: 100%;
  height: 100%;
}

.bakedbot-hero-background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.bakedbot-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.bakedbot-luckys-hero .bakedbot-hero-content {
  text-align: center;
  color: white;
  max-width: 600px;
  padding: 2rem;
  display: block;
}

.bakedbot-luckys-hero .bakedbot-hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.bakedbot-luckys-hero .bakedbot-hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.bakedbot-hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.bakedbot-hero-cta.bakedbot-primary-cta {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bakedbot-hero-cta.bakedbot-primary-cta:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.bakedbot-hero-cta.bakedbot-secondary-cta {
  background: transparent;
  color: white;
  border: 2px solid white;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bakedbot-hero-cta.bakedbot-secondary-cta:hover {
  background: white;
  color: var(--primary-color);
  transform: translateY(-2px);
}

/* Section Headers */
.bakedbot-section-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 1rem !important;
  width: 100% !important;
}

.bakedbot-section-header h4 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: var(--text-color) !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex-grow: 1 !important;
}

/* Desktop/Tablet Section Headers */
@media (min-width: 769px) {
  .bakedbot-section-header h4 {
    font-size: 1.5rem !important;
  }

  .bakedbot-section-header {
    margin-bottom: 1.5rem !important;
  }
}

@media (min-width: 1024px) {
  .bakedbot-section-header h4 {
    font-size: 1.75rem !important;
  }

  .bakedbot-section-header {
    margin-bottom: 2rem !important;
  }
}

.bakedbot-section-icon {
  color: var(--secondary-color);
}

.bakedbot-view-all-btn {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  background: transparent !important;
  color: var(--primary-color) !important;
  border: none !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
  transition: all 0.2s !important;
  white-space: nowrap !important;
}

.bakedbot-view-all-btn:hover {
  background: var(--primary-color);
  color: white;
}

/* Categories Section */
.bakedbot-categories-section {
  margin-bottom: 3rem;
}

.bakedbot-luckys-categories .bakedbot-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.bakedbot-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.bakedbot-category-card {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow);
}

.bakedbot-luckys-category-card {
  background: #F8F9FA;
  border: none;
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.bakedbot-luckys-category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.bakedbot-category-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.bakedbot-luckys-category-card .bakedbot-category-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: transparent;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 0.75rem auto;
  font-size: 2rem;
  color: inherit;
}

.bakedbot-category-icon {
  width: 3rem;
  height: 3rem;
  background: var(--hover-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1.25rem;
  color: var(--primary-color);
}

.bakedbot-luckys-category-card .bakedbot-category-name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #333;
  text-transform: capitalize;
}

/* Modern Categories Design */
.bakedbot-modern-categories {
  margin-bottom: 2rem;
}

/* Desktop/Tablet Sections */
@media (min-width: 769px) {
  .bakedbot-modern-categories,
  .bakedbot-featured-section,
  .bakedbot-category-products-section,
  .bakedbot-staff-picks-section {
    margin-bottom: 3rem;
  }
}

.bakedbot-categories-mobile-grid {
  display: flex;
  overflow-x: auto;
  gap: 2rem;
  margin-bottom: 1rem;
  padding: 0 1rem;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.bakedbot-categories-mobile-grid::-webkit-scrollbar {
  display: none;
}

/* Desktop/Tablet Categories */
@media (min-width: 769px) {
  .bakedbot-categories-mobile-grid {
    padding: 0;
    justify-content: flex-start;
    gap: 2rem;
    flex-wrap: nowrap;
  }
}

.bakedbot-modern-category-card {
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  flex: 0 0 auto;
  width: 120px;
}

/* Desktop/Tablet Category Cards */
@media (min-width: 769px) {
  .bakedbot-modern-category-card {
    width: 140px;
  }
}

@media (min-width: 1024px) {
  .bakedbot-modern-category-card {
    width: 160px;
  }
}

.bakedbot-modern-category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.bakedbot-modern-category-card .bakedbot-category-icon {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem !important;
  color: white;
  transition: all 0.2s ease;
  border-radius: 12px 12px 0 0;
}

/* Desktop/Tablet Category Icons */
@media (min-width: 769px) {
  .bakedbot-modern-category-card .bakedbot-category-icon {
    height: 100px;
    font-size: 3rem !important;
  }
}

.bakedbot-modern-category-card:hover .bakedbot-category-icon {
  transform: scale(1.02);
}

.bakedbot-modern-category-card .bakedbot-category-name {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
  color: #1F2937;
  text-align: center;
  padding: 0.75rem 0.5rem;
  text-transform: capitalize;
  line-height: 1.2;
}

/* Desktop/Tablet Category Names */
@media (min-width: 769px) {
  .bakedbot-modern-category-card .bakedbot-category-name {
    font-size: 1rem;
    padding: 1rem;
  }
}

.bakedbot-view-all-categories-btn {
  width: 100%;
  padding: 1rem;
  background: #F3F4F6;
  border: 1px solid #E5E7EB;
  border-radius: 50px;
  color: #6B7280;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.bakedbot-view-all-categories-btn:hover {
  background: #E5E7EB;
  color: #374151;
  transform: translateY(-1px);
}

.bakedbot-category-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.bakedbot-category-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

/* Scrollable Containers */
.bakedbot-scrollable-container {
  position: relative;
  margin-bottom: 3rem;
}

/* Carousel Arrow Buttons */
.bakedbot-carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: var(--background-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
  z-index: 10;
  box-shadow: var(--shadow);
}

.bakedbot-carousel-arrow:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-50%) scale(1.05);
  box-shadow: var(--shadow-md);
}

.bakedbot-carousel-arrow-left {
  left: -24px;
}

.bakedbot-carousel-arrow-right {
  right: -24px;
}

.bakedbot-products-scroll {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  padding-bottom: 1rem;
  -webkit-overflow-scrolling: touch;
}

.bakedbot-products-scroll::-webkit-scrollbar {
  height: 6px;
}

.bakedbot-products-scroll::-webkit-scrollbar-track {
  background: var(--hover-color);
  border-radius: 3px;
}

.bakedbot-products-scroll::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.bakedbot-products-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.bakedbot-featured-scroll .bakedbot-product-card {
  min-width: 280px;
  flex-shrink: 0;
}

.bakedbot-category-scroll .bakedbot-product-card {
  min-width: 250px;
  flex-shrink: 0;
}

/* Brands Scroll */
.bakedbot-brands-scroll {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  padding-bottom: 1rem;
  -webkit-overflow-scrolling: touch;
}

.bakedbot-brands-scroll::-webkit-scrollbar {
  height: 6px;
}

.bakedbot-brands-scroll::-webkit-scrollbar-track {
  background: var(--hover-color);
  border-radius: 3px;
}

.bakedbot-brands-scroll::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.bakedbot-brands-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.bakedbot-brands-scroll .bakedbot-brand-card {
  min-width: 150px;
  flex-shrink: 0;
}

/* Categories Scroll */
.bakedbot-categories-scroll {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  padding-bottom: 1rem;
  -webkit-overflow-scrolling: touch;
}

.bakedbot-categories-scroll::-webkit-scrollbar {
  height: 6px;
}

.bakedbot-categories-scroll::-webkit-scrollbar-track {
  background: var(--hover-color);
  border-radius: 3px;
}

.bakedbot-categories-scroll::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.bakedbot-categories-scroll::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.bakedbot-categories-scroll .bakedbot-category-card {
  min-width: 200px;
  flex-shrink: 0;
}

/* Legacy Grid Support (if needed) */
.bakedbot-products-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.bakedbot-featured-grid {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

.bakedbot-products-grid:not(.bakedbot-featured-grid) {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Product Cards */
.bakedbot-product-card {
  background: var(--background-color);
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 380px; /* Ensure consistent minimum height */
  position: relative;
}

.bakedbot-product-card-link {
  display: block;
  text-decoration: none !important;
  color: inherit !important;
  cursor: pointer;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.bakedbot-product-card-link:hover,
.bakedbot-product-card-link:focus {
  text-decoration: none !important;
  color: inherit !important;
}

.bakedbot-product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary-color);
}

.bakedbot-product-card.size-small {
  max-width: 240px;
  min-width: 200px;
}

.bakedbot-product-card.size-medium {
  max-width: 280px;
  min-width: 220px;
}

.bakedbot-product-card.size-large {
  max-width: 320px;
  min-width: 240px;
}


.bakedbot-product-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-height: 120px; /* Reserve space for consistent content height */
}

.bakedbot-product-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.8em; /* 2 lines * 1.4 line-height */
}

.bakedbot-product-brand {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  min-height: 1.2em; /* Accommodate one line */
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.bakedbot-product-details {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0.25rem 0;
}

.bakedbot-product-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: 0.75rem;
  padding: 0.5rem;
  border-top: 1px solid var(--border-color);
}

.bakedbot-product-image {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background: var(--hover-color);
}

.bakedbot-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;
}

.bakedbot-product-card:hover .bakedbot-product-image img {
  transform: scale(1.05);
}

.bakedbot-product-badges {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.bakedbot-product-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.125rem;
  backdrop-filter: blur(4px);
  max-width: fit-content;
}

.bakedbot-product-badge.top-pick {
  background: rgba(251, 191, 36, 0.9);
  color: white !important;
  font-weight: 700;
}

.bakedbot-product-badge.featured {
  background: rgba(139, 92, 246, 0.9);
  color: white !important;
  font-weight: 600;
}

.bakedbot-product-badge.high-thc {
  background: rgba(239, 68, 68, 0.9);
  color: white !important;
}

.bakedbot-product-badge.high-cbd {
  background: rgba(34, 197, 94, 0.9);
  color: white !important;
}

.bakedbot-add-to-cart-btn {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.875rem;
}

.bakedbot-quantity-controls-inline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--hover-color);
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.bakedbot-quantity-btn-inline {
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  background: var(--primary-color);
  color: white !important;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.bakedbot-quantity-display-inline {
  font-weight: 600;
  color: var(--text-color);
  min-width: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
}

.bakedbot-variant-selector {
  margin: 0.5rem 0;
}

.bakedbot-variant-selector select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background: var(--background-color);
  color: var(--text-color);
  font-size: 0.875rem;
}

.bakedbot-variant-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Mobile-friendly Variant Selector */
.bakedbot-mobile-variant-selector {
  margin: 0.5rem 0 0 0;
}

.bakedbot-mobile-select {
  width: 100%;
  padding: 0.375rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid var(--border-color);
  background-color: var(--background-color);
  color: var(--text-color) !important;
  font-size: 0.75rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: 2rem;
}

.bakedbot-mobile-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.bakedbot-mobile-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  margin-top: auto;
  padding-top: 0.5rem;
  border-top: 1px solid var(--border-color);
}

.bakedbot-price-section {
  display: flex;
  align-items: center;
}

.bakedbot-product-price {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--primary-color);
  background: rgba(16, 185, 129, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  display: inline-block;
}

.bakedbot-action-buttons {
  display: flex;
  align-items: center;
}

.bakedbot-mobile-add-btn {
  padding: 0.375rem 0.75rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.bakedbot-mobile-add-btn:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-mobile-add-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bakedbot-quantity-controls-mobile {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background: var(--hover-color);
  border-radius: 0.25rem;
  padding: 0.125rem;
}

.bakedbot-quantity-btn-mobile {
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: var(--primary-color);
  color: white !important;
  border-radius: 0.125rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.625rem;
  transition: background-color 0.2s;
}

.bakedbot-quantity-btn-mobile:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-quantity-display-mobile {
  font-weight: 600;
  color: var(--text-color);
  min-width: 1rem;
  text-align: center;
  font-size: 0.75rem;
}

/* Scrollable sections mobile optimization */
.bakedbot-mobile-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.bakedbot-mobile-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Category cards mobile optimization */
.bakedbot-mobile-category-card {
  min-width: 140px;
  padding: 0.75rem;
}

.bakedbot-mobile-category-card .bakedbot-category-name {
  font-size: 0.875rem;
  margin: 0.5rem 0 0.25rem 0;
}

.bakedbot-mobile-category-card .bakedbot-category-count {
  font-size: 0.75rem;
  margin: 0;
}

/* Hero section mobile optimization */
.bakedbot-mobile-hero {
  padding: 2rem 1rem;
}

.bakedbot-mobile-hero .bakedbot-hero-content {
  text-align: center;
  max-width: 100%;
}

.bakedbot-mobile-hero .bakedbot-hero-title {
  font-size: 2rem !important;
  margin-bottom: 1rem !important;
}

.bakedbot-mobile-hero .bakedbot-hero-subtitle {
  font-size: 1rem !important;
  margin-bottom: 1.5rem !important;
}

.bakedbot-mobile-cta {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border-radius: 0.5rem;
}

/* Products grid mobile optimization */
.bakedbot-products-scroll.bakedbot-mobile-scroll {
  gap: 0.75rem;
}

/* Mobile modal optimizations */
.bakedbot-mobile-modal .bakedbot-modal-content {
  width: 95vw;
  max-width: 95vw;
  margin: 1rem;
  max-height: 90vh;
  overflow-y: auto;
}

.bakedbot-mobile-modal-content {
  border-radius: 0.75rem;
}

.bakedbot-mobile-modal-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--background-color);
  z-index: 10;
}

.bakedbot-mobile-close-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  background: var(--hover-color);
  border: none;
  color: var(--text-color) !important;
}

/* Cart sidebar mobile optimization */
.bakedbot-mobile-cart-sidebar {
  width: 100vw;
  max-width: 100vw;
}

.bakedbot-mobile-sidebar-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.bakedbot-mobile-sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Brands Section */
.bakedbot-brands-section {
  margin-bottom: 3rem;
}

/* Legacy support for brands grid */
.bakedbot-brands-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.bakedbot-brand-card {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow);
}

.bakedbot-brand-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.bakedbot-brand-logo {
  width: 3rem;
  height: 3rem;
  background: var(--primary-color);
  color: white !important;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
  font-size: 1.25rem;
  font-weight: 700;
}

.bakedbot-brand-name {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

/* Floating Cart */
.bakedbot-floating-cart {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
}

.bakedbot-cart-summary {
  background: var(--primary-color);
  color: white !important;
  padding: 1rem 1.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  gap: 1rem;
  min-width: 250px;
}

.bakedbot-cart-info {
  flex: 1;
  font-weight: 500;
}

.bakedbot-checkout-btn {
  padding: 0.5rem 1rem;
  background: white;
  color: var(--primary-color) !important;
  border: none;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.bakedbot-checkout-btn:hover {
  transform: scale(1.05);
}

/* Responsive Design */


@media (max-width: 768px) {
  .bakedbot-header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .bakedbot-header-center {
    order: 3;
    max-width: none;
  }
  
  .bakedbot-hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    padding: 1.5rem 1rem;
  }
  
  .bakedbot-hero-title {
    font-size: 1.875rem;
  }
  
  .bakedbot-luckys-hero {
    height: 300px;
  }

  .bakedbot-luckys-hero .bakedbot-hero-title {
    font-size: 2.5rem !important;
  }

  .bakedbot-luckys-hero .bakedbot-hero-subtitle {
    font-size: 1.125rem !important;
  }

  .bakedbot-hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .bakedbot-hero-cta.bakedbot-primary-cta,
  .bakedbot-hero-cta.bakedbot-secondary-cta {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }

  .bakedbot-categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .bakedbot-luckys-categories .bakedbot-categories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .bakedbot-luckys-category-card {
    min-height: 100px;
    padding: 1rem;
  }

  .bakedbot-luckys-category-card .bakedbot-category-icon {
    font-size: 1.75rem;
    margin-bottom: 0.5rem;
  }

  .bakedbot-luckys-category-card .bakedbot-category-name {
    font-size: 0.875rem;
  }

  /* Modern Categories Mobile */
  .bakedbot-categories-mobile-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 0;
    overflow-x: visible;
  }

  /* Large mobile - 3 columns up to 768px */
  @media (min-width: 480px) and (max-width: 768px) {
    .bakedbot-categories-mobile-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .bakedbot-modern-category-card {
    width: auto;
  }

  .bakedbot-modern-category-card .bakedbot-category-icon {
    font-size: 2rem !important;
  }

  .bakedbot-modern-category-card .bakedbot-category-name {
    font-size: 0.875rem;
    padding: 0.75rem;
  }

  .bakedbot-view-all-categories-btn {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .bakedbot-products-grid,
  .bakedbot-featured-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.75rem;
  }

  .bakedbot-carousel-arrow {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .bakedbot-featured-scroll .bakedbot-product-card,
  .bakedbot-category-scroll .bakedbot-product-card {
    min-width: 180px;
    max-width: 220px;
  }

  .bakedbot-brands-scroll .bakedbot-brand-card {
    min-width: 120px;
  }

  .bakedbot-categories-scroll .bakedbot-category-card {
    min-width: 120px;
    padding: 0.75rem;
  }
  

  
  .bakedbot-cart-footer-row {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .bakedbot-cart-total-compact {
    text-align: center;
    font-size: 1rem;
  }

  .bakedbot-checkout-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .bakedbot-form-row {
    grid-template-columns: 1fr;
  }

  .bakedbot-checkout-form input {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.75rem;
  }

  .bakedbot-order-success {
    padding: 1.5rem;
    margin: 1rem;
  }

  .bakedbot-success-icon {
    width: 50px;
    height: 50px;
  }

  .bakedbot-order-success h2 {
    font-size: 1.25rem;
  }
  
  .bakedbot-floating-cart {
    display: none; /* Hide on mobile in favor of cart button */
  }
  
  .bakedbot-section-header {
    margin-bottom: 1rem;
  }

  .bakedbot-section-header h4 {
    font-size: 1.125rem !important;
  }

  .bakedbot-section-header .bakedbot-view-all-btn {
    margin-left: auto !important;
    font-size: 0.75rem !important;
    padding: 0.375rem 0.75rem !important;
  }

  .bakedbot-headless-homepage.enhanced {
    padding: 0;
  }
}

@media (max-width: 480px) {
  .bakedbot-homepage-main {
    padding: 0.75rem;
  }
  
  .bakedbot-section-header {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 0.5rem !important;
    margin-bottom: 0.75rem !important;
  }
  
  .bakedbot-view-all-btn {
    align-self: auto !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .bakedbot-featured-scroll .bakedbot-product-card,
  .bakedbot-category-scroll .bakedbot-product-card {
    min-width: 140px;
    max-width: 160px;
  }

  .bakedbot-brands-scroll .bakedbot-brand-card {
    min-width: 100px;
  }

  .bakedbot-categories-scroll .bakedbot-category-card {
    min-width: 100px;
    padding: 0.625rem;
  }

  .bakedbot-category-card .bakedbot-category-name {
    font-size: 0.8125rem;
  }

  .bakedbot-category-card .bakedbot-category-count {
    font-size: 0.6875rem;
  }
  
  .bakedbot-products-grid,
  .bakedbot-featured-grid,
  .bakedbot-categories-grid,
  .bakedbot-brands-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.625rem;
  }

  .bakedbot-carousel-arrow {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .bakedbot-hero-section {
    padding: 1.5rem 0.75rem;
  }

  .bakedbot-hero-title {
    font-size: 1.625rem;
  }

  .bakedbot-hero-subtitle {
    font-size: 0.9375rem;
  }

  .bakedbot-hero-cta {
    padding: 0.625rem 1.25rem;
    font-size: 0.9375rem;
  }
}

/* Carousel Section with Swiper */
.bakedbot-carousel-section {
  margin-bottom: 1rem;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.bakedbot-custom-swiper {
  width: 100%;
  aspect-ratio: 3 / 1; /* 1200x400 ratio */
  border-radius: 12px;
}

.bakedbot-carousel-slide {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.bakedbot-carousel-slide .bakedbot-slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 12px;
}



/* Custom Swiper Pagination */
.bakedbot-custom-swiper .swiper-pagination {
  bottom: 1rem !important;
}

.bakedbot-custom-bullet {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background: rgba(255, 255, 255, 0.5) !important;
  cursor: pointer !important;
  transition: background 0.3s ease !important;
  margin: 0 4px !important;
}

.bakedbot-custom-bullet-active {
  background: white !important;
}

/* Promotional Section */
.bakedbot-promotional-section {
  background: #F8E8E8;
  border-radius: 12px;
  margin-bottom: 2rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bakedbot-promo-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: #F8E8E8;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  width: 100%;
  text-align: left;
}

.bakedbot-promo-header:hover {
  background: #F0D8D8;
}

.bakedbot-promo-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #8B4513;
  flex: 1;
}

.bakedbot-promo-toggle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #8B4513;
  transition: transform 0.2s ease;
}

.bakedbot-promo-toggle.expanded {
  transform: rotate(180deg);
}

.bakedbot-promo-content {
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid #E5E7EB;
}

.bakedbot-promo-item {
  padding: 0.75rem 0;
  border-bottom: 1px solid #F3F4F6;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #4B5563;
}

.bakedbot-promo-item:last-child {
  border-bottom: none;
}

.bakedbot-promo-item strong {
  color: #8B4513;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .bakedbot-custom-swiper {
    border-radius: 8px;
  }
  
  .bakedbot-carousel-slide .bakedbot-slide-image {
    border-radius: 8px;
  }
  
  .bakedbot-promo-title {
    font-size: 1rem;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .bakedbot-promo-title {
    font-size: 0.9rem;
    line-height: 1.3;
  }
}

/* VIP Signup Section */
.bakedbot-vip-signup {
  background: var(--hover-color);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.bakedbot-vip-signup h3 {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: var(--primary-color) !important;
  margin: 0 0 0.5rem 0 !important;
}

.bakedbot-vip-signup p {
  margin: 0 0 1.5rem 0 !important;
  color: var(--text-secondary);
  max-width: 500px;
  margin-left: auto !important;
  margin-right: auto !important;
}

.bakedbot-vip-form {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  max-width: 400px;
  margin: 0 auto;
}

.bakedbot-vip-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
}

.bakedbot-vip-button {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
}

/* Announcement Bar */
.bakedbot-announcement-bar {
  background: var(--primary-color);
  color: white !important;
  padding: 0.75rem 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Dynamic Announcements Section */
.bakedbot-announcements-section {
  margin-bottom: 2rem;
}

.bakedbot-announcement-item {
  background: #F8F9FA;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #E5E7EB;
}

.bakedbot-announcement-item h5 {
  margin: 0 0 0.5rem 0 !important;
  color: #1F2937 !important;
}

.bakedbot-announcement-item p {
  margin: 0 !important;
  color: #6B7280 !important;
  line-height: 1.5 !important;
}

.bakedbot-announcement-link {
  display: inline-block;
  margin-top: 0.5rem;
  color: var(--primary-color) !important;
  font-weight: 600 !important;
  text-decoration: none !important;
}

.bakedbot-promo-learn-more {
  margin-left: 0.5rem;
  color: var(--primary-color) !important;
  font-weight: 500;
}

