import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  memo,
} from "react";
import { useCart } from "../ChatWidget/CartContext";
import {
  fetchGroupedProducts,
  fetchFilterOptions,
  fetchTopPicks,
  GroupedProduct,
  ProductVariant,
  FilterOptions,
  TopPicksResponse,
  bakedBotApiRequest,
  fetchMenuSettings,
  MenuSetting,
} from "../../utils/api";
import useAuth from "../../hooks/useAuth";
import {
  FaShoppingCart,
  FaSearch,
  FaArrowRight,
  FaTag,
  FaStar,
  FaFire,
  FaLeaf,
  FaPlus,
  FaMinus,
  FaTimes,
  FaCheck,
  FaChevronLeft,
  FaChevronRight,
} from "react-icons/fa";
import SharedHeader from "../../components/SharedHeader";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/autoplay";
import "./headless-homepage.css";

interface HeadlessHomepageProps {
  apiKey?: string;
  enableCart?: boolean;
  theme?: "light" | "dark" | "custom";
  primaryColor?: string;
  secondaryColor?: string;
  onNavigateToMenu?: (filters?: {
    category?: string;
    subcategory?: string;
    brand?: string;
  }) => void;
  onNavigateToProduct?: (product: GroupedProduct) => void;
  containerId?: string;
  featuredProductsCount?: number;
  popularCategoriesCount?: number;
  showPromotion?: boolean;
  promotionTitle?: string;
  promotionSubtitle?: string;
  promotionImageUrl?: string;
  menuPageUrl?: string;
  productPageUrl?: string;
}

interface CategorySection {
  title: string;
  products: GroupedProduct[];
  category?: string;
  subcategory?: string;
  viewAllLabel?: string;
}

interface CheckoutFormData {
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  birth_date: string;
  couponCode: string;
}

// Move ProductCard outside the main component and memoize it
const ProductCard: React.FC<{
  product: GroupedProduct;
  size?: "small" | "medium" | "large";
  selectedVariant?: ProductVariant;
  isInCart?: boolean;
  cartQuantity?: number;
  onVariantSelect: (metaSku: string, variant: ProductVariant) => void;
  onAddToCart: (product: GroupedProduct) => void;
  onUpdateQuantity: (productId: string, change: number) => void;
  onNavigateToProduct?: (product: GroupedProduct) => void;
  productPageUrl?: string;
}> = memo(
  ({
    product,
    size = "medium",
    selectedVariant,
    isInCart,
    cartQuantity,
    onVariantSelect,
    onAddToCart,
    onUpdateQuantity,
    onNavigateToProduct,
    productPageUrl = "/product",
  }) => {
    const displayPrice = selectedVariant?.latest_price || product.base_price;

    const handleVariantChange = useCallback(
      (e: React.ChangeEvent<HTMLSelectElement>) => {
        const variant = product.variants.find((v) => v.id === e.target.value);
        if (variant) onVariantSelect(product.meta_sku, variant);
      },
      [product.variants, product.meta_sku, onVariantSelect]
    );

    const handleAddToCart = useCallback(() => {
      onAddToCart(product);
    }, [product, onAddToCart]);

    const handleQuantityDecrease = useCallback(() => {
      if (selectedVariant)
        onUpdateQuantity(String(selectedVariant.product_id), -1);
    }, [selectedVariant, onUpdateQuantity]);

    const handleQuantityIncrease = useCallback(() => {
      if (selectedVariant)
        onUpdateQuantity(String(selectedVariant.product_id), 1);
    }, [selectedVariant, onUpdateQuantity]);

    const handleCardClick = useCallback(() => {
      onNavigateToProduct?.(product);
    }, [product, onNavigateToProduct]);

    return (
      <div
        className={`bakedbot-product-card size-${size} bakedbot-mobile-optimized`}
      >
        <a
          href={`${productPageUrl}?id=${product.meta_sku}`}
          className="bakedbot-product-card-link"
          aria-label={`View details for ${product.product_name}`}
        >
          <div className="bakedbot-product-image">
            <img
              src={product.image_url}
              alt={product.product_name}
              loading="lazy"
              decoding="async"
            />

            <div className="bakedbot-product-badges">
              {product.is_top_pick && (
                <div className="bakedbot-product-badge top-pick">
                  <FaStar /> Top Pick
                </div>
              )}
              {product.is_featured && !product.is_top_pick && (
                <div className="bakedbot-product-badge featured">
                  <FaTag /> Featured
                </div>
              )}
              {Number(product.percentage_thc) > 20 ? (
                <div className="bakedbot-product-badge high-thc">
                  <FaFire /> High THC
                </div>
              ) : null}
              {Number(product.percentage_cbd) > 10 ? (
                <div className="bakedbot-product-badge high-cbd">
                  <FaLeaf /> High CBD
                </div>
              ) : null}
            </div>
          </div>
          <div className="bakedbot-product-info">
            <p className="bakedbot-product-name">{product.product_name}</p>
            <p className="bakedbot-product-brand">
              {product.brand_name || "Premium Brand"}
            </p>
            <div className="bakedbot-product-details">
              <span>THC: {product.percentage_thc || 0}%</span>
              <span>CBD: {product.percentage_cbd || 0}%</span>
            </div>
          </div>
        </a>

        {/* Mobile-friendly Variant Selector */}
        {product.variants && product.variants.length > 1 && (
          <div className="bakedbot-variant-selector bakedbot-mobile-variant-selector">
            <select
              value={selectedVariant?.id || ""}
              onChange={handleVariantChange}
              className="bakedbot-mobile-select"
              aria-label="Select product variant"
            >
              {product.variants.map((variant) => (
                <option key={variant.id} value={variant.id}>
                  {variant.display_weight} - ${variant.latest_price}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="bakedbot-product-actions bakedbot-mobile-actions">
          <div className="bakedbot-price-section">
            <span className="bakedbot-product-price">${displayPrice}</span>
          </div>
          <div className="bakedbot-action-buttons">
            {isInCart ? (
              <div className="bakedbot-quantity-controls-mobile">
                <button
                  onClick={handleQuantityDecrease}
                  className="bakedbot-quantity-btn-mobile"
                  aria-label="Decrease quantity"
                >
                  <FaMinus />
                </button>
                <span
                  className="bakedbot-quantity-display-mobile"
                  aria-label={`Quantity: ${cartQuantity}`}
                >
                  {cartQuantity}
                </span>
                <button
                  onClick={handleQuantityIncrease}
                  className="bakedbot-quantity-btn-mobile"
                  aria-label="Increase quantity"
                >
                  <FaPlus />
                </button>
              </div>
            ) : (
              <button
                className="bakedbot-add-to-cart-btn bakedbot-mobile-add-btn"
                onClick={handleAddToCart}
                disabled={!selectedVariant}
                aria-label={`Add ${product.product_name} to cart`}
              >
                Add to Cart
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }
);

ProductCard.displayName = "ProductCard";

const getCategoryEmoji = (category: string): string => {
  const lowerCategory = category.toLowerCase();
  if (lowerCategory.includes("flower")) return "🌸";
  if (lowerCategory.includes("edible")) return "🍫";
  if (lowerCategory.includes("concentrate")) return "💎";
  if (lowerCategory.includes("wellness")) return "🧘";
  if (lowerCategory.includes("accessories")) return "🔥";
  if (lowerCategory.includes("preroll") || lowerCategory.includes("pre-roll"))
    return "🚬";
  if (lowerCategory.includes("vape")) return "💨";
  if (lowerCategory.includes("tincture")) return "💧";
  if (lowerCategory.includes("topical")) return "🧴";
  if (lowerCategory.includes("other")) return "🛍️";
  return "🌿"; // Default cannabis leaf emoji
};

const getCategoryColor = (category: string): string => {
  const lowerCategory = category.toLowerCase();
  if (lowerCategory.includes("flower")) return "#10B981"; // Green
  if (lowerCategory.includes("preroll") || lowerCategory.includes("pre-roll"))
    return "#8B5CF6"; // Purple
  if (lowerCategory.includes("vape")) return "#3B82F6"; // Blue
  if (lowerCategory.includes("concentrate")) return "#F59E0B"; // Orange/Amber
  if (lowerCategory.includes("edible")) return "#EF4444"; // Red
  if (lowerCategory.includes("tincture")) return "#06B6D4"; // Cyan
  if (lowerCategory.includes("accessories")) return "#6B7280"; // Gray
  return "#10B981"; // Default green
};

const getCategoryImage = (category: string): string => {
  const lowerCategory = category.toLowerCase();
  if (lowerCategory.includes("flower"))
    return "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop";
  if (lowerCategory.includes("preroll") || lowerCategory.includes("pre-roll"))
    return "https://images.unsplash.com/photo-1544966503-7cc5ac882d5c?w=300&h=200&fit=crop";
  if (lowerCategory.includes("vape"))
    return "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop";
  if (lowerCategory.includes("concentrate"))
    return "https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=300&h=200&fit=crop";
  if (lowerCategory.includes("edible"))
    return "https://images.unsplash.com/photo-1571835491394-3a5bcb5fc3ad?w=300&h=200&fit=crop";
  if (lowerCategory.includes("tincture"))
    return "https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=300&h=200&fit=crop";
  return "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop"; // Default cannabis image
};

export const HeadlessHomepage: React.FC<HeadlessHomepageProps> = ({
  apiKey,
  enableCart = true,
  theme = "light",
  primaryColor = "#065f46",
  secondaryColor = "#10b981",
  onNavigateToMenu,
  onNavigateToProduct,
  containerId,
  featuredProductsCount = 8,
  popularCategoriesCount = 6,
  showPromotion = true,
  promotionTitle = "Premium Cannabis Products",
  promotionSubtitle = "Discover our curated selection of the finest cannabis products",
  promotionImageUrl,
  menuPageUrl = "/dispensary-menu",
  productPageUrl = "/product",
}) => {
  // State management
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    brands: [],
    categories: [],
    subcategories: [],
  });
  const [categoryProducts, setCategoryProducts] = useState<
    Record<string, GroupedProduct[]>
  >({});
  const [featuredProducts, setFeaturedProducts] = useState<GroupedProduct[]>(
    []
  );
  const [topPickProducts, setTopPickProducts] = useState<GroupedProduct[]>([]);
  const [selectedVariants, setSelectedVariants] = useState<
    Record<string, ProductVariant>
  >({});
  const [showCartSidebar, setShowCartSidebar] = useState(false);
  const [showCheckout, setShowCheckout] = useState(false);
  const [showOrderConfirmation, setShowOrderConfirmation] = useState(false);
  const [checkoutForm, setCheckoutForm] = useState<CheckoutFormData>({
    email: "",
    phone: "",
    firstName: "",
    lastName: "",
    birth_date: "",
    couponCode: "",
  });
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [showAllCategories, setShowAllCategories] = useState(false);

  // Mobile-specific state
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);

  // Promotional section state
  const [isPromoExpanded, setIsPromoExpanded] = useState(false);

  // VIP signup form state
  const [vipFormData, setVipFormData] = useState({
    email: "",
    phone: "",
    firstName: "",
  });
  const [vipSubmitting, setVipSubmitting] = useState(false);
  const [vipSubmitted, setVipSubmitted] = useState(false);

  // Menu settings state
  const [carouselSlides, setCarouselSlides] = useState<MenuSetting[]>([]);
  const [promoContent, setPromoContent] = useState<MenuSetting[]>([]);
  const [announcements, setAnnouncements] = useState<MenuSetting[]>([]);

  // Refs for scrollable containers
  const featuredScrollRef = useRef<HTMLDivElement>(null);
  const categoryScrollRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const brandsScrollRef = useRef<HTMLDivElement>(null);
  const categoriesScrollRef = useRef<HTMLDivElement>(null);

  // State for arrow visibility
  const [scrollStates, setScrollStates] = useState<
    Record<
      string,
      {
        canScrollLeft: boolean;
        canScrollRight: boolean;
      }
    >
  >({});

  const { cart, addToCart, updateQuantity, removeFromCart, handleCheckout } =
    useCart();

  // Make auth optional for WordPress/standalone usage
  let user: any = null;
  try {
    const authHook = useAuth();
    user = authHook?.user || null;
  } catch (error) {
    console.log("BakedBot Debug: Auth hook not available in this context");
  }

  // Load filter options and categories
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      // Load filter options first
      const options = await fetchFilterOptions();
      setFilterOptions(options);

      // Load menu settings (carousel slides, promo content, announcements)
      try {
        const carouselData = await fetchMenuSettings("carousel_slide");
        const promoData = await fetchMenuSettings("promo_content");
        const announcementData = await fetchMenuSettings("announcement");

        setCarouselSlides(carouselData);
        setPromoContent(promoData);
        setAnnouncements(announcementData);
      } catch (error) {
        console.error("Error loading menu settings:", error);
        // Continue with empty arrays if menu settings fail
      }

      // Initialize selected variants object
      const initialVariants: Record<string, ProductVariant> = {};

      // Load featured products using regular endpoint with featured sort
      try {
        const featuredResponse = await fetchGroupedProducts(1, {
          sort: "featured",
          direction: "desc",
          per_page: featuredProductsCount,
        });
        setFeaturedProducts(featuredResponse.products);

        // Initialize selected variants for featured products
        featuredResponse.products.forEach((product) => {
          if (product.variants && product.variants.length > 0) {
            initialVariants[product.meta_sku] = product.variants[0];
          }
        });

        // Load top picks using the dedicated endpoint
        const topPicksResponse = await fetchTopPicks(6);
        setTopPickProducts(topPicksResponse.top_picks || []);

        // Initialize selected variants for top picks
        topPicksResponse.top_picks.forEach((product) => {
          if (
            product.variants &&
            product.variants.length > 0 &&
            !initialVariants[product.meta_sku]
          ) {
            initialVariants[product.meta_sku] = product.variants[0];
          }
        });

        setSelectedVariants(initialVariants);
      } catch (error) {
        console.error("Error loading featured/top pick products:", error);
      }

      // Load products for each major category
      const categoryData: Record<string, GroupedProduct[]> = {};
      const topCategories = options.categories.slice(0, popularCategoriesCount);

      for (const category of topCategories) {
        try {
          const categoryResponse = await fetchGroupedProducts(1, {
            category: category,
            per_page: 6, // Show 6 products per category
          });
          categoryData[category] = categoryResponse.products;

          // Add variants for category products
          categoryResponse.products.forEach((product) => {
            if (
              product.variants &&
              product.variants.length > 0 &&
              !initialVariants[product.meta_sku]
            ) {
              initialVariants[product.meta_sku] = product.variants[0];
            }
          });
        } catch (error) {
          console.error(
            `Error loading products for category ${category}:`,
            error
          );
          categoryData[category] = [];
        }
      }

      setCategoryProducts(categoryData);
      setSelectedVariants(initialVariants);
    } catch (error) {
      console.error("Error loading homepage data:", error);
    } finally {
      setLoading(false);
    }
  }, [featuredProductsCount, popularCategoriesCount]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Mobile detection
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Auto-show all categories on desktop
  useEffect(() => {
    const checkShowAllCategories = () => {
      const isDesktop = window.innerWidth > 768;
      setShowAllCategories(isDesktop);
    };

    checkShowAllCategories();
    window.addEventListener("resize", checkShowAllCategories);
    return () => window.removeEventListener("resize", checkShowAllCategories);
  }, []);

  // Initialize scroll states when data loads
  useEffect(() => {
    const initializeScrollStates = () => {
      // Featured products
      if (featuredScrollRef.current) {
        updateScrollState(featuredScrollRef.current, "featured");
      }

      // Categories
      if (categoriesScrollRef.current) {
        updateScrollState(categoriesScrollRef.current, "categories");
      }

      // Category products
      Object.entries(categoryScrollRefs.current).forEach(([category, ref]) => {
        if (ref) {
          updateScrollState(ref, `category-${category}`);
        }
      });

      // Brands
      if (brandsScrollRef.current) {
        updateScrollState(brandsScrollRef.current, "brands");
      }
    };

    // Initialize after a short delay to ensure content is rendered
    if (
      featuredProducts.length > 0 ||
      topPickProducts.length > 0 ||
      Object.keys(categoryProducts).length > 0
    ) {
      setTimeout(initializeScrollStates, 100);
    }
  }, [
    featuredProducts,
    topPickProducts,
    categoryProducts,
    filterOptions.brands,
  ]);

  // Auto-fill user data in checkout form
  useEffect(() => {
    if (user && showCheckout) {
      const nameParts = (user.displayName || "").split(" ");
      setCheckoutForm((prev) => ({
        ...prev,
        email: prev.email || user.email || "",
        firstName: prev.firstName || nameParts[0] || "",
        lastName: prev.lastName || nameParts.slice(1).join(" ") || "",
      }));
    }
  }, [user, showCheckout]);

  // Check scroll position and update arrow visibility with throttling
  const updateScrollState = useCallback(
    (element: HTMLDivElement, key: string) => {
      const canScrollLeft = element.scrollLeft > 0;
      const canScrollRight =
        element.scrollLeft < element.scrollWidth - element.clientWidth - 1; // -1 for rounding

      setScrollStates((prev) => {
        // Only update if values actually changed
        const current = prev[key];
        if (
          current?.canScrollLeft === canScrollLeft &&
          current?.canScrollRight === canScrollRight
        ) {
          return prev;
        }
        return {
          ...prev,
          [key]: { canScrollLeft, canScrollRight },
        };
      });
    },
    []
  );

  // Scroll functions for horizontal sections
  const scrollContainer = (
    ref: React.RefObject<HTMLDivElement>,
    direction: "left" | "right",
    key: string
  ) => {
    if (ref.current) {
      const scrollAmount = 320; // Width of roughly 1 product card + gap
      const currentScroll = ref.current.scrollLeft;
      const targetScroll =
        direction === "left"
          ? currentScroll - scrollAmount
          : currentScroll + scrollAmount;

      ref.current.scrollTo({
        left: targetScroll,
        behavior: "smooth",
      });

      // Update arrow visibility after scroll completes
      setTimeout(() => {
        if (ref.current) {
          updateScrollState(ref.current, key);
        }
      }, 300);
    }
  };

  const scrollFeatured = (direction: "left" | "right") => {
    scrollContainer(featuredScrollRef, direction, "featured");
  };

  const scrollCategory = (category: string, direction: "left" | "right") => {
    const ref = { current: categoryScrollRefs.current[category] };
    scrollContainer(ref, direction, `category-${category}`);
  };

  const scrollBrands = (direction: "left" | "right") => {
    scrollContainer(brandsScrollRef, direction, "brands");
  };

  const scrollCategories = (direction: "left" | "right") => {
    scrollContainer(categoriesScrollRef, direction, "categories");
  };

  // Touch gesture handlers for product sections (different from carousel)
  const handleProductTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
  };

  const handleProductTouchEnd = (
    e: React.TouchEvent,
    scrollFunction: (direction: "left" | "right") => void
  ) => {
    if (!touchStartX || !touchStartY) return;

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;

    const deltaX = touchStartX - touchEndX;
    const deltaY = touchStartY - touchEndY;

    // Only handle horizontal swipes (ignore vertical scrolling)
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        scrollFunction("right");
      } else {
        scrollFunction("left");
      }
    }

    setTouchStartX(null);
    setTouchStartY(null);
  };

  // Search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim() && onNavigateToMenu) {
      onNavigateToMenu(); // Navigate to menu with search
      // Note: The actual search would be handled by the HeadlessMenu component
    }
  };

  // Variant selection handler
  const handleVariantSelect = (metaSku: string, variant: ProductVariant) => {
    setSelectedVariants((prev) => ({
      ...prev,
      [metaSku]: variant,
    }));
  };

  // Checkout handler
  const handleCheckoutSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (Object.keys(cart).length === 0) return;

    setCheckoutLoading(true);
    try {
      const success = await handleCheckout({
        email: checkoutForm.email,
        phone: checkoutForm.phone,
        firstName: checkoutForm.firstName,
        lastName: checkoutForm.lastName,
        name: `${checkoutForm.firstName} ${checkoutForm.lastName}`.trim(),
        birth_date: checkoutForm.birth_date,
        couponCode: checkoutForm.couponCode,
      });

      if (success) {
        setShowCheckout(false);
        setShowOrderConfirmation(true);
        // Clear cart after successful order
        Object.keys(cart).forEach((productId) => removeFromCart(productId));
        // Reset form
        setCheckoutForm({
          email: "",
          phone: "",
          firstName: "",
          lastName: "",
          birth_date: "",
          couponCode: "",
        });
      } else {
        alert("Failed to place order. Please try again.");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      alert("An error occurred during checkout. Please try again.");
    } finally {
      setCheckoutLoading(false);
    }
  };

  // VIP signup handler
  const handleVipSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!vipFormData.email || !vipFormData.phone || !vipFormData.firstName) {
      alert("Please fill in all required fields");
      return;
    }

    setVipSubmitting(true);
    try {
      await bakedBotApiRequest("POST", "/public/products/users", {
        external_id: vipFormData.email,
        email: vipFormData.email,
        phone: vipFormData.phone,
        first_name: vipFormData.firstName,
        vip: true,
        data: {
          membership_date: new Date().toISOString(),
        },
      });

      setVipSubmitted(true);
      // Reset form
      setVipFormData({
        email: "",
        phone: "",
        firstName: "",
      });
    } catch (error) {
      console.error("VIP signup error:", error);
      alert("Failed to join VIP club. Please try again.");
    } finally {
      setVipSubmitting(false);
    }
  };

  // Add to cart handler
  const handleAddToCart = useCallback(
    (product: GroupedProduct) => {
      const selectedVariant = selectedVariants[product.meta_sku];
      if (!selectedVariant) return;

      // Convert grouped product + variant to individual product for cart
      const cartProduct = {
        id: selectedVariant.product_id,
        product_id: selectedVariant.product_id,
        cann_sku_id: selectedVariant.product_id,
        product_name: `${product.product_name} - ${selectedVariant.display_weight}`,
        brand_name: product.brand_name,
        category: product.category,
        image_url: product.image_url,
        latest_price: selectedVariant.latest_price,
        percentage_thc: product.percentage_thc ?? null,
        percentage_cbd: product.percentage_cbd ?? null,
        meta_sku: product.meta_sku,
        medical: selectedVariant.medical,
        recreational: selectedVariant.recreational,
        // Additional required fields
        brand_id: null,
        url: "",
        raw_product_name: product.product_name,
        raw_weight_string: selectedVariant.display_weight,
        display_weight: selectedVariant.display_weight,
        raw_product_category: product.category,
        raw_subcategory: product.subcategory || null,
        subcategory: product.subcategory || null,
        product_tags: null,
        mg_thc: null,
        mg_cbd: null,
        quantity_per_package: null,
        menu_provider: "",
        retailer_id: "",
        updated_at: "",
        price: selectedVariant.latest_price,
        description: product.description || "",
      };

      addToCart(cartProduct);
    },
    [selectedVariants, addToCart]
  );

  // Product navigation handler
  const handleProductClick = useCallback(
    (product: GroupedProduct) => {
      // Use the onNavigateToProduct prop if provided, otherwise navigate directly
      if (onNavigateToProduct) {
        onNavigateToProduct(product);
      } else {
        // Navigate to product details page using configurable URL
        window.location.href = `${productPageUrl}?id=${product.meta_sku}`;
      }
    },
    [onNavigateToProduct, productPageUrl]
  );

  // Navigation handlers with filters
  const handleNavigateToMenuWithFilter = useCallback(
    (filters?: { category?: string; subcategory?: string; brand?: string }) => {
      // Build URL with query parameters
      const baseUrl = menuPageUrl;
      const searchParams = new URLSearchParams();

      if (filters?.category) {
        searchParams.set("category", filters.category);
      }
      if (filters?.subcategory) {
        searchParams.set("subcategory", filters.subcategory);
      }
      if (filters?.brand) {
        searchParams.set("brand", filters.brand);
      }

      const url = searchParams.toString()
        ? `${baseUrl}?${searchParams.toString()}`
        : baseUrl;

      // Use window.location to navigate
      window.location.href = url;

      // Also call the callback if provided (for backward compatibility)
      if (onNavigateToMenu) {
        onNavigateToMenu(filters);
      }
    },
    [onNavigateToMenu, menuPageUrl]
  );

  // Cart calculations
  const cartCount = Object.values(cart).reduce(
    (sum, { quantity }) => sum + quantity,
    0
  );
  const cartTotal = Object.values(cart).reduce(
    (sum, { product, quantity }) => sum + product.latest_price * quantity,
    0
  );

  // Category sections for rendering - only show categories with products
  const categorySections: CategorySection[] = useMemo(() => {
    return Object.entries(categoryProducts)
      .filter(([category, products]) => products.length > 0) // Only include categories with products
      .map(([category, products]) => ({
        title: category,
        products,
        category,
        viewAllLabel: `View All ${category}`,
      }));
  }, [categoryProducts]);

  // Form validation
  const isCheckoutFormValid =
    checkoutForm.email.includes("@") &&
    checkoutForm.firstName.trim() &&
    checkoutForm.lastName.trim() &&
    checkoutForm.phone.trim() &&
    checkoutForm.birth_date.trim();

  // Memoize the style object to prevent re-renders
  const containerStyle = useMemo(
    () =>
      ({
        "--primary-color": primaryColor,
        "--secondary-color": secondaryColor,
      } as React.CSSProperties),
    [primaryColor, secondaryColor]
  );

  const handleHeaderSearch = (query: string) => {
    // Navigate to menu page with search query
    const url = `${menuPageUrl}?search=${encodeURIComponent(query)}`;
    window.location.href = url;

    // Also call the callback if provided (for backward compatibility)
    if (onNavigateToMenu) {
      onNavigateToMenu({ category: query }); // Re-using category for search for now
    }
  };

  if (loading) {
    return (
      <div className={`bakedbot-headless-homepage theme-${theme} loading`}>
        <div className="bakedbot-loading-spinner">
          Loading your cannabis marketplace...
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bakedbot-headless-homepage enhanced theme-${theme}`}
      style={containerStyle}
    >
      <SharedHeader
        cartCount={cartCount}
        onCartClick={() => setShowCartSidebar(true)}
        onSearch={handleHeaderSearch}
        enableCart={enableCart}
      />
      {/* Main Content */}
      <main className="bakedbot-homepage-main">
        {/* Dynamic Carousel Section */}
        {carouselSlides.length > 0 && (
          <section className="bakedbot-carousel-section">
            <Swiper
              modules={[Navigation, Pagination, Autoplay]}
              spaceBetween={0}
              slidesPerView={1}
              navigation={false}
              pagination={{
                clickable: true,
                bulletClass: "swiper-pagination-bullet bakedbot-custom-bullet",
                bulletActiveClass:
                  "swiper-pagination-bullet-active bakedbot-custom-bullet-active",
              }}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
              }}
              loop={carouselSlides.length > 1}
              className="bakedbot-custom-swiper"
            >
              {carouselSlides
                .sort((a, b) => a.order - b.order)
                .map((slide) => (
                  <SwiperSlide key={slide.id}>
                    <div className="bakedbot-carousel-slide">
                      {slide.link ? (
                        <a
                          href={slide.link}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <img
                            src={slide.image_url}
                            alt={slide.title || slide.description}
                            className="bakedbot-slide-image"
                          />
                        </a>
                      ) : (
                        <img
                          src={slide.image_url}
                          alt={slide.title || slide.description}
                          className="bakedbot-slide-image"
                        />
                      )}
                    </div>
                  </SwiperSlide>
                ))}
            </Swiper>
          </section>
        )}

        {/* Dynamic Promotional Section */}
        {promoContent.length > 0 && (
          <section className="bakedbot-promotional-section">
            <div
              className="bakedbot-promo-header"
              onClick={() => setIsPromoExpanded(!isPromoExpanded)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  setIsPromoExpanded(!isPromoExpanded);
                }
              }}
            >
              <span className="bakedbot-promo-title">
                {"Special Promotions"}
              </span>
              <span
                className={`bakedbot-promo-toggle ${
                  isPromoExpanded ? "expanded" : ""
                }`}
              >
                {isPromoExpanded ? "−" : "+"}
              </span>
            </div>

            {isPromoExpanded && (
              <div className="bakedbot-promo-content">
                {promoContent.map((promo) => (
                  <div key={promo.id} className="bakedbot-promo-item">
                    <strong>{promo.title}:</strong> {promo.description}
                    {promo.link && (
                      <a
                        href={promo.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bakedbot-promo-learn-more"
                      >
                        Learn More
                      </a>
                    )}
                  </div>
                ))}
              </div>
            )}
          </section>
        )}

        {/* Announcements Section */}
        {announcements.length > 0 && (
          <section className="bakedbot-announcements-section">
            <div className="bakedbot-section-header">
              <h4>Announcements</h4>
            </div>
            <div className="bakedbot-announcements-content">
              {announcements.map((announcement) => (
                <div
                  key={announcement.id}
                  className="bakedbot-announcement-item"
                >
                  <h5>{announcement.title}</h5>
                  <p>{announcement.description}</p>
                  {announcement.link && (
                    <a
                      href={announcement.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bakedbot-announcement-link"
                    >
                      Read More →
                    </a>
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Categories Overview */}
        <section className="bakedbot-categories-section bakedbot-modern-categories">
          <div className="bakedbot-section-header">
            <h4>Categories</h4>
          </div>
          <div className="bakedbot-categories-mobile-grid">
            {filterOptions.categories
              .slice(0, showAllCategories ? filterOptions.categories.length : 6)
              .map((category) => (
                <div
                  key={category}
                  className="bakedbot-category-card bakedbot-modern-category-card"
                  onClick={() => handleNavigateToMenuWithFilter({ category })}
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      handleNavigateToMenuWithFilter({ category });
                    }
                  }}
                  aria-label={`Browse ${category} products`}
                >
                  <div
                    className="bakedbot-category-icon"
                    style={{ backgroundColor: getCategoryColor(category) }}
                  >
                    {getCategoryEmoji(category)}
                  </div>
                  <h4 className="bakedbot-category-name">{category}</h4>
                </div>
              ))}
          </div>
          {filterOptions.categories.length > 6 && !showAllCategories && (
            <button
              className="bakedbot-view-all-categories-btn"
              onClick={() => setShowAllCategories(true)}
            >
              VIEW ALL
            </button>
          )}
        </section>

        {/* Featured Products */}
        <section className="bakedbot-featured-section">
          <div className="bakedbot-section-header">
            <h4>Popular Flower</h4>
            <button
              className="bakedbot-view-all-btn"
              onClick={() =>
                handleNavigateToMenuWithFilter({ category: "Flower" })
              }
            >
              View All <FaArrowRight />
            </button>
          </div>
          <div className="bakedbot-scrollable-container">
            {/* Left Arrow */}
            {scrollStates.featured?.canScrollLeft && (
              <button
                className="bakedbot-carousel-arrow bakedbot-carousel-arrow-left"
                onClick={() => scrollFeatured("left")}
                aria-label="Scroll featured products left"
              >
                <FaChevronLeft />
              </button>
            )}

            {/* Right Arrow */}
            {scrollStates.featured?.canScrollRight && (
              <button
                className="bakedbot-carousel-arrow bakedbot-carousel-arrow-right"
                onClick={() => scrollFeatured("right")}
                aria-label="Scroll featured products right"
              >
                <FaChevronRight />
              </button>
            )}

            <div
              className="bakedbot-products-scroll bakedbot-featured-scroll bakedbot-mobile-scroll"
              ref={featuredScrollRef}
              onScroll={(e) => updateScrollState(e.currentTarget, "featured")}
              onTouchStart={handleProductTouchStart}
              onTouchEnd={(e) => handleProductTouchEnd(e, scrollFeatured)}
            >
              {featuredProducts
                .slice(0, featuredProductsCount)
                .map((product) => (
                  <ProductCard
                    key={product.meta_sku}
                    product={product}
                    size={isMobile ? "large" : "medium"}
                    selectedVariant={selectedVariants[product.meta_sku]}
                    isInCart={
                      !!(
                        selectedVariants[product.meta_sku] &&
                        cart[selectedVariants[product.meta_sku].product_id]
                      )
                    }
                    cartQuantity={
                      selectedVariants[product.meta_sku] &&
                      cart[selectedVariants[product.meta_sku].product_id]
                        ?.quantity
                    }
                    onVariantSelect={handleVariantSelect}
                    onAddToCart={handleAddToCart}
                    onUpdateQuantity={(productId, change) =>
                      updateQuantity(productId, change)
                    }
                    onNavigateToProduct={handleProductClick}
                    productPageUrl={productPageUrl}
                  />
                ))}
            </div>
          </div>
        </section>

        {/* Category Sections */}
        {categorySections.map((section) => (
          <section
            key={section.title}
            className="bakedbot-category-products-section"
          >
            <div className="bakedbot-section-header">
              <h4>Popular {section.title}</h4>
              <button
                className="bakedbot-view-all-btn"
                onClick={() =>
                  handleNavigateToMenuWithFilter({ category: section.category })
                }
              >
                View All <FaArrowRight />
              </button>
            </div>
            <div className="bakedbot-scrollable-container">
              {/* Left Arrow */}
              {scrollStates[`category-${section.title}`]?.canScrollLeft && (
                <button
                  className="bakedbot-carousel-arrow bakedbot-carousel-arrow-left"
                  onClick={() => scrollCategory(section.title, "left")}
                  aria-label={`Scroll ${section.title} left`}
                >
                  <FaChevronLeft />
                </button>
              )}

              {/* Right Arrow */}
              {scrollStates[`category-${section.title}`]?.canScrollRight && (
                <button
                  className="bakedbot-carousel-arrow bakedbot-carousel-arrow-right"
                  onClick={() => scrollCategory(section.title, "right")}
                  aria-label={`Scroll ${section.title} right`}
                >
                  <FaChevronRight />
                </button>
              )}

              <div
                className="bakedbot-products-scroll bakedbot-category-scroll bakedbot-mobile-scroll"
                ref={(el) => {
                  categoryScrollRefs.current[section.title] = el;
                }}
                onScroll={(e) =>
                  updateScrollState(
                    e.currentTarget,
                    `category-${section.title}`
                  )
                }
                onTouchStart={handleProductTouchStart}
                onTouchEnd={(e) =>
                  handleProductTouchEnd(e, (direction) =>
                    scrollCategory(section.title, direction)
                  )
                }
              >
                {section.products.map((product) => (
                  <ProductCard
                    key={product.meta_sku}
                    product={product}
                    size={isMobile ? "large" : "medium"}
                    selectedVariant={selectedVariants[product.meta_sku]}
                    isInCart={
                      !!(
                        selectedVariants[product.meta_sku] &&
                        cart[selectedVariants[product.meta_sku].product_id]
                      )
                    }
                    cartQuantity={
                      selectedVariants[product.meta_sku] &&
                      cart[selectedVariants[product.meta_sku].product_id]
                        ?.quantity
                    }
                    onVariantSelect={handleVariantSelect}
                    onAddToCart={handleAddToCart}
                    onUpdateQuantity={(productId, change) =>
                      updateQuantity(productId, change)
                    }
                    onNavigateToProduct={handleProductClick}
                    productPageUrl={productPageUrl}
                  />
                ))}
              </div>
            </div>
          </section>
        ))}

        {/* VIP Club Signup Section */}
        {!vipSubmitted ? (
          <section className="bakedbot-vip-signup-section">
            <div className="bakedbot-vip-signup-container">
              <div className="bakedbot-vip-header">
                <h3>🌿 Join Our VIP Club</h3>
                <p>
                  Get exclusive AI recommendations, member pricing, and priority
                  delivery access
                </p>
              </div>
              <form
                onSubmit={handleVipSignup}
                className="bakedbot-vip-signup-form"
              >
                <div className="bakedbot-vip-form-fields">
                  <input
                    type="text"
                    placeholder="First Name"
                    value={vipFormData.firstName}
                    onChange={(e) =>
                      setVipFormData({
                        ...vipFormData,
                        firstName: e.target.value,
                      })
                    }
                    required
                    disabled={vipSubmitting}
                  />
                  <input
                    type="email"
                    placeholder="Email Address"
                    value={vipFormData.email}
                    onChange={(e) =>
                      setVipFormData({
                        ...vipFormData,
                        email: e.target.value,
                      })
                    }
                    required
                    disabled={vipSubmitting}
                  />
                  <input
                    type="tel"
                    placeholder="Phone Number"
                    value={vipFormData.phone}
                    onChange={(e) =>
                      setVipFormData({
                        ...vipFormData,
                        phone: e.target.value,
                      })
                    }
                    required
                    disabled={vipSubmitting}
                  />
                </div>
                <button
                  type="submit"
                  className="bakedbot-vip-activate-btn"
                  disabled={vipSubmitting}
                >
                  {vipSubmitting ? "Activating..." : "🚀 Activate VIP Access"}
                </button>
              </form>
            </div>
          </section>
        ) : (
          <section className="bakedbot-vip-success-section">
            <div className="bakedbot-vip-success-container">
              <div className="bakedbot-vip-success-icon">✅</div>
              <h3>Welcome to VIP Club!</h3>
              <p>
                You're all set! Check your email for exclusive offers and member
                benefits.
              </p>
            </div>
          </section>
        )}

        {/* Staff Picks */}
        {topPickProducts.length > 0 && (
          <section className="bakedbot-staff-picks-section">
            <div className="bakedbot-section-header">
              <h4>Staff Picks</h4>
              <button
                className="bakedbot-view-all-btn"
                onClick={() => handleNavigateToMenuWithFilter()}
              >
                View All <FaArrowRight />
              </button>
            </div>
            <div className="bakedbot-scrollable-container">
              {/* Left Arrow */}
              {scrollStates.staffPicks?.canScrollLeft && (
                <button
                  className="bakedbot-carousel-arrow bakedbot-carousel-arrow-left"
                  onClick={() =>
                    scrollContainer(
                      { current: categoryScrollRefs.current.staffPicks },
                      "left",
                      "staffPicks"
                    )
                  }
                  aria-label="Scroll staff picks left"
                >
                  <FaChevronLeft />
                </button>
              )}

              {/* Right Arrow */}
              {scrollStates.staffPicks?.canScrollRight && (
                <button
                  className="bakedbot-carousel-arrow bakedbot-carousel-arrow-right"
                  onClick={() =>
                    scrollContainer(
                      { current: categoryScrollRefs.current.staffPicks },
                      "right",
                      "staffPicks"
                    )
                  }
                  aria-label="Scroll staff picks right"
                >
                  <FaChevronRight />
                </button>
              )}

              <div
                className="bakedbot-products-scroll bakedbot-category-scroll bakedbot-mobile-scroll"
                ref={(el) => {
                  categoryScrollRefs.current.staffPicks = el;
                }}
                onScroll={(e) =>
                  updateScrollState(e.currentTarget, "staffPicks")
                }
                onTouchStart={handleProductTouchStart}
                onTouchEnd={(e) =>
                  handleProductTouchEnd(e, (direction) =>
                    scrollContainer(
                      { current: categoryScrollRefs.current.staffPicks },
                      direction,
                      "staffPicks"
                    )
                  )
                }
              >
                {topPickProducts.map((product) => (
                  <ProductCard
                    key={`staff-${product.meta_sku}`}
                    product={product}
                    size={isMobile ? "large" : "medium"}
                    selectedVariant={selectedVariants[product.meta_sku]}
                    isInCart={
                      !!(
                        selectedVariants[product.meta_sku] &&
                        cart[selectedVariants[product.meta_sku].product_id]
                      )
                    }
                    cartQuantity={
                      selectedVariants[product.meta_sku] &&
                      cart[selectedVariants[product.meta_sku].product_id]
                        ?.quantity
                    }
                    onVariantSelect={handleVariantSelect}
                    onAddToCart={handleAddToCart}
                    onUpdateQuantity={(productId, change) =>
                      updateQuantity(productId, change)
                    }
                    onNavigateToProduct={handleProductClick}
                    productPageUrl={productPageUrl}
                  />
                ))}
              </div>
            </div>
          </section>
        )}
      </main>
    </div>
  );
};

export default HeadlessHomepage;
