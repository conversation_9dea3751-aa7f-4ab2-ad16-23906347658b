/* SEO and Accessibility Styles */
.bakedbot-screen-reader-text {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px, 1px, 1px, 1px);
  white-space: nowrap;
}

/* Typography Overrides - Prevent WordPress Theme Interference */
.bakedbot-headless-menu h1,
.bakedbot-headless-menu h2,
.bakedbot-headless-menu h3,
.bakedbot-headless-menu h4,
.bakedbot-headless-menu h5,
.bakedbot-headless-menu h6 {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: inherit !important;
  line-height: 1.2 !important;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-decoration: none !important;
  text-shadow: none !important;
  font-style: normal !important;
}

.bakedbot-headless-menu h1 {
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--primary-color) !important;
}

.bakedbot-headless-menu h2 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

.bakedbot-headless-menu h3 {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

.bakedbot-headless-menu h4 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

.bakedbot-headless-menu h5 {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

.bakedbot-headless-menu h6 {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  color: inherit !important;
}

/* Text elements */
.bakedbot-headless-menu p,
.bakedbot-headless-menu span,
.bakedbot-headless-menu div,
.bakedbot-headless-menu label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  line-height: 1.5 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-decoration: none !important;
  text-shadow: none !important;
  font-style: normal !important;
}

/* Links */
.bakedbot-headless-menu a {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: var(--primary-color) !important;
  text-decoration: none !important;
  font-weight: inherit !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
}

.bakedbot-headless-menu a:hover,
.bakedbot-headless-menu a:focus {
  color: var(--secondary-color) !important;
  text-decoration: none !important;
}

/* Buttons */
.bakedbot-headless-menu button,
.bakedbot-headless-menu input[type="button"],
.bakedbot-headless-menu input[type="submit"] {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-weight: 500 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
  line-height: 1.5 !important;
}

/* Form elements */
.bakedbot-headless-menu input,
.bakedbot-headless-menu select,
.bakedbot-headless-menu textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  color: inherit !important;
  font-weight: 400 !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  font-style: normal !important;
  line-height: 1.5 !important;
}

/* Progressive Enhancement Support */
.bakedbot-headless-menu {
  /* Ensure smooth transitions during enhancement */
  transition: all 0.2s ease;
  max-width: 1400px;
  margin: 0 auto;
}

.bakedbot-headless-menu.enhanced {
  /* Mark when React has taken over */
  position: relative;
}

/* Hide non-functional elements until React loads */
.bakedbot-headless-menu:not(.enhanced) .bakedbot-filter-toggle,
.bakedbot-headless-menu:not(.enhanced) .bakedbot-cart-button,
.bakedbot-headless-menu:not(.enhanced) .bakedbot-layout-btn,
.bakedbot-headless-menu:not(.enhanced) .bakedbot-add-to-cart-btn {
  pointer-events: none;
  opacity: 0.7;
  cursor: not-allowed;
}

.bakedbot-headless-menu.enhanced .bakedbot-filter-toggle,
.bakedbot-headless-menu.enhanced .bakedbot-cart-button,
.bakedbot-headless-menu.enhanced .bakedbot-layout-btn,
.bakedbot-headless-menu.enhanced .bakedbot-add-to-cart-btn {
  pointer-events: auto;
  opacity: 1;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

/* Add a subtle indicator when React is loaded */
.bakedbot-headless-menu.enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 2px;
  background: #10b981;
  border-radius: 50%;
  z-index: 1000;
  opacity: 0.8;
}

/* SEO-friendly form styles */
.bakedbot-search-form {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.bakedbot-search-submit {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.5rem;
}

/* Headless Menu Styles - Updated to match homepage */
.bakedbot-headless-menu {
  --primary-color: #065f46;
  --secondary-color: #10b981;
  --background-color: #ffffff;
  --text-color: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --hover-color: #f3f4f6;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  width: 100%;
  position: relative;
  overflow: visible;
  height: auto;
  min-height: auto;
}

/* Dark theme support */
.bakedbot-headless-menu.theme-dark {
  --background-color: #111827;
  --text-color: #f9fafb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
  --hover-color: #1f2937;
}

/* Header - Removed since we use SharedHeader */
.bakedbot-menu-header {
  display: none; /* Hidden since SharedHeader is used */
}

/* Main Content Layout */
.bakedbot-menu-content {
  display: block;
  position: relative;
  height: auto;
  min-height: auto;
  overflow: visible;
}

/* Vertical divider line between filters and products */
.bakedbot-menu-content::after {
  content: '';
  position: absolute;
  left: 300px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: var(--border-color);
  z-index: 1;
  display: none;
}

/* Filters Column - Desktop */
.bakedbot-filters-column {
  display: none;
  background: var(--background-color);
  border-right: 1px solid var(--border-color);
  overflow: visible;
  height: auto;
  min-height: auto;
}

.bakedbot-filters-column .bakedbot-filters-content {
  height: auto;
  display: flex;
  flex-direction: column;
}

.bakedbot-filters-column .bakedbot-filters-scroll-area {
  padding: 1.5rem;
  overflow: visible;
  height: auto;
  max-height: none;
}

.bakedbot-filters-column .bakedbot-filters-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--background-color);
  position: sticky;
  bottom: 0;
}

/* Filters Modal */
.bakedbot-filters-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: none;
  pointer-events: none;
}

.bakedbot-filters-modal.open {
  display: block;
  pointer-events: auto;
}

.bakedbot-filters-modal .bakedbot-modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background-color);
  border-radius: 1rem 1rem 0 0;
  max-height: 90vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
}

.bakedbot-filters-modal.open .bakedbot-modal-content {
  transform: translateY(0);
}

.bakedbot-filters-modal .bakedbot-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.bakedbot-filters-modal .bakedbot-modal-header .bakedbot-modal-handle {
  position: absolute;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2rem;
  height: 0.25rem;
  background: var(--border-color);
  border-radius: 0.125rem;
}

.bakedbot-filters-modal .bakedbot-modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.bakedbot-filters-modal .bakedbot-modal-header button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.bakedbot-filters-modal .bakedbot-modal-header button:hover {
  background: var(--hover-color);
  color: var(--text-color);
}

.bakedbot-filters-modal .bakedbot-modal-body {
  padding: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.bakedbot-filters-modal .bakedbot-modal-body .bakedbot-filters-scroll-area {
  overflow-y: auto;
  padding: 1rem 1.5rem;
  flex: 1;
}

.bakedbot-filters-modal .bakedbot-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background: var(--background-color);
  display: flex;
  gap: 1rem;
}

.bakedbot-filters-modal .bakedbot-modal-footer > button {
  flex: 1;
}

/* Filter Sections */
.bakedbot-filter-section {
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  overflow: hidden;
  padding: 1rem 0;
}

.bakedbot-filter-section:last-of-type {
  margin-bottom: 0;
}

.bakedbot-filter-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  cursor: pointer;
  background: var(--hover-color);
  transition: background-color 0.2s;
}

.bakedbot-filter-section-header:hover {
  background: var(--border-color);
}

.bakedbot-filter-section-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bakedbot-filter-section-toggle {
  color: var(--text-secondary);
  transition: transform 0.2s;
}

.bakedbot-filter-section.collapsed .bakedbot-filter-section-toggle {
  transform: rotate(-90deg);
}

.bakedbot-filter-section-content {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  max-height: 300px;
  overflow-y: auto;
  transition: max-height 0.3s ease;
}

.bakedbot-filter-section.collapsed .bakedbot-filter-section-content {
  max-height: 0;
  padding: 0 1rem;
  overflow: hidden;
  border-top: none;
}

.bakedbot-filter-search {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  background: var(--background-color);
  color: var(--text-color);
}

.bakedbot-filter-search:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.bakedbot-filter-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 0.25rem;
  margin: 0.125rem 0;
}

.bakedbot-filter-checkbox:hover {
  background: var(--hover-color);
}

.bakedbot-filter-checkbox input {
  margin: 0;
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary-color);
}

/* Range Inputs */
.bakedbot-range-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.bakedbot-range-inputs input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background: var(--background-color);
  color: var(--text-color);
}

.bakedbot-range-inputs input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.bakedbot-range-inputs input::placeholder {
  color: var(--text-secondary);
}

.bakedbot-range-separator {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  flex-shrink: 0;
  padding: 0 0.25rem;
}

/* Range Sliders */
.bakedbot-range-slider-container {
  margin-top: 0.75rem;
}

.bakedbot-range-slider-dual {
  position: relative;
  height: 1.5rem;
  margin-bottom: 0.75rem;
}

.bakedbot-range-slider {
  position: absolute;
  width: 100%;
  height: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  pointer-events: none;
}

.bakedbot-range-slider::-webkit-slider-track {
  height: 0.25rem;
  background: var(--border-color);
  border-radius: 0.125rem;
}

.bakedbot-range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--shadow);
  pointer-events: auto;
  position: relative;
  z-index: 2;
}

.bakedbot-range-slider::-moz-range-track {
  height: 0.25rem;
  background: var(--border-color);
  border-radius: 0.125rem;
  border: none;
}

.bakedbot-range-slider::-moz-range-thumb {
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: 2px solid white;
  box-shadow: var(--shadow);
}

.bakedbot-range-slider-min {
  z-index: 1;
}

.bakedbot-range-slider-max {
  z-index: 2;
}

.bakedbot-range-slider-dual::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 0.25rem;
  background: var(--border-color);
  border-radius: 0.125rem;
  transform: translateY(-50%);
}

.bakedbot-range-values {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Filters Footer */
.bakedbot-filters-footer {
  display: flex;
  gap: 0.75rem;
}

.bakedbot-apply-filters-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bakedbot-apply-filters-btn:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-apply-filters-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bakedbot-clear-filters-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.bakedbot-clear-filters-btn:hover {
  background: var(--hover-color);
  color: var(--text-color);
}

/* Products Area */
.bakedbot-products-area {
  padding: 1.5rem;
  overflow: visible;
  height: auto;
  min-height: auto;
}

/* Results Header */
.bakedbot-results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1rem;
}

.bakedbot-results-count {
  font-size: 1rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.bakedbot-results-header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bakedbot-layout-toggle-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 0.25rem;
}

.bakedbot-layout-toggle-btn {
  padding: 0.5rem;
  border: none;
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bakedbot-layout-toggle-btn:hover {
  background-color: var(--background-color);
  color: var(--text-color);
}

.bakedbot-layout-toggle-btn.active {
  background-color: var(--primary-color);
  color: white !important;
}

.bakedbot-page-info {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  display: none; /* Hide the page info */
}

.bakedbot-header-controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.bakedbot-header-controls-left {
  flex: 1;
}

.bakedbot-header-controls-right {
  display: none; /* Hide from header, will be in results area */
}

/* Product Grid */
.bakedbot-product-grid {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.bakedbot-product-grid.layout-grid {
  grid-template-columns: repeat(3, 1fr);
}

.bakedbot-product-grid.layout-list {
  grid-template-columns: 1fr;
}

.bakedbot-product-card {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  overflow: hidden;
  transition: all 0.2s;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  height: 100%; /* Ensure all cards have the same height */
  min-height: 380px; /* Minimum height for consistency */
}

.bakedbot-product-card-link {
  display: block;
  text-decoration: none !important;
  color: inherit !important;
  cursor: pointer;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.bakedbot-product-card-link:hover,
.bakedbot-product-card-link:focus {
  text-decoration: none !important;
  color: inherit !important;
}

.layout-list .bakedbot-product-card {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: 1rem;
}

.bakedbot-product-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.bakedbot-product-image {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  background: var(--hover-color);
}

.layout-list .bakedbot-product-image {
  aspect-ratio: 1;
  width: 200px;
  height: 200px;
}

.bakedbot-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;
}

.bakedbot-product-card:hover .bakedbot-product-image img {
  transform: scale(1.05);
}

.bakedbot-product-info {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-height: 120px; /* Reserve space for consistent content height */
}

.bakedbot-product-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 2.8em; /* 2 lines * 1.4 line-height */
}

.bakedbot-product-brand {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
  min-height: 1.2em; /* Accommodate one line */
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.bakedbot-product-details {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0.25rem 0;
}

.bakedbot-variant-selector {
  margin: 0.5rem 0;
}

.bakedbot-variant-selector select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background: var(--background-color);
  color: var(--text-color);
  font-size: 0.875rem;
}

.bakedbot-variant-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.bakedbot-product-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  gap: 0.75rem;
}

.bakedbot-product-price {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--primary-color);
}

.bakedbot-add-to-cart-btn {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.875rem;
}

.bakedbot-add-to-cart-btn:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-add-to-cart-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Quantity Controls */
.bakedbot-quantity-controls-inline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--hover-color);
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.bakedbot-quantity-btn-inline {
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  background: var(--primary-color);
  color: white !important;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.bakedbot-quantity-btn-inline:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-quantity-display-inline {
  font-weight: 600;
  color: var(--text-color);
  min-width: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
}

.bakedbot-quantity-controls-modal {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--hover-color);
  border-radius: 0.375rem;
  padding: 0.25rem;
}

.bakedbot-quantity-btn-modal {
  width: 1.75rem;
  height: 1.75rem;
  border: none;
  background: var(--primary-color);
  color: white !important;
  border-radius: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: background-color 0.2s;
}

.bakedbot-quantity-btn-modal:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-quantity-display-modal {
  font-weight: 600;
  color: var(--text-color);
  min-width: 1.5rem;
  text-align: center;
  font-size: 0.875rem;
}

/* Loading and Empty States */
.bakedbot-loading-state,
.bakedbot-empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-secondary);
  font-size: 1rem;
  grid-column: 1 / -1;
}

/* Pagination */
.bakedbot-pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.bakedbot-pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bakedbot-pagination-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  background: var(--background-color);
  color: var(--text-color);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  font-weight: 500;
}

.bakedbot-pagination-btn:hover:not(:disabled) {
  background: var(--hover-color);
  border-color: var(--primary-color);
}

.bakedbot-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bakedbot-pagination-btn.active {
  background: var(--primary-color);
  color: white !important;
  border-color: var(--primary-color);
}

.bakedbot-pagination-pages {
  display: flex;
  gap: 0.25rem;
}

.bakedbot-tag,
.bakedbot-mood-tag {
  padding: 0.375rem 0.75rem;
  background: var(--secondary-color);
  color: white !important;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.bakedbot-mood-tag {
  background: #8b5cf6;
}

.bakedbot-effects-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bakedbot-effect-detail {
  display: flex;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.bakedbot-effect-detail strong {
  color: var(--primary-color);
  min-width: 80px;
}

.bakedbot-effect-description {
  margin-top: 0.5rem;
  padding: 1rem;
  background: var(--hover-color);
  border-radius: 0.5rem;
  font-size: 0.95rem;
  line-height: 1.6;
}

.bakedbot-effect-description p {
  margin: 0;
}

.bakedbot-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.bakedbot-star-icon {
  color: #fbbf24;
}

.bakedbot-reviews-count {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.bakedbot-description-section p {
  margin: 0;
  line-height: 1.6;
  color: var(--text-color);
}

.bakedbot-variants-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bakedbot-variant-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  transition: all 0.2s;
}

.bakedbot-variant-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
}

.bakedbot-variant-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.bakedbot-variant-weight {
  font-weight: 600;
  color: var(--text-color);
}

.bakedbot-variant-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

.bakedbot-variant-availability {
  display: flex;
  gap: 0.5rem;
}

.bakedbot-medical-badge,
.bakedbot-rec-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.bakedbot-medical-badge {
  background: var(--primary-color);
  color: white !important ;
}

.bakedbot-rec-badge {
  background: var(--secondary-color);
  color: white !important;
}

/* Product Badges */
.bakedbot-product-badges {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  align-items: flex-end;
}

.bakedbot-product-badge {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.125rem;
  backdrop-filter: blur(4px);
  max-width: fit-content;
}

.bakedbot-product-badge.top-pick {
  background: rgba(251, 191, 36, 0.9);
  color: white !important;
  font-weight: 700;
}

.bakedbot-product-badge.featured {
  background: rgba(139, 92, 246, 0.9);
  color: white !important;
  font-weight: 600;
}

.bakedbot-product-badge.high-thc {
  background: rgba(239, 68, 68, 0.9);
  color: white !important;
}

.bakedbot-product-badge.high-cbd {
  background: rgba(34, 197, 94, 0.9);
  color: white !important;
}

.bakedbot-add-variant-btn {
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.bakedbot-add-variant-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  /* Mobile Layout - Switch to Single Column */
  .bakedbot-menu-content {
    /* grid-template-columns: 1fr; */
    display: block;
    padding-top: 0;
  }

  .bakedbot-menu-content::after {
    display: none;
  }

  /* Hide Desktop Filter Column on Mobile */
  .bakedbot-filters-column {
    display: none;
  }

  /* Show Mobile Filter Modal */
  .bakedbot-filters-modal {
    display: block;
  }

  .bakedbot-products-area {
    height: auto;
    min-height: calc(100vh - 80px);
  }

  .bakedbot-product-grid.layout-grid {
    padding: 0rem;
    gap: 1rem;
    grid-template-columns: repeat(2, 1fr);
  }

  .bakedbot-mobile-product-grid {
    padding: 0rem;
    gap: 1rem;
    grid-template-columns: repeat(2, 1fr);
  }

  .bakedbot-mobile-product-grid .bakedbot-product-card {
    min-height: auto;
    border-radius: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-image {
    aspect-ratio: 1;
    height: auto;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-info {
    padding: 0rem;
    gap: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-name {
    font-size: 0.875rem;
    line-height: 1.3;
    -webkit-line-clamp: 2;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-brand {
    font-size: 0.75rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-details {
    font-size: 0.625rem;
    gap: 0.125rem;
    margin: 0.25rem 0;
    flex-direction: column;
    align-items: flex-start;
  }

  .bakedbot-mobile-product-grid .bakedbot-variant-selector select {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-actions {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-price {
    font-size: 1rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-add-to-cart-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-controls-inline {
    padding: 0.125rem;
    border-radius: 0.25rem;
    background: var(--hover-color);
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-btn-inline {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
    border-radius: 0.125rem;
    background: var(--primary-color);
    color: white !important;
    border: none;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-display-inline {
    font-size: 0.75rem;
    min-width: 1rem;
  }

  .bakedbot-layout-list .bakedbot-product-card {
    display: flex;
    flex-direction: column;
  }

  .bakedbot-layout-list .bakedbot-product-image {
    width: 100%;
    aspect-ratio: 16/9;
  }

  .bakedbot-modal-content {
    margin: 0.5rem;
    max-height: calc(100vh - 1rem);
    border-radius: 0.75rem;
  }

  .bakedbot-form-row {
    grid-template-columns: 1fr;
  }

  .bakedbot-checkout-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .bakedbot-product-details-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .bakedbot-cannabinoid-info {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 1rem;
  }

  .bakedbot-variant-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .bakedbot-variant-availability {
    align-self: stretch;
    justify-content: space-between;
  }

  .bakedbot-add-variant-btn {
    width: 100%;
  }

  .bakedbot-product-actions {
    flex-direction: column;
    align-items: flex-start;
  }
}@media (max-width: 480px) {
  .bakedbot-products-area {
    padding: 0rem;
  }

  .bakedbot-product-grid {
    gap: 0.75rem;
  }

  .bakedbot-product-grid.layout-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .bakedbot-modal-content {
    margin: 0.25rem;
    max-height: calc(100vh - 0.5rem);
  }

  .bakedbot-product-details-grid {
    padding: 1rem;
  }

  .bakedbot-product-header h3 {
    font-size: 1.5rem;
  }

  .bakedbot-category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .bakedbot-order-success {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }

  .bakedbot-success-icon {
    width: 60px;
    height: 60px;
  }

  .bakedbot-success-icon svg {
    font-size: 1.5rem;
  }

  .bakedbot-order-success h2 {
    font-size: 1.5rem;
  }

  .bakedbot-continue-shopping-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    min-width: 150px;
  }
}

/* Tablet and landscape mobile - 3 columns */
@media (min-width: 481px) and (max-width: 768px) {
  .bakedbot-product-grid.layout-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .bakedbot-mobile-product-grid {
    padding: 1rem;
    gap: 1rem;
    grid-template-columns: repeat(3, 1fr);
  }

  .bakedbot-mobile-product-grid .bakedbot-product-card {
    min-height: auto;
    border-radius: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-image {
    aspect-ratio: 1;
    height: auto;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-info {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-name {
    font-size: 0.875rem;
    line-height: 1.3;
    -webkit-line-clamp: 2;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-brand {
    font-size: 0.75rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-details {
    font-size: 0.625rem;
    gap: 0.125rem;
    margin: 0.25rem 0;
    flex-direction: column;
    align-items: flex-start;
  }

  .bakedbot-mobile-product-grid .bakedbot-variant-selector select {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-actions {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-price {
    font-size: 1rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-add-to-cart-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-controls-inline {
    padding: 0.125rem;
    border-radius: 0.25rem;
    background: var(--hover-color);
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-btn-inline {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
    border-radius: 0.125rem;
    background: var(--primary-color);
    color: white !important;
    border: none;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-display-inline {
    font-size: 0.75rem;
    min-width: 1rem;
  }
}

@media (min-width: 769px) {
  .bakedbot-filter-toggle {
    display: none;
  }
}

/* Large desktop - 4 columns */
@media (min-width: 1025px) {
  .bakedbot-product-grid.layout-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* VIP Signup Section */
.bakedbot-vip-signup-section {
  margin: 2rem 0;
  padding: 0 1.5rem;
}

.bakedbot-vip-signup-container {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-radius: 1rem;
  padding: 2rem;
  color: #1f2937;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.bakedbot-vip-signup-container::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
  pointer-events: none;
}

.bakedbot-vip-header {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.bakedbot-vip-header h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.bakedbot-vip-header p {
  margin: 0;
  color: #374151;
  font-size: 1rem;
  opacity: 0.9;
}

.bakedbot-vip-signup-form {
  position: relative;
  z-index: 1;
}

.bakedbot-vip-form-fields {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.bakedbot-vip-form-fields input {
  padding: 0.75rem 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.5rem;
  background: rgba(255, 255, 255, 0.9);
  color: #1f2937;
  font-size: 1rem;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.bakedbot-vip-form-fields input:focus {
  outline: none;
  border-color: #065f46;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
}

.bakedbot-vip-form-fields input::placeholder {
  color: #6b7280;
}

.bakedbot-vip-form-fields input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.bakedbot-vip-activate-btn {
  width: 100%;
  background: #1f2937;
  color: #ffffff;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(31, 41, 55, 0.3);
}

.bakedbot-vip-activate-btn:hover:not(:disabled) {
  background: #111827;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(31, 41, 55, 0.4);
}

.bakedbot-vip-activate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* VIP Success Section */
.bakedbot-vip-success-section {
  margin: 2rem 0;
  padding: 0 1.5rem;
}

.bakedbot-vip-success-container {
  background: linear-gradient(135deg, #10b981 0%, #065f46 100%);
  border-radius: 1rem;
  padding: 2rem;
  color: #ffffff;
  text-align: center;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.bakedbot-vip-success-container::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
  pointer-events: none;
}

.bakedbot-vip-success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  animation: successPulse 2s ease-in-out infinite;
}

.bakedbot-vip-success-container h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #ffffff;
  position: relative;
  z-index: 1;
}

.bakedbot-vip-success-container p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  position: relative;
  z-index: 1;
}

/* Mobile adjustments for VIP section */
@media (max-width: 768px) {
  .bakedbot-vip-signup-section,
  .bakedbot-vip-success-section {
    margin: 1.5rem 0;
    padding: 0 1rem;
  }

  .bakedbot-vip-signup-container,
  .bakedbot-vip-success-container {
    padding: 1.5rem;
  }

  .bakedbot-vip-form-fields {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .bakedbot-vip-header h3 {
    font-size: 1.25rem;
  }

  .bakedbot-vip-header p {
    font-size: 0.875rem;
  }

  .bakedbot-vip-activate-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .bakedbot-vip-signup-container,
  .bakedbot-vip-success-container {
    padding: 1.25rem;
    margin: 0 0.5rem;
  }

  .bakedbot-vip-header h3 {
    font-size: 1.125rem;
  }

  .bakedbot-vip-form-fields input {
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
  }
}

@media (min-width: 769px) {
  .bakedbot-filter-toggle {
    display: none;
  }
}

/* New Menu Header Styles */
.bakedbot-menu-page-header {
  background-color: var(--background-color);
  padding: 1rem 2rem;
  text-align: center;
}

.menu-page-header h1 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: var(--text-color) !important;
  margin-bottom: 0.5rem !important;
}

.menu-page-header p {
  font-size: 1.1rem !important;
  color: var(--text-secondary) !important;
  max-width: 600px;
  margin: 0 auto 2rem auto !important;
}

.bakedbot-filters-and-search-container {
  background-color: var(--background-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  max-width: 1000px;
  margin: 0 auto;
  border: 1px solid var(--border-color);
}

.bakedbot-search-bar-container {
  display: flex;
  margin-bottom: 1.5rem;
}

.bakedbot-search-bar-container input {
  flex-grow: 1;
  border: 1px solid var(--border-color);
  border-right: none;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border-radius: 0.5rem 0 0 0.5rem;
}

.bakedbot-search-bar-container input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(6, 95, 70, 0.1);
  z-index: 1;
}

.bakedbot-search-bar-container button {
  background-color: var(--secondary-color);
  color: white !important;
  border: none;
  padding: 0 1.5rem;
  font-weight: 600;
  border-radius: 0 0.5rem 0.5rem 0;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bakedbot-search-bar-container button:hover {
  background-color: color-mix(in srgb, var(--secondary-color) 90%, black);
}

.bakedbot-main-filters-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.bakedbot-filter-dropdown select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  background-color: var(--background-color);
  font-size: 0.9rem;
  color: var(--text-color);
  cursor: pointer;
}

.bakedbot-category-filters-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  gap: 0.75rem;
  overflow-x: auto;
  padding-bottom: 1rem;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on mobile */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  width: 100%;
  max-width: 100%;
}

.bakedbot-category-filters-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

/* On desktop, show a subtle scrollbar when hovering for better UX */
@media (min-width: 769px) {
  .bakedbot-category-filters-container:hover {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
  }
  
  .bakedbot-category-filters-container:hover::-webkit-scrollbar {
    display: block;
    height: 4px;
  }
  
  .bakedbot-category-filters-container:hover::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .bakedbot-category-filters-container:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
  
  .bakedbot-category-filters-container:hover::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
  }
}

.bakedbot-category-filter-btn {
  padding: 0.5rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  background-color: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  min-width: max-content;
}

.bakedbot-category-filter-btn:hover {
  background-color: var(--hover-color);
  border-color: var(--secondary-color);
  color: var(--text-color);
}

.bakedbot-category-filter-btn.active {
  background-color: var(--secondary-color);
  color: white !important;
  border-color: var(--secondary-color);
}

.bakedbot-layout-toggle-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 0.25rem;
  margin-left: auto;
}

.bakedbot-layout-toggle-btn {
  padding: 0.5rem;
  border: none;
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: 0.25rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bakedbot-layout-toggle-btn:hover {
  background-color: var(--background-color);
  color: var(--text-color);
}

.bakedbot-layout-toggle-btn.active {
  background-color: var(--primary-color);
  color: white !important;
}

.bakedbot-header-controls-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.bakedbot-header-controls-left {
  flex: 1;
}

.bakedbot-header-controls-right {
  display: none; /* Hide from header, will be in results area */
}

/* Mobile-specific styles for headless menu */

/* Mobile menu layout */
.bakedbot-mobile-menu {
  padding-top: 0;
}

.bakedbot-mobile-menu-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 20;
  gap: 1rem;
}

.bakedbot-mobile-filter-btn,
.bakedbot-mobile-search-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  flex: 1;
  justify-content: center;
}

.bakedbot-mobile-filter-btn:hover,
.bakedbot-mobile-search-btn:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

.bakedbot-mobile-search-container {
  display: flex;
  padding: 1rem;
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  gap: 0.75rem;
}

.bakedbot-mobile-search-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  color: var(--text-color) !important;
  font-size: 1rem;
}

.bakedbot-mobile-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.bakedbot-mobile-search-execute-btn {
  padding: 0.75rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  min-width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile results header */
.bakedbot-mobile-results-header {
  padding: 1rem;
  background: var(--background-color);
}

.bakedbot-mobile-results-header .results-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* Mobile filters modal */
.bakedbot-mobile-filters-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 50;
  display: flex;
  align-items: flex-end;
}

.bakedbot-mobile-filters-modal .bakedbot-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.bakedbot-mobile-filters-content {
  position: relative;
  background: var(--background-color);
  width: 100%;
  max-height: 85vh;
  border-radius: 1rem 1rem 0 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateY(100%);
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes slideUp {
  to {
    transform: translateY(0);
  }
}

.bakedbot-mobile-filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--background-color);
}

.bakedbot-mobile-filters-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.bakedbot-mobile-filters-body {
  padding: 1rem;
  overflow-y: auto;
}

.bakedbot-mobile-filter-section {
  margin-bottom: 1.5rem;
}

.bakedbot-mobile-filter-section:last-child {
  margin-bottom: 0;
}

.bakedbot-mobile-filter-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem !important;
}

.bakedbot-mobile-category-buttons,
.bakedbot-mobile-range-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.bakedbot-mobile-category-btn,
.bakedbot-mobile-range-btn {
  padding: 0.5rem 0.75rem;
  background: var(--hover-color);
  color: var(--text-color) !important;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.bakedbot-mobile-category-btn:hover,
.bakedbot-mobile-range-btn:hover {
  background: var(--primary-color);
  color: white !important;
  border-color: var(--primary-color);
}

.bakedbot-mobile-category-btn.active,
.bakedbot-mobile-range-btn.active {
  background: var(--primary-color);
  color: white !important;
  border-color: var(--primary-color);
}

.bakedbot-mobile-sort-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--background-color);
  color: var(--text-color) !important;
  font-size: 0.875rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.bakedbot-mobile-sort-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

.bakedbot-mobile-filters-footer {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background: var(--background-color);
}

.bakedbot-mobile-clear-filters-btn,
.bakedbot-mobile-apply-filters-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bakedbot-mobile-clear-filters-btn {
  background: var(--hover-color);
  color: var(--text-color) !important;
  border: 1px solid var(--border-color);
}

.bakedbot-mobile-clear-filters-btn:hover {
  background: var(--border-color);
}

.bakedbot-mobile-apply-filters-btn {
  background: var(--primary-color);
  color: white !important;
}

.bakedbot-mobile-apply-filters-btn:hover {
  background: color-mix(in srgb, var(--primary-color) 90%, black);
}

/* Hide desktop elements on mobile */
@media (max-width: 768px) {
  
  .bakedbot-menu-page-header .bakedbot-filters-and-search-container {
    display: none;
  }
  
  .bakedbot-menu-page-header h1 {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  
  .bakedbot-menu-page-header p {
    font-size: 0.9rem !important;
    margin-bottom: 0rem !important;
  }
  
  .bakedbot-filter-dropdown {
    display: none;
  }
  
  .bakedbot-category-filters-container {
    display: none;
  }
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .bakedbot-mobile-product-grid {
    padding: 0rem;
    gap: 0.75rem;
    grid-template-columns: repeat(2, 1fr);
  }

  .bakedbot-mobile-product-grid .bakedbot-product-card {
    min-height: auto;
    border-radius: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-image {
    aspect-ratio: 1;
    height: auto;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-info {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-name {
    font-size: 0.875rem;
    line-height: 1.3;
    -webkit-line-clamp: 2;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-brand {
    font-size: 0.75rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-details {
    font-size: 0.625rem;
    gap: 0.125rem;
    margin: 0.25rem 0;
    flex-direction: column;
    align-items: flex-start;
  }

  .bakedbot-mobile-product-grid .bakedbot-variant-selector select {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-actions {
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-product-price {
    font-size: 1rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-add-to-cart-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-controls-inline {
    padding: 0.125rem;
    border-radius: 0.25rem;
    background: var(--hover-color);
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-btn-inline {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
    border-radius: 0.125rem;
    background: var(--primary-color);
    color: white !important;
    border: none;
  }

  .bakedbot-mobile-product-grid .bakedbot-quantity-display-inline {
    font-size: 0.75rem;
    min-width: 1rem;
  }
  
  .bakedbot-mobile-menu-bar {
    padding: 0.75rem;
  }
  
  .bakedbot-mobile-filter-btn,
  .bakedbot-mobile-search-btn {
    padding: 0.625rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .bakedbot-mobile-search-container {
    padding: 0.75rem;
  }
  
  .bakedbot-mobile-results-header {
    padding: 0.75rem;
  }
  
  .bakedbot-mobile-filters-body {
    padding: 0.75rem;
  }
  
  .bakedbot-mobile-filters-footer {
    padding: 0.75rem;
  }
}

.bakedbot-category-filter-btn,
.bakedbot-mobile-category-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  text-align: center;
  min-height: 1rem;
}

.bakedbot-category-emoji {
  font-size: 1.25rem;
  margin-right: 0.5rem;
  line-height: 1;
}

.bakedbot-category-text {
  font-size: 0.8rem;
  line-height: 1.2;
}

/* Pills for mobile category buttons */
.bakedbot-mobile-category-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 0.75rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
  margin-left: -0.5rem;
  margin-right: -0.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.bakedbot-mobile-category-buttons::-webkit-scrollbar {
  display: none;
}

.bakedbot-mobile-category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0rem 0.5rem;
  border-radius: 999px;
  border: 1.5px solid #e5e7eb;
  background: #f3f4f6;
  color: #374151;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.18s;
  min-width: max-content;
  box-shadow: none;
  outline: none;
  white-space: nowrap;
  height: 2.5rem;
  line-height: 1.2;
}

.bakedbot-mobile-category-btn .bakedbot-category-emoji {
  font-size: 1.25rem;
  margin-right: 0.5rem;
}

.bakedbot-mobile-category-btn.all {
  background: var(--primary-color, #10b981);
  color: #fff;
  border: none;
}

.bakedbot-mobile-category-btn.active {
  background: var(--primary-color, #10b981);
  color: #fff !important;
  border: none;
  box-shadow: 0 2px 8px rgba(16,185,129,0.08);
}

.bakedbot-mobile-category-btn:not(.active):hover {
  background: #e0e7ef;
  color: var(--primary-color, #10b981);
  border-color: var(--primary-color, #10b981);
}

.bakedbot-mobile-category-btn.all:not(.active):hover {
  background: #059669;
  color: #fff;
}

.bakedbot-mobile-category-btn .bakedbot-category-text {
  font-weight: 600;
}

.bakedbot-mobile-category-bar {
  margin: 0.5rem 0 1.25rem 0;
  padding: 0 0.5rem;
  width: 100%;
  overflow-x: auto;
  background: none;
}

/* The .bakedbot-mobile-category-buttons class already handles horizontal scroll and pill layout */

/* Mobile category pills */
@media (max-width: 768px) {
  .bakedbot-category-filters-container {
    display: flex;
    overflow-x: auto;
    gap: 8px;
    padding: 8px 0;
    margin-bottom: 16px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .bakedbot-category-filters-container::-webkit-scrollbar {
    display: none;
  }

  .bakedbot-category-filter-btn {
    flex: 0 0 auto;
    font-size: 12px;
    padding: 6px 12px;
    white-space: nowrap;
    border-radius: 16px;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    color: #374151;
    transition: all 0.2s;
  }

  .bakedbot-category-filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
  }

  .bakedbot-category-emoji {
    margin-right: 4px;
  }
}

