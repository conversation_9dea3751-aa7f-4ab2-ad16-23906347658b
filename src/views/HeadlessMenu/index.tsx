/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, KeyboardEvent, useEffect } from "react";
import {
  FaPlus,
  FaMinus,
  FaTimes,
  FaSearch,
  FaCheck,
  FaStar,
  FaTag,
  FaFire,
  FaLeaf,
} from "react-icons/fa";
import { BiGridAlt, BiListUl } from "react-icons/bi";
import {
  ProductVariant,
  GroupedProduct,
  bakedBotApiRequest,
} from "../../utils/api";
import { useCart } from "../ChatWidget/CartContext";
import useAuth from "../../hooks/useAuth";
import SharedHeader from "../../components/SharedHeader";
import { useProducts } from "./useProducts";
import {
  DEFAULT_FILTERS,
  emojiForCategory,
  Filters,
} from "../../utils/menuhelpers";
import "./headless-menu.css";

interface HeadlessMenuProps {
  productsPerPage?: number;
  layout?: "grid" | "list";
  primaryColor?: string;
  secondaryColor?: string;
  enableCart?: boolean;
  enableSearch?: boolean;
  enableFilters?: boolean;
  containerId?: string;
  productPageUrl?: string;
}

const HeadlessMenu: React.FC<HeadlessMenuProps> = ({
  productsPerPage = 12,
  layout: initialLayout = "grid",
  primaryColor = "#065f46",
  secondaryColor = "#10b981",
  enableCart = true,
  enableSearch = true,
  enableFilters = true,
  containerId,
  productPageUrl = "/product",
}) => {
  /* ─── central hook ────────────────────────────────────── */
  const {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    page,
    setPage,
    products,
    totalProducts,
    totalPages,
    loading,
    filterOptions,
  } = useProducts({ productsPerPage });

  /* ─── local UI state (all the missing features) ───────── */
  const [layout, setLayout] = useState<"grid" | "list">(initialLayout);
  const [isMobile, setIsMobile] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState<
    Record<string, ProductVariant>
  >({});
  const [showFiltersModal, setShowFiltersModal] = useState(false);
  const [vipSubmitted, setVipSubmitted] = useState(false);
  const [vipSubmitting, setVipSubmitting] = useState(false);
  const [vipForm, setVipForm] = useState({
    email: "",
    phone: "",
    firstName: "",
  });

  const { cart, addToCart, updateQuantity, removeFromCart, handleCheckout } =
    useCart();

  /* optional auth */
  const user = useAuth()?.user ?? null;

  /* ─── helpers ─────────────────────────────────────────── */
  const toggleFilterValue = (
    key: keyof Omit<Filters, "priceRange" | "thcRange" | "cbdRange">,
    value: string,
    single = false
  ) => {
    setFilters((prev) => {
      const next = { ...prev };
      const arr = prev[key] as string[];
      if (single) {
        next[key] = arr[0] === value ? [] : ([value] as any);
      } else {
        next[key] = arr.includes(value)
          ? arr.filter((v) => v !== value)
          : [...arr, value];
      }
      return next;
    });
    setPage(1);
  };

  const setRange = (
    key: "priceRange" | "thcRange" | "cbdRange",
    range: [number, number]
  ) => {
    setFilters((prev) => ({ ...prev, [key]: range }));
    setPage(1);
  };

  const handleVariantSelect = (sku: string, v: ProductVariant) =>
    setSelectedVariants((prev) => ({ ...prev, [sku]: v }));

  const onSearch = () => setPage(1);

  const onSearchKey = (e: KeyboardEvent<HTMLInputElement>) =>
    e.key === "Enter" && onSearch();

  const handleSortChange = (newSort: string) => {
    setSortBy(newSort);
    setPage(1);
  };

  const handleAddToCart = (p: GroupedProduct) => {
    const v = selectedVariants[p.meta_sku] ?? p.variants[0];
    if (!v) return;
    addToCart({
      id: v.product_id,
      product_id: v.product_id,
      cann_sku_id: v.product_id,
      product_name: `${p.product_name} - ${v.display_weight}`,
      brand_name: p.brand_name,
      category: p.category,
      image_url: p.image_url,
      latest_price: v.latest_price,
      percentage_thc: p.percentage_thc ?? null,
      percentage_cbd: p.percentage_cbd ?? null,
      meta_sku: p.meta_sku,
      medical: v.medical,
      recreational: v.recreational,
      brand_id: null,
      url: "",
      raw_product_name: p.product_name,
      raw_weight_string: v.display_weight,
      display_weight: v.display_weight,
      raw_product_category: p.category,
      raw_subcategory: p.subcategory ?? null,
      subcategory: p.subcategory ?? null,
      product_tags: null,
      mg_thc: null,
      mg_cbd: null,
      quantity_per_package: null,
      menu_provider: "",
      retailer_id: "",
      updated_at: "",
      price: v.latest_price,
      description: p.description ?? "",
    });
  };

  /* VIP signup */
  const submitVip = async (e: React.FormEvent) => {
    e.preventDefault();
    setVipSubmitting(true);
    try {
      await bakedBotApiRequest("POST", "/public/products/users", {
        external_id: vipForm.email,
        email: vipForm.email,
        phone: vipForm.phone,
        first_name: vipForm.firstName,
        vip: true,
      });
      setVipSubmitted(true);
      setVipForm({ email: "", phone: "", firstName: "" });
    } catch (err) {
      alert("Failed – try again?");
    } finally {
      setVipSubmitting(false);
    }
  };

  const cartCount = Object.values(cart).reduce((s, c) => s + c.quantity, 0);
  const cartTotal = Object.values(cart).reduce(
    (s, c) => s + c.product.latest_price * c.quantity,
    0
  );

  // Add mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  /* ─── render ──────────────────────────────────────────── */
  return (
    <div
      id={containerId}
      className="bakedbot-headless-menu enhanced"
      style={
        {
          "--primary-color": primaryColor,
          "--secondary-color": secondaryColor,
        } as React.CSSProperties
      }
    >
      <SharedHeader
        cartCount={cartCount}
        onCartClick={() => {}}
        onFilterClick={() => setShowFiltersModal(true)}
        onSearch={onSearch}
        isMenuPage
        filterCount={
          filters.category.length +
          filters.subcategory.length +
          filters.brands.length
        }
        enableCart={enableCart}
      />

      {/* hero box */}
      <section className="bakedbot-menu-page-header">
        <h1>Products</h1>
        <p>
          Browse our collection with advanced filtering, search, and AI-powered
          recommendations
        </p>

        <div className="bakedbot-filters-and-search-container">
          {/* search */}
          {enableSearch && (
            <div className="bakedbot-search-bar-container">
              <input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={onSearchKey}
              />
              <button onClick={onSearch}>
                <FaSearch /> Search
              </button>
            </div>
          )}

          {/* dropdowns */}
          {enableFilters && !isMobile && (
            <div className="bakedbot-main-filters-container">
              {/* THC dropdown */}
              <div className="bakedbot-filter-dropdown">
                <select
                  value={filters.thcRange[1]}
                  onChange={(e) => {
                    const v = Number(e.target.value);
                    const map: Record<number, [number, number]> = {
                      100: [0, 100],
                      15: [0, 15],
                      25: [15, 25],
                      50: [25, 50],
                      101: [50, 100],
                    };
                    setRange("thcRange", map[v]);
                  }}
                >
                  <option value={100}>All THC Levels</option>
                  <option value={15}>Under 15%</option>
                  <option value={25}>15% – 25%</option>
                  <option value={50}>25% – 50%</option>
                  <option value={101}>Over 50%</option>
                </select>
              </div>

              {/* price */}
              <div className="bakedbot-filter-dropdown">
                <select
                  value={filters.priceRange[1]}
                  onChange={(e) => {
                    const v = Number(e.target.value);
                    const map: Record<number, [number, number]> = {
                      1000: [0, 1000],
                      20: [0, 20],
                      50: [20, 50],
                      100: [50, 100],
                      999: [100, 1000],
                    };
                    setRange("priceRange", map[v]);
                  }}
                >
                  <option value={1000}>All Prices</option>
                  <option value={20}>$0 – $20</option>
                  <option value={50}>$20 – $50</option>
                  <option value={100}>$50 – $100</option>
                  <option value={999}>$100+</option>
                </select>
              </div>

              {/* sort */}
              <div className="bakedbot-filter-dropdown">
                <select
                  value={sortBy}
                  onChange={(e) => handleSortChange(e.target.value)}
                >
                  <option value="">Recently Added</option>
                  <option value="featured">Featured</option>
                  <option value="price_low_high">Price: Low → High</option>
                  <option value="price_high_low">Price: High → Low</option>
                  <option value="thc_low_high">THC: Low → High</option>
                  <option value="thc_high_low">THC: High → Low</option>
                  <option value="name_az">Name A → Z</option>
                  <option value="name_za">Name Z → A</option>
                </select>
              </div>
            </div>
          )}
          <div className="bakedbot-category-filters-container">
            {[...filterOptions.categories].map((cat) => {
              const active =
                cat === "All Products"
                  ? filters.category.length === 0
                  : filters.category.includes(cat);
              return (
                <button
                  key={cat}
                  className={`bakedbot-category-filter-btn${
                    active ? " active" : ""
                  }`}
                  onClick={() => {
                    if (cat === "All Products") {
                      setFilters({ ...filters, category: [] });
                    } else {
                      toggleFilterValue("category", cat);
                    }
                    setPage(1);
                  }}
                >
                  <span className="bakedbot-category-text">
                    <span className="bakedbot-category-emoji">
                      {emojiForCategory(cat)}
                    </span>
                    {cat}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      </section>
      {/* Mobile category pills */}
      {isMobile && (
        <div className="bakedbot-category-filters-container">
          {[...filterOptions.categories].map((cat) => {
            const active =
              cat === "All Products"
                ? filters.category.length === 0
                : filters.category.includes(cat);
            return (
              <button
                key={cat}
                className={`bakedbot-category-filter-btn${
                  active ? " active" : ""
                }`}
                onClick={() => {
                  if (cat === "All Products") {
                    setFilters({ ...filters, category: [] });
                  } else {
                    toggleFilterValue("category", cat);
                  }
                  setPage(1);
                }}
              >
                <span className="bakedbot-category-text">
                  <span className="bakedbot-category-emoji">
                    {emojiForCategory(cat)}
                  </span>
                  {cat}
                </span>
              </button>
            );
          })}
        </div>
      )}
      {/* results header */}
      <div className="bakedbot-results-header">
        <span className="bakedbot-results-count">
          {loading ? "Loading…" : `${totalProducts} products found`}
        </span>
        <div className="bakedbot-layout-toggle-container">
          <button
            className={`bakedbot-layout-toggle-btn ${
              layout === "grid" ? "active" : ""
            }`}
            onClick={() => setLayout("grid")}
          >
            <BiGridAlt />
          </button>
          <button
            className={`bakedbot-layout-toggle-btn ${
              layout === "list" ? "active" : ""
            }`}
            onClick={() => setLayout("list")}
          >
            <BiListUl />
          </button>
        </div>
      </div>

      {/* product grid */}
      <div className={`bakedbot-product-grid layout-${layout}`}>
        {loading && <div className="bakedbot-loading-state">Loading…</div>}
        {!loading && products.length === 0 && (
          <div className="bakedbot-empty-state">
            No products match your filters.
          </div>
        )}
        {!loading &&
          products.length > 0 &&
          products.map((p) => {
            const sel = selectedVariants[p.meta_sku] ?? p.variants[0];
            const price = sel?.latest_price ?? p.base_price;
            return (
              <div key={p.meta_sku} className="bakedbot-product-card">
                <a
                  href={`${productPageUrl}?id=${p.meta_sku}`}
                  className="bakedbot-product-card-link"
                  aria-label={`View details for ${p.product_name}`}
                >
                  <div className="bakedbot-product-image">
                    <img src={p.image_url} alt={p.product_name} />
                    <div className="bakedbot-product-badges">
                      {p.is_top_pick && (
                        <div className="bakedbot-product-badge top-pick">
                          <FaStar /> Top Pick
                        </div>
                      )}
                      {p.is_featured && !p.is_top_pick && (
                        <div className="bakedbot-product-badge featured">
                          <FaTag /> Featured
                        </div>
                      )}
                      {Number(p.percentage_thc) > 20 && (
                        <div className="bakedbot-product-badge high-thc">
                          <FaFire /> High THC
                        </div>
                      )}
                      {Number(p.percentage_cbd) > 10 && (
                        <div className="bakedbot-product-badge high-cbd">
                          <FaLeaf /> High CBD
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="bakedbot-product-info">
                    <p className="bakedbot-product-name">{p.product_name}</p>
                    <p className="bakedbot-product-brand">
                      {p.brand_name || "Premium Brand"}
                    </p>
                    <div className="bakedbot-product-details">
                      <span>THC {p.percentage_thc ?? 0}%</span>
                      <span>CBD {p.percentage_cbd ?? 0}%</span>
                    </div>
                  </div>
                </a>

                {p.variants.length > 1 && (
                  <div className="bakedbot-variant-selector">
                    <select
                      value={sel?.id ?? ""}
                      onChange={(e) => {
                        const v = p.variants.find(
                          (v) => v.id === e.target.value
                        );
                        if (v) handleVariantSelect(p.meta_sku, v);
                      }}
                    >
                      {p.variants.map((v) => (
                        <option key={v.id} value={v.id}>
                          {v.display_weight} – ${v.latest_price}
                        </option>
                      ))}
                    </select>
                  </div>
                )}

                <div className="bakedbot-product-actions">
                  <span className="bakedbot-product-price">${price}</span>
                  {sel && cart[sel.product_id] ? (
                    <div className="bakedbot-quantity-controls-inline">
                      <button
                        className="bakedbot-quantity-btn-inline"
                        onClick={() => updateQuantity(sel.product_id, -1)}
                      >
                        <FaMinus />
                      </button>
                      <span className="bakedbot-quantity-display-inline">
                        {cart[sel.product_id].quantity}
                      </span>
                      <button
                        className="bakedbot-quantity-btn-inline"
                        onClick={() => updateQuantity(sel.product_id, 1)}
                      >
                        <FaPlus />
                      </button>
                    </div>
                  ) : (
                    <button
                      className="bakedbot-add-to-cart-btn"
                      disabled={!sel}
                      onClick={() => handleAddToCart(p)}
                    >
                      Add to Cart
                    </button>
                  )}
                </div>
              </div>
            );
          })}
      </div>

      {/* pagination */}
      {totalPages > 1 && !loading && (
        <div className="bakedbot-pagination-container">
          <button
            className="bakedbot-pagination-btn"
            disabled={page === 1}
            onClick={() => setPage(page - 1)}
          >
            Previous
          </button>
          {Array.from({ length: totalPages }).map((_, i) => {
            const p = i + 1;
            if (totalPages > 5 && Math.abs(p - page) > 2) return null;
            return (
              <button
                key={p}
                className={`bakedbot-pagination-btn ${
                  p === page ? "active" : ""
                }`}
                onClick={() => setPage(p)}
              >
                {p}
              </button>
            );
          })}
          <button
            className="bakedbot-pagination-btn"
            disabled={page === totalPages}
            onClick={() => setPage(page + 1)}
          >
            Next
          </button>
        </div>
      )}

      {/* VIP signup -------------------------------------------------- */}
      {!vipSubmitted ? (
        <div className="bakedbot-vip-signup-section">
          <div className="bakedbot-vip-signup-container">
            <div className="bakedbot-vip-header">
              <h3>🌿 Join Our VIP Club</h3>
              <p>
                Get exclusive AI recommendations, member pricing, and priority
                delivery access
              </p>
            </div>
            <form onSubmit={submitVip} className="bakedbot-vip-signup-form">
              <div className="bakedbot-vip-form-fields">
                <input
                  placeholder="First Name"
                  required
                  disabled={vipSubmitting}
                  value={vipForm.firstName}
                  onChange={(e) =>
                    setVipForm({ ...vipForm, firstName: e.target.value })
                  }
                />
                <input
                  type="email"
                  placeholder="Email"
                  required
                  disabled={vipSubmitting}
                  value={vipForm.email}
                  onChange={(e) =>
                    setVipForm({ ...vipForm, email: e.target.value })
                  }
                />
                <input
                  placeholder="Phone"
                  required
                  disabled={vipSubmitting}
                  value={vipForm.phone}
                  onChange={(e) =>
                    setVipForm({ ...vipForm, phone: e.target.value })
                  }
                />
              </div>
              <button
                type="submit"
                className="bakedbot-vip-activate-btn"
                disabled={vipSubmitting}
              >
                {vipSubmitting ? "Activating…" : "🚀 Activate VIP Access"}
              </button>
            </form>
          </div>
        </div>
      ) : (
        <div className="bakedbot-vip-success-section">
          <div className="bakedbot-vip-success-container">
            <div className="bakedbot-vip-success-icon">✅</div>
            <h3>Welcome to VIP Club!</h3>
            <p>Check your email for exclusive offers and member benefits.</p>
          </div>
        </div>
      )}

      {/* filters modal ------------------------------------------- */}
      {enableFilters && isMobile && showFiltersModal && (
        <div className="bakedbot-mobile-filters-modal">
          <div
            className="bakedbot-modal-overlay"
            onClick={() => setShowFiltersModal(false)}
          />
          <div className="bakedbot-mobile-filters-content">
            <div className="bakedbot-mobile-filters-header">
              <h3>Filters</h3>
              <button onClick={() => setShowFiltersModal(false)}>
                <FaTimes />
              </button>
            </div>

            <div className="bakedbot-mobile-filters-body">
              {/* THC range pills */}
              <div className="bakedbot-mobile-filter-section">
                <h4>THC Level</h4>
                <div className="bakedbot-mobile-range-buttons">
                  {[
                    { label: "All THC", range: [0, 100] },
                    { label: "Under 15%", range: [0, 15] },
                    { label: "15% – 25%", range: [15, 25] },
                    { label: "25% – 50%", range: [25, 50] },
                    { label: "Over 50%", range: [50, 100] },
                  ].map(({ label, range }: any) => {
                    const active =
                      filters.thcRange[0] === range[0] &&
                      filters.thcRange[1] === range[1];
                    return (
                      <button
                        key={label}
                        className={`bakedbot-mobile-range-btn${
                          active ? " active" : ""
                        }`}
                        onClick={() => setRange("thcRange", range)}
                      >
                        {label}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* price range pills */}
              <div className="bakedbot-mobile-filter-section">
                <h4>Price Range</h4>
                <div className="bakedbot-mobile-range-buttons">
                  {[
                    { label: "All Prices", range: [0, 1000] },
                    { label: "$0 – $20", range: [0, 20] },
                    { label: "$20 – $50", range: [20, 50] },
                    { label: "$50 – $100", range: [50, 100] },
                    { label: "$100+", range: [100, 1000] },
                  ].map(({ label, range }: any) => {
                    const active =
                      filters.priceRange[0] === range[0] &&
                      filters.priceRange[1] === range[1];
                    return (
                      <button
                        key={label}
                        className={`bakedbot-mobile-range-btn${
                          active ? " active" : ""
                        }`}
                        onClick={() => setRange("priceRange", range)}
                      >
                        {label}
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="bakedbot-mobile-filters-footer">
              <button
                className="bakedbot-mobile-clear-filters-btn"
                onClick={() => {
                  setFilters(DEFAULT_FILTERS);
                  setPage(1);
                }}
              >
                Clear All
              </button>
              <button
                className="bakedbot-mobile-apply-filters-btn"
                onClick={() => setShowFiltersModal(false)}
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeadlessMenu;
