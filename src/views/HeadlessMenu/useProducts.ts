import { useCallback, useEffect, useState } from "react";
import {
  bakedBotApiRequest,
  FilterOptions,
  GroupedProduct,
} from "../../utils/api";
import { DEFAULT_FILTERS, Filters } from "../../utils/menuhelpers";

interface UseProductsArgs {
  productsPerPage: number;
  initialFilters?: Filters;
  initialSearch?: string;
}

export const useProducts = ({
  productsPerPage,
  initialFilters,
  initialSearch,
}: UseProductsArgs) => {
  /* ─── state ───────────────────────────────────────────── */
  const [filters, setFilters] = useState<Filters>(
    initialFilters ?? DEFAULT_FILTERS
  );
  const [searchQuery, setSearchQuery] = useState(initialSearch ?? "");
  const [sortBy, setSortBy] = useState("");
  const [page, setPage] = useState(1);

  const [data, setData] = useState<GroupedProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pages, setPages] = useState(1);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    brands: [],
    categories: [],
    subcategories: [],
  });
  const [hydrated, setHydrated] = useState(false);

  /* ─── helpers ─────────────────────────────────────────── */
  const buildQuery = useCallback(
    (p: number) => {
      const q: Record<string, string | number> = {
        page: p,
        per_page: productsPerPage,
      };
      if (searchQuery.trim()) q.q = searchQuery.trim();
      if (filters.category.length) q.category = filters.category.join(",");
      if (filters.subcategory.length)
        q.subcategory = filters.subcategory.join(",");
      if (filters.brands.length) q.brand = filters.brands.join(",");
      if (filters.priceRange[0] > 0) q.min_price = filters.priceRange[0];
      if (filters.priceRange[1] < 1000) q.max_price = filters.priceRange[1];
      if (filters.thcRange[0] > 0) q.min_thc = filters.thcRange[0];
      if (filters.thcRange[1] < 100) q.max_thc = filters.thcRange[1];
      if (filters.cbdRange[0] > 0) q.min_cbd = filters.cbdRange[0];
      if (filters.cbdRange[1] < 100) q.max_cbd = filters.cbdRange[1];
      if (sortBy) q.sort = sortBy;
      return q;
    },
    [filters, searchQuery, sortBy, productsPerPage]
  );

  const updateUrl = useCallback(
    (params: Record<string, string | number>) => {
      if (typeof window === "undefined" || !hydrated) return;
      const u = new URL(window.location.href);

      // Instead of clearing all search params, only update the ones we manage
      // Remove our managed parameters first
      const managedParams = [
        "menu_page",
        "per_page",
        "q",
        "category",
        "subcategory",
        "brand",
        "min_price",
        "max_price",
        "min_thc",
        "max_thc",
        "min_cbd",
        "max_cbd",
        "sort",
      ];
      managedParams.forEach((param) => u.searchParams.delete(param));

      // Add the new parameters
      Object.entries(params).forEach(([k, v]) => {
        if (k === "page") {
          u.searchParams.set("menu_page", String(v));
        } else {
          u.searchParams.set(k, String(v));
        }
      });

      window.history.replaceState({}, "", u.toString());
    },
    [hydrated]
  );

  /* ─── fetch products ───────────────────────────────────── */
  const fetchPage = useCallback(
    async (p: number) => {
      setLoading(true);
      try {
        const q = buildQuery(p);
        const res = await bakedBotApiRequest("GET", "/public/products", q);

        setData(res.products);
        setTotal(res.pagination.total);
        setPages(res.pagination.total_pages ?? res.pagination.totalPages ?? 1);
        setPage(p);
        updateUrl(q);
      } catch (err) {
        console.error("fetch products", err);
      } finally {
        setLoading(false);
      }
    },
    [buildQuery, updateUrl]
  );

  /* ─── first run: hydrate from URL ───────────────────────── */
  useEffect(() => {
    if (typeof window === "undefined") return;

    /* helpers */
    const list = (v?: string | null) => (v ? v.split(",") : []);
    const num = (v?: string | null, d = 0) => (v ? Number(v) : d);

    const url = new URL(window.location.href);
    const sp = url.searchParams;

    /* 1️⃣  raw values from URL */
    const rawFilters: Filters = {
      category: list(sp.get("category")),
      subcategory: list(sp.get("subcategory")),
      brands: list(sp.get("brand")),
      priceRange: [num(sp.get("min_price"), 0), num(sp.get("max_price"), 1000)],
      thcRange: [num(sp.get("min_thc"), 0), num(sp.get("max_thc"), 100)],
      cbdRange: [num(sp.get("min_cbd"), 0), num(sp.get("max_cbd"), 100)],
    };
    const rawSearch = sp.get("q") ?? sp.get("search") ?? "";
    const rawSort = sp.get("sort") ?? "";
    const startPage = num(sp.get("menu_page"), 1);

    // ① seed state **only**
    setFilters(rawFilters);
    setSearchQuery(rawSearch);
    setSortBy(rawSort);
    setPage(startPage);

    // ② load dropdown data
    bakedBotApiRequest("GET", "/public/products/filters")
      .then(setFilterOptions)
      .catch(console.error);

    // ③ flag that hydration is finished
    setHydrated(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* ─── reactive reload ──────────────────────────────────── */
  useEffect(() => {
    if (hydrated) {
      // When filters or search change, go to page 1
      fetchPage(page);
    }
  }, [filters, searchQuery, sortBy, page, hydrated, fetchPage]);

  /* ─── return API ───────────────────────────────────────── */
  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    page,
    setPage,
    products: data,
    totalProducts: total,
    totalPages: pages,
    loading,
    filterOptions,
  };
};
