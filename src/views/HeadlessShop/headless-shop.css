/* HeadlessShop Component Styles */
.headless-shop {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* Navigation breadcrumb */
.shop-navigation {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.nav-home-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: white;
  border: 1px solid #065f46;
  border-radius: 0.375rem;
  color: #065f46;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.nav-home-btn:hover {
  background: #065f46;
  color: white;
}

.nav-filter {
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.nav-filter strong {
  color: #1f2937;
}

/* Responsive design */
@media (max-width: 768px) {
  .shop-navigation {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .nav-filter {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .shop-navigation {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .nav-home-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
} 