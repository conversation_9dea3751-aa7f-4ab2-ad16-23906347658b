import React, { useState, useCallback } from "react";
import { GroupedProduct } from "../../utils/api";
import HeadlessHomepage from "../HeadlessHomepage";
import HeadlessMenu from "../HeadlessMenu";
import "./headless-shop.css";

interface HeadlessShopProps {
  apiKey?: string;
  enableFilters?: boolean;
  enableSearch?: boolean;
  enableCart?: boolean;
  layout?: "grid" | "list";
  productsPerPage?: number;
  theme?: "light" | "dark" | "custom";
  primaryColor?: string;
  secondaryColor?: string;
  initialProducts?: GroupedProduct[];
  isProgressiveEnhancement?: boolean;
  containerId?: string;
  featuredProductsCount?: number;
  popularCategoriesCount?: number;
  showPromotion?: boolean;
  promotionTitle?: string;
  promotionSubtitle?: string;
  promotionImageUrl?: string;
  // Navigation props
  defaultView?: "homepage" | "menu";
  enableHomepage?: boolean;
}

interface NavigationFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  search?: string;
}

export const HeadlessShop: React.FC<HeadlessShopProps> = ({
  apiKey,
  enableFilters = true,
  enableSearch = true,
  enableCart = true,
  layout = "grid",
  productsPerPage = 12,
  theme = "light",
  primaryColor = "#065f46",
  secondaryColor = "#10b981",
  initialProducts = [],
  isProgressiveEnhancement = false,
  containerId,
  featuredProductsCount = 8,
  popularCategoriesCount = 6,
  showPromotion = true,
  promotionTitle = "Premium Cannabis Products",
  promotionSubtitle = "Discover our curated selection of the finest cannabis products",
  promotionImageUrl,
  defaultView = "homepage",
  enableHomepage = true,
}) => {
  // Navigation state
  const [currentView, setCurrentView] = useState<"homepage" | "menu">(
    enableHomepage ? defaultView : "menu"
  );
  const [menuFilters, setMenuFilters] = useState<NavigationFilters>({});
  const [selectedProduct, setSelectedProduct] = useState<GroupedProduct | null>(
    null
  );

  // Navigation handlers
  const navigateToMenu = useCallback((filters?: NavigationFilters) => {
    console.log("BakedBot Debug: Navigating to menu with filters:", filters);
    if (filters) {
      setMenuFilters(filters);
    }
    setCurrentView("menu");
  }, []);

  const navigateToHomepage = useCallback(() => {
    console.log("BakedBot Debug: Navigating to homepage");
    setCurrentView("homepage");
    setMenuFilters({});
    setSelectedProduct(null);
  }, []);

  const handleProductClick = useCallback((product: GroupedProduct) => {
    console.log("BakedBot Debug: Product clicked:", product.product_name);
    setSelectedProduct(product);
    setCurrentView("menu");
  }, []);

  // If homepage is disabled, always show menu
  if (!enableHomepage) {
    return (
      <HeadlessMenu
        enableSearch={enableSearch}
        enableCart={enableCart}
        layout={layout}
        productsPerPage={productsPerPage}
        primaryColor={primaryColor}
        secondaryColor={secondaryColor}
        containerId={containerId}
      />
    );
  }

  // Render based on current view
  if (currentView === "homepage") {
    return (
      <HeadlessHomepage
        apiKey={apiKey}
        enableCart={enableCart}
        theme={theme}
        primaryColor={primaryColor}
        secondaryColor={secondaryColor}
        onNavigateToMenu={navigateToMenu}
        onNavigateToProduct={handleProductClick}
        containerId={containerId}
        featuredProductsCount={featuredProductsCount}
        popularCategoriesCount={popularCategoriesCount}
        showPromotion={showPromotion}
        promotionTitle={promotionTitle}
        promotionSubtitle={promotionSubtitle}
        promotionImageUrl={promotionImageUrl}
      />
    );
  }

  return (
    <div className="headless-shop">
      {/* Menu component with applied filters */}
      <HeadlessMenu
        enableSearch={enableSearch}
        enableCart={enableCart}
        layout={layout}
        productsPerPage={productsPerPage}
        primaryColor={primaryColor}
        secondaryColor={secondaryColor}
        containerId={containerId}
      />
    </div>
  );
};

export default HeadlessShop;
