/* ProductDetailsPage Component Styles */
.bakedbot-product-details-page {
  --primary-color: #065f46;
  --secondary-color: #10b981;
  --background-color: #ffffff;
  --text-color: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --hover-color: #f3f4f6;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  background-color: #f9fafb;
  color: var(--text-color);
  min-height: 100vh;
}

.bakedbot-product-details-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem 2rem 2rem;
  background: transparent;
}

.bakedbot-product-details-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem 0 1.5rem 0;
  margin-bottom: 2rem;
}

.bakedbot-product-details-header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-color);
  line-height: 1.2;
}

.bakedbot-back-button {
  background: var(--primary-color);
  border: none;
  font-size: 0.875rem;
  color: white;
  cursor: pointer;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;
}

.bakedbot-back-button:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.bakedbot-product-details-content {
  padding: 0;
  background: var(--background-color);
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
  padding: 2rem;
}

.bakedbot-product-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.bakedbot-product-image-section {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.bakedbot-product-detail-image {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 1rem;
  box-shadow: var(--shadow-md);
  object-fit: cover;
}

.bakedbot-product-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.bakedbot-product-header h3 {
  margin: 0 0 0.75rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-color);
  line-height: 1.2;
}

.bakedbot-brand-name {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: var(--primary-color);
  font-weight: 600;
}

.bakedbot-category-info {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.bakedbot-category, .bakedbot-subcategory {
  padding: 0.5rem 1rem;
  background: var(--hover-color);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color);
}

.bakedbot-subcategory {
  background: var(--primary-color);
  color: white !important;
  border-color: var(--primary-color);
}

.bakedbot-cannabinoid-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  padding: 2rem;
  background: linear-gradient(135deg, var(--hover-color), var(--background-color));
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  box-shadow: var(--shadow);
}

.bakedbot-cannabinoid {
  text-align: center;
  font-size: 1.125rem;
}

.bakedbot-cannabinoid strong {
  display: block;
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.bakedbot-variants-section h4,
.bakedbot-description-section h4 {
  margin: 1em 0 1.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.bakedbot-variants-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bakedbot-variant-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  background: var(--background-color);
  transition: all 0.2s;
}

.bakedbot-variant-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.bakedbot-variant-item.active {
  border-color: var(--primary-color);
  background: rgba(6, 95, 70, 0.05);
  box-shadow: var(--shadow-md);
}

.bakedbot-variant-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.bakedbot-variant-weight {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1.125rem;
}

.bakedbot-variant-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.bakedbot-quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--hover-color);
  border-radius: 0.5rem;
  padding: 0.5rem;
}

.bakedbot-quantity-btn {
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  background: var(--primary-color);
  border-radius: 0.375rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.bakedbot-quantity-btn:hover {
  background: var(--secondary-color);
  transform: scale(1.05);
}

.bakedbot-quantity-display {
  font-weight: 600;
  color: var(--text-color);
  min-width: 2rem;
  text-align: center;
  font-size: 1rem;
}

.bakedbot-add-variant-btn {
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white !important;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
}

.bakedbot-add-variant-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.bakedbot-add-variant-btn svg {
  color: white;
}

.bakedbot-description-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.bakedbot-description-section > div {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: flex-start;
}

.bakedbot-description-section > div > img {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: var(--shadow-md);
  flex: 1 1 calc(50% - 0.75rem);
  object-fit: cover;
}

.bakedbot-description-section > div > p {
  flex-basis: 100%;
  margin-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.bakedbot-loading-state,
.bakedbot-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  font-size: 1.25rem;
  color: var(--text-secondary);
  text-align: center;
}

.bakedbot-description-section h4 {
  margin-bottom: 1rem;
  font-size: 1.25rem;
  color: #333;
}

.bakedbot-short-description-section {
  background-color: #f9f9f9;
  border-left: 4px solid #4a90e2;
  margin: 1.5rem 0;
  padding: 1rem 1.5rem;
  font-style: italic;
  color: #555;
}

.bakedbot-short-description-section p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.6;
}

.bakedbot-related-products-section {
  margin-top: 2rem;
  padding-top: 2rem;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .bakedbot-product-details-page {
    padding-top: 70px;
  }

  .bakedbot-product-details-container {
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 0.5rem;
  }

  .bakedbot-product-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    margin-bottom: 1.5rem;
  }

  .bakedbot-product-details-header h1 {
    font-size: 2rem;
    order: 2;
  }

  .bakedbot-back-button {
    order: 1;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }

  .bakedbot-product-details-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .bakedbot-product-header h3 {
    font-size: 1.5rem;
  }

  .bakedbot-brand-name {
    font-size: 1.125rem;
  }

  .bakedbot-cannabinoid-info {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }

  .bakedbot-variant-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem;
  }

  .bakedbot-variant-info {
    align-self: stretch;
  }

  .bakedbot-quantity-controls {
    align-self: stretch;
    justify-content: center;
  }

  .bakedbot-add-variant-btn {
    width: 100%;
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .bakedbot-product-details-container {
    margin: 0.5rem;
    padding: 1rem;
  }

  .bakedbot-product-details-header h1 {
    font-size: 1.75rem;
  }

  .bakedbot-product-header h3 {
    font-size: 1.25rem;
  }

  .bakedbot-category-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .bakedbot-cannabinoid-info {
    padding: 1rem;
  }

  .bakedbot-variant-item {
    padding: 1rem;
  }

  .bakedbot-product-details-content {
    padding: 1.5rem;
  }
} 