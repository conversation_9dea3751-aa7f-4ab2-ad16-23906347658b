import React, { useEffect, useState } from "react";
import {
  fetchSingleProduct,
  GroupedProduct,
  ProductVariant,
} from "../../utils/api";
import { useCart } from "../ChatWidget/CartContext";
import { SharedHeader } from "../../components/SharedHeader";
import {
  FaPlus,
  FaMinus,
  FaTimes,
  FaCheck,
  FaStar,
  FaTag,
  FaFire,
  FaLeaf,
} from "react-icons/fa";
import "./ProductDetailsPage.css";

const ProductDetailsPage: React.FC = () => {
  // Get SKU from URL query parameters (WordPress compatible)
  const getSKUFromURL = () => {
    // Parse query parameters manually for WordPress compatibility
    const queryString = window.location.search;
    if (!queryString) return null;

    // Remove the ? and split by &
    const params = queryString.substring(1).split("&");

    // Look for id or sku parameter
    for (const param of params) {
      const [key, value] = param.split("=");
      if (key === "id" || key === "sku") {
        return decodeURIComponent(value);
      }
    }

    return null;
  };

  const sku = getSKUFromURL();
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState<GroupedProduct | null>(null);
  const [selected, setSelected] = useState<ProductVariant | null>(null);

  const { cart, addToCart, updateQuantity, removeFromCart } = useCart();

  /* initial fetch */
  useEffect(() => {
    if (!sku) {
      // Go back to previous page or to menu
      window.history.back();
      return;
    }

    console.log("Fetching product with SKU:", sku);

    fetchSingleProduct(sku)
      .then((p) => {
        console.log("Product data received:", p);
        console.log("Variants:", p?.variants);

        setProduct(p);
        setSelected(p?.variants?.[0] ?? null);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching product:", error);
        setLoading(false);
      });
  }, [sku]);

  if (loading) return <div className="bakedbot-loading-state">Loading…</div>;
  if (!product) {
    console.log("No product found for SKU:", sku);
    return <div className="bakedbot-empty-state">Product not found.</div>;
  }

  console.log("Rendering product:", product.product_name);
  console.log("Selected variant:", selected);

  /* re-used helper to add to cart */
  const addSelected = () => {
    if (!selected) return;
    addToCart({
      id: selected.product_id,
      product_id: selected.product_id,
      cann_sku_id: selected.product_id,
      product_name: `${product.product_name} - ${selected.display_weight}`,
      brand_name: product.brand_name,
      category: product.category,
      image_url: product.image_url,
      latest_price: selected.latest_price,
      percentage_thc: product.percentage_thc ?? null,
      percentage_cbd: product.percentage_cbd ?? null,
      meta_sku: product.meta_sku,
      medical: selected.medical,
      recreational: selected.recreational,
      brand_id: null,
      url: window.location.href,
      raw_product_name: product.product_name,
      raw_weight_string: selected.display_weight,
      display_weight: selected.display_weight,
      raw_product_category: product.category,
      raw_subcategory: product.subcategory ?? null,
      subcategory: product.subcategory ?? null,
      product_tags: null,
      mg_thc: null,
      mg_cbd: null,
      quantity_per_package: null,
      menu_provider: "",
      retailer_id: "",
      updated_at: "",
      price: selected.latest_price,
      description: product.description ?? "",
    });
  };

  /* cart helpers */
  const qty = selected ? cart[selected.product_id]?.quantity ?? 0 : 0;

  return (
    <>
      <SharedHeader
        cartCount={Object.keys(cart).length}
        onCartClick={() => {}}
        onSearch={() => {}}
      />

      <div className="bakedbot-product-details-page">
        <div className="bakedbot-product-details-container">
          <div className="bakedbot-product-details-content">
            <div className="bakedbot-product-details-grid">
              <div className="bakedbot-product-image-section">
                <img
                  src={product.image_url}
                  alt={product.product_name}
                  className="bakedbot-product-detail-image"
                />
              </div>

              <div className="bakedbot-product-info-section">
                {/* header */}
                <div className="bakedbot-product-header">
                  <h3>{product.product_name}</h3>
                  {product.brand_name && (
                    <p className="bakedbot-brand-name">{product.brand_name}</p>
                  )}
                  <div className="bakedbot-category-info">
                    <span className="bakedbot-category">
                      {product.category}
                    </span>
                    {product.subcategory && (
                      <span className="bakedbot-subcategory">
                        {product.subcategory}
                      </span>
                    )}
                  </div>
                </div>

                {/* THC / CBD block */}
                <div className="bakedbot-cannabinoid-info">
                  <div className="bakedbot-cannabinoid">
                    <strong>THC:</strong> {product.percentage_thc ?? 0}%
                  </div>
                  <div className="bakedbot-cannabinoid">
                    <strong>CBD:</strong> {product.percentage_cbd ?? 0}%
                  </div>
                </div>

                {/* variants */}
                <div className="bakedbot-variants-section">
                  <h4>Available Options</h4>
                  <div className="bakedbot-variants-list">
                    {product.variants?.map((v) => (
                      <div
                        key={v.id}
                        className={`bakedbot-variant-item${
                          String(selected?.id) === String(v.id) ? " active" : ""
                        }`}
                      >
                        <div className="bakedbot-variant-info">
                          <span className="bakedbot-variant-weight">
                            {v.display_weight || "Standard Size"}
                          </span>
                          <span className="bakedbot-variant-price">
                            ${v.latest_price}
                          </span>
                        </div>

                        {qty > 0 && String(selected?.id) === String(v.id) ? (
                          <div className="bakedbot-quantity-controls">
                            <button
                              className="bakedbot-quantity-btn"
                              onClick={() => updateQuantity(v.product_id, -1)}
                            >
                              <FaMinus />
                            </button>
                            <span className="bakedbot-quantity-display">
                              {qty}
                            </span>
                            <button
                              className="bakedbot-quantity-btn"
                              onClick={() => updateQuantity(v.product_id, 1)}
                            >
                              <FaPlus />
                            </button>
                          </div>
                        ) : (
                          <button
                            className="bakedbot-add-variant-btn"
                            onClick={() => {
                              setSelected(v);
                              addSelected();
                            }}
                          >
                            Add to Cart
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* optional sections (tags, effects, etc.) */}
              </div>
            </div>

            {product.product_short_description && (
              <div className="bakedbot-short-description-section">
                <p>{product.product_short_description}</p>
              </div>
            )}

            {product.description && (
              <div className="bakedbot-description-section">
                <h4>Description</h4>
                <div
                  dangerouslySetInnerHTML={{ __html: product.description }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDetailsPage;
