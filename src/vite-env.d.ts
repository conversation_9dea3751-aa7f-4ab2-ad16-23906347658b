/// <reference types="vite/client" />

interface ImportMetaEnv {
  // BakedBot API Configuration
  readonly VITE_BAKED_BOT_API: string;
  readonly VITE_BAKED_BOT_API_KEY: string;

  // Chat API Configuration
  readonly VITE_BASE_URL: string;

  // Firebase Configuration
  readonly VITE_FIREBASE_API_KEY: string;
  readonly VITE_FIREBASE_AUTH_DOMAIN: string;
  readonly VITE_FIREBASE_PROJECT_ID: string;
  readonly VITE_FIREBASE_STORAGE_BUCKET: string;
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID: string;
  readonly VITE_FIREBASE_MEASUREMENT_ID: string;
  readonly VITE_FIREBASE_APP_ID: string;

  // Optional Configuration
  readonly VITE_DEBUG: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
