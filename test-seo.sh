#!/bin/bash

# SEO Testing Script for BakedBot Menu Pagination
# Usage: ./test-seo.sh https://yoursite.com/dispensary-menu

if [ -z "$1" ]; then
    echo "Usage: $0 <menu-page-url>"
    echo "Example: $0 https://yoursite.com/dispensary-menu"
    exit 1
fi

BASE_URL="$1"
echo "🔍 Testing SEO implementation for: $BASE_URL"
echo "=================================================="

# Test Page 1 (no parameters)
echo ""
echo "📄 Testing Page 1..."
echo "URL: $BASE_URL"
curl -s "$BASE_URL" | grep -E 'rel="(prev|next|canonical|first|last)"' | while read line; do
    echo "  ✓ $line"
done

# Test Page 2 (with menu_page parameter)
echo ""
echo "📄 Testing Page 2..."
PAGE2_URL="${BASE_URL}?menu_page=2"
echo "URL: $PAGE2_URL"
curl -s "$PAGE2_URL" | grep -E 'rel="(prev|next|canonical|first|last)"' | while read line; do
    echo "  ✓ $line"
done

# Test meta description
echo ""
echo "📝 Meta Description Check..."
curl -s "$BASE_URL" | grep -o '<meta name="description"[^>]*>' | head -1

# Test Open Graph tags
echo ""
echo "📱 Open Graph Tags..."
curl -s "$BASE_URL" | grep -E 'property="og:(title|description|type|image)"' | while read line; do
    echo "  ✓ $line"
done

# Test structured data
echo ""
echo "📊 Structured Data Check..."
if curl -s "$BASE_URL" | grep -q 'application/ld+json'; then
    echo "  ✓ JSON-LD structured data found"
    curl -s "$BASE_URL" | grep -A5 -B1 'application/ld+json' | head -10
else
    echo "  ❌ No structured data found"
fi

# Test pagination HTML structure
echo ""
echo "🔗 Pagination Links Check..."
curl -s "$PAGE2_URL" | grep -E 'class="pagination-btn.*rel="' | while read line; do
    echo "  ✓ $line"
done

echo ""
echo "✅ SEO Testing Complete!"
echo "💡 Next steps:"
echo "   1. Use Google Search Console URL Inspector"
echo "   2. Run Screaming Frog SEO Spider"
echo "   3. Check Google PageSpeed Insights"
echo "   4. Validate with W3C Markup Validator" 