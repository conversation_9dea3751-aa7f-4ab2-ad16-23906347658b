import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { resolve } from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react()],
  build: {
    // Use terser for more advanced minification options
    minify: "terser",
    terserOptions: {
      compress: {
        // Only drop console logs in production
        drop_console: mode === "production",
      },
    },
    // Disable code splitting entirely
    rollupOptions: {
      input: {
        main: resolve(__dirname, "index.html"),
      },
      external: ["react", "react-dom", "react-router-dom"],
      output: {
        // Ensure IIFE format for browser compatibility
        format: "iife",
        // Specify global variable names to prevent conflicts
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
          "react-router-dom": "ReactRouterDOM",
        },
        // Ensure all code is in a single bundle
        inlineDynamicImports: true,
        manualChunks: undefined,
        // Rename output files with simple names
        entryFileNames: "assets/js/bundle.js",
        chunkFileNames: "assets/js/[name].js",
        assetFileNames: "assets/[ext]/[name].[ext]",
      },
    },
    // Force a clean dist folder on each build
    emptyOutDir: true,
    // Output to dist folder
    outDir: "dist",
  },
}));
