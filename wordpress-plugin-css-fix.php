<?php
/**
 * BakedBot Chatbot CSS Isolation
 * 
 * This file contains functions to prevent WordPress theme styles from overriding
 * the BakedBot chatbot styles. Integrate these functions into your main plugin file.
 */

/**
 * Enhanced CSS registration with isolation techniques
 * 
 * Replace the existing CSS registration in your plugin with this function
 */
function bakedbot_register_isolated_styles() {
    // Get plugin path and URL
    $plugin_path = plugin_dir_path(__FILE__);
    $plugin_url = plugin_dir_url(__FILE__);
    
    // Register main CSS files from dist folder with high priority (100)
    $css_files = glob($plugin_path . 'dist/assets/css/*.css');
    if (!empty($css_files)) {
        foreach ($css_files as $css_file) {
            $filename = basename($css_file);
            $handle = 'bakedbot-' . pathinfo($filename, PATHINFO_FILENAME);
            
            // Register with high priority
            wp_register_style(
                $handle,
                $plugin_url . 'dist/assets/css/' . $filename,
                array(),
                filemtime($css_file)
            );
            
            // Enqueue with high priority
            add_action('wp_enqueue_scripts', function() use ($handle) {
                wp_enqueue_style($handle);
            }, 100);
            
            // Debug CSS loading
            wp_add_inline_script('bakedbot-debug', 'console.log("BakedBot Debug: Enqueued CSS with isolation: ' . $filename . '");');
        }
    }
    
    // Add CSS namespace prefix to all selectors
    add_action('wp_head', 'bakedbot_add_css_namespace_prefix', 999);
    
    // Add critical inline styles with !important
    add_action('wp_head', 'bakedbot_add_critical_styles', 1000);
}

/**
 * Add CSS namespace prefix to ensure all selectors are properly scoped
 */
function bakedbot_add_css_namespace_prefix() {
    ?>
    <style id="bakedbot-namespace-fix">
        /* Ensure all BakedBot elements use proper box-sizing */
        #bakedbot-container,
        #bakedbot-container * {
            box-sizing: border-box !important;
        }
        
        /* Reset some basic properties for all elements inside the container */
        #bakedbot-container div,
        #bakedbot-container span,
        #bakedbot-container p,
        #bakedbot-container a,
        #bakedbot-container button,
        #bakedbot-container input,
        #bakedbot-container textarea {
            margin: 0 !important;
            padding: 0 !important;
            font-family: inherit !important;
            font-size: inherit !important;
            line-height: inherit !important;
            color: inherit !important;
            text-transform: none !important;
            letter-spacing: normal !important;
        }
    </style>
    <?php
}

/**
 * Add critical inline styles with !important flags
 * These styles will override any theme styles
 */
function bakedbot_add_critical_styles() {
    // Get options
    $options = get_option('bakedbot_chatbot_options', array());
    $primary_color = isset($options['primary_color']) ? $options['primary_color'] : '#22AD85';
    $secondary_color = isset($options['secondary_color']) ? $options['secondary_color'] : '#24504A';
    $background_color = isset($options['background_color']) ? $options['background_color'] : '#FFFFFF';
    $text_color = isset($options['text_color']) ? $options['text_color'] : '#2C2C2C';
    
    ?>
    <style id="bakedbot-critical-styles">
        /* CSS Variables for consistent styling */
        #bakedbot-container {
            --bakedbot-primary-color: <?php echo esc_attr($primary_color); ?> !important;
            --bakedbot-secondary-color: <?php echo esc_attr($secondary_color); ?> !important;
            --bakedbot-background-color: <?php echo esc_attr($background_color); ?> !important;
            --bakedbot-text-color: <?php echo esc_attr($text_color); ?> !important;
            --bakedbot-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
            --bakedbot-font-size: 16px !important;
            --bakedbot-line-height: 1.5 !important;
            --bakedbot-border-radius: 4px !important;
            
            /* Base container styles */
            font-family: var(--bakedbot-font-family) !important;
            font-size: var(--bakedbot-font-size) !important;
            line-height: var(--bakedbot-line-height) !important;
            color: var(--bakedbot-text-color) !important;
            background-color: var(--bakedbot-background-color) !important;
            
            /* Ensure proper z-index for floating widget */
            z-index: 999999 !important;
            
            /* Reset any theme margins/paddings */
            margin: 0 !important;
            padding: 0 !important;
            
            /* Prevent theme styles from affecting dimensions */
            width: auto !important;
            height: auto !important;
            max-width: none !important;
            max-height: none !important;
        }
        
        /* Highly specific selectors for buttons */
        html body #page #content #primary #main article .entry-content #bakedbot-container .bakedbot-button,
        #bakedbot-container .bakedbot-button,
        #bakedbot-container button[class*="bakedbot"],
        #bakedbot-container [class*="bakedbot"] button {
            background-color: var(--bakedbot-primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: var(--bakedbot-border-radius) !important;
            padding: 8px 16px !important;
            font-family: var(--bakedbot-font-family) !important;
            font-size: var(--bakedbot-font-size) !important;
            line-height: var(--bakedbot-line-height) !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            box-shadow: none !important;
            text-shadow: none !important;
            outline: none !important;
            cursor: pointer !important;
        }
        
        /* Ensure text inputs have consistent styling */
        #bakedbot-container input[type="text"],
        #bakedbot-container textarea,
        #bakedbot-container [class*="bakedbot"] input[type="text"],
        #bakedbot-container [class*="bakedbot"] textarea {
            background-color: white !important;
            color: var(--bakedbot-text-color) !important;
            border: 1px solid #ddd !important;
            border-radius: var(--bakedbot-border-radius) !important;
            padding: 8px !important;
            font-family: var(--bakedbot-font-family) !important;
            font-size: var(--bakedbot-font-size) !important;
            line-height: var(--bakedbot-line-height) !important;
            width: 100% !important;
            box-shadow: none !important;
            outline: none !important;
        }
        
        /* Ensure links have consistent styling */
        #bakedbot-container a,
        #bakedbot-container [class*="bakedbot"] a {
            color: var(--bakedbot-primary-color) !important;
            text-decoration: none !important;
            background-color: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }
        
        /* Ensure headings have consistent styling */
        #bakedbot-container h1,
        #bakedbot-container h2,
        #bakedbot-container h3,
        #bakedbot-container h4,
        #bakedbot-container h5,
        #bakedbot-container h6,
        #bakedbot-container [class*="bakedbot"] h1,
        #bakedbot-container [class*="bakedbot"] h2,
        #bakedbot-container [class*="bakedbot"] h3,
        #bakedbot-container [class*="bakedbot"] h4,
        #bakedbot-container [class*="bakedbot"] h5,
        #bakedbot-container [class*="bakedbot"] h6 {
            font-family: var(--bakedbot-font-family) !important;
            color: var(--bakedbot-text-color) !important;
            margin: 0 0 10px 0 !important;
            padding: 0 !important;
            border: none !important;
            background: none !important;
            text-transform: none !important;
            letter-spacing: normal !important;
            line-height: 1.2 !important;
        }
    </style>
    <?php
}

/**
 * Shadow DOM implementation for complete CSS isolation
 * This is an alternative approach that can be used if the above methods are not sufficient
 */
function bakedbot_shadow_dom_implementation() {
    // Get options
    $options = get_option('bakedbot_chatbot_options', array());
    $primary_color = isset($options['primary_color']) ? $options['primary_color'] : '#22AD85';
    $secondary_color = isset($options['secondary_color']) ? $options['secondary_color'] : '#24504A';
    $background_color = isset($options['background_color']) ? $options['background_color'] : '#FFFFFF';
    $text_color = isset($options['text_color']) ? $options['text_color'] : '#2C2C2C';
    
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to initialize Shadow DOM
        function initBakedBotShadowDOM() {
            const container = document.getElementById('bakedbot-container');
            if (!container) return;
            
            // Check if browser supports Shadow DOM
            if (!container.attachShadow) {
                console.log('BakedBot Debug: Shadow DOM not supported in this browser');
                return;
            }
            
            // Save the original content
            const originalContent = container.innerHTML;
            container.innerHTML = '';
            
            // Create shadow root
            const shadowRoot = container.attachShadow({mode: 'open'});
            
            // Add styles to shadow DOM
            const style = document.createElement('style');
            style.textContent = `
                :host {
                    all: initial;
                    display: block;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
                    font-size: 16px;
                    line-height: 1.5;
                    color: ${<?php echo json_encode($text_color); ?>};
                    background-color: ${<?php echo json_encode($background_color); ?>};
                }
                
                * {
                    box-sizing: border-box;
                }
                
                .bakedbot-button {
                    background-color: ${<?php echo json_encode($primary_color); ?>};
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 16px;
                    cursor: pointer;
                }
                
                input[type="text"], textarea {
                    background-color: white;
                    color: ${<?php echo json_encode($text_color); ?>};
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px;
                    width: 100%;
                }
                
                a {
                    color: ${<?php echo json_encode($primary_color); ?>};
                    text-decoration: none;
                }
            `;
            shadowRoot.appendChild(style);
            
            // Create wrapper for content
            const wrapper = document.createElement('div');
            wrapper.className = 'bakedbot-shadow-wrapper';
            wrapper.innerHTML = originalContent;
            shadowRoot.appendChild(wrapper);
            
            console.log('BakedBot Debug: Shadow DOM initialized successfully');
        }
        
        // Try to initialize Shadow DOM
        try {
            initBakedBotShadowDOM();
        } catch (error) {
            console.error('BakedBot Debug: Error initializing Shadow DOM', error);
        }
    });
    </script>
    <?php
}

/**
 * Fix for the toggle button background image
 * This ensures the chat toggle button displays correctly
 */
function bakedbot_fix_toggle_button() {
    // Get plugin URL
    $plugin_url = plugin_dir_url(__FILE__);
    
    ?>
    <style id="bakedbot-toggle-button-fix">
        /* Fix the toggle button background image */
        .toggle-button {
            background: url("<?php echo esc_url($plugin_url); ?>dist/images/blunt-smokey-sm.png") no-repeat center center !important;
            background-size: contain !important;
        }
        
        /* Also fix any other elements that might use this image */
        .bb-sm-loading-icon,
        [class*="bakedbot"] .bb-sm-loading-icon,
        .bakedbot-toggle-icon {
            background: url("<?php echo esc_url($plugin_url); ?>dist/images/blunt-smokey-sm.png") no-repeat center center !important;
            background-size: contain !important;
        }
    </style>
    <?php
}


// Add the toggle button fix to WordPress head with high priority
add_action('wp_head', 'bakedbot_fix_toggle_button', 1002);
