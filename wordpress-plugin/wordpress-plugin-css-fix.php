<?php
/**
 * BakedBot Chatbot CSS Isolation
 * 
 * This file contains functions to prevent WordPress theme styles from overriding
 * the BakedBot chatbot styles. Integrate these functions into your main plugin file.
 */

/**
 * Shadow DOM implementation for complete CSS isolation
 * This is an alternative approach that can be used if the above methods are not sufficient
 */
function bakedbot_shadow_dom_implementation() {
    // Get options
    $options = get_option('bakedbot_chatbot_options', array());
    $primary_color = isset($options['primary_color']) ? $options['primary_color'] : '#22AD85';
    $secondary_color = isset($options['secondary_color']) ? $options['secondary_color'] : '#24504A';
    $background_color = isset($options['background_color']) ? $options['background_color'] : '#FFFFFF';
    $text_color = isset($options['text_color']) ? $options['text_color'] : '#2C2C2C';

    $plugin_path = plugin_dir_path(__FILE__);
    $plugin_url = plugin_dir_url(__FILE__);
    $css_urls = [];
    $css_files = glob($plugin_path . 'dist/assets/css/*.css');
    if (!empty($css_files)) {
        foreach ($css_files as $css_file) {
            $filename = basename($css_file);
            $css_urls[] = $plugin_url . 'dist/assets/css/' . $filename;
        }
    }
    
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const pluginUrl = '<?php echo esc_url($plugin_url); ?>';
        // Function to initialize Shadow DOM
        function initBakedBotShadowDOM() {
            const container = document.getElementById('bakedbot-container');
            if (!container || container.shadowRoot) return;
            
            // Check if browser supports Shadow DOM
            if (!container.attachShadow) {
                console.log('BakedBot Debug: Shadow DOM not supported in this browser');
                return;
            }
            
            // Create a new shadow root
            const shadowRoot = container.attachShadow({mode: 'open'});
            
            // Store the original content
            const originalContent = container.innerHTML;
            container.innerHTML = '';

            // Add plugin css files to shadow DOM
            const cssUrls = <?php echo json_encode($css_urls); ?>;
            cssUrls.forEach(url => {
                const linkElem = document.createElement('link');
                linkElem.setAttribute('rel', 'stylesheet');
                linkElem.setAttribute('href', url);
                shadowRoot.appendChild(linkElem);
            });
            
            // Add styles to the shadow DOM - these will not be affected by the parent page CSS
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                /* Base reset for all elements */
                *, *::before, *::after {
                    all: initial;
                    box-sizing: border-box;
                }
                
                /* Base container styling */
                :host {
                    display: block;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
                    font-size: 16px;
                    line-height: 1.5;
                    color: <?php echo esc_attr($text_color); ?>;
                }
                
                /* Comprehensive styling for all heading elements */
                h1, h2, h3, h4, h5, h6,
                [class*="text-xl"], [class*="text-lg"], [class*="text-md"], [class*="text-sm"],
                [class*="font-medium"], [class*="font-semibold"], [class*="font-bold"],
                [class*="heading"], [class*="title"], [class*="header"] {
                    font-family: inherit;
                    line-height: 1.2;
                    color: <?php echo esc_attr($text_color); ?>;
                    padding: 0;
                    border: none;
                    background: none;
                    text-transform: none;
                    letter-spacing: normal;
                    display: block;
                    text-shadow: none;
                    box-shadow: none;
                }
                
                /* Specific styling for each heading level */
                h1 {
                    font-size: 2em;
                    font-weight: 600;
                    margin: 0.67em 0;
                }
                
                h2 {
                    font-size: 1.5em;
                    font-weight: 500;
                    margin: 0.83em 0;
                }
                
                h3 {
                    font-size: 1.17em;
                    font-weight: 500;
                    margin: 1em 0;
                }
                
                h4 {
                    font-size: 1em;
                    font-weight: 500;
                    margin: 1.33em 0;
                }
                
                h5 {
                    font-size: 0.83em;
                    font-weight: 500;
                    margin: 1.67em 0;
                }
                
                h6 {
                    font-size: 0.67em;
                    font-weight: 500;
                    margin: 2.33em 0;
                }
                
                /* Custom heading component styling for better isolation */
                .bakedbot-heading {
                    display: block;
                    font-family: inherit;
                    font-weight: 500;
                    line-height: 1.2;
                    color: <?php echo esc_attr($text_color); ?>;
                    margin-block-start: 0.83em;
                    margin-block-end: 0.83em;
                    text-align: inherit;
                }
                
                /* Size variants for custom headings */
                .bakedbot-heading.text-xl {
                    font-size: 1.25rem;
                }
                
                .bakedbot-heading.text-lg {
                    font-size: 1.125rem;
                }
                
                .bakedbot-heading.text-md {
                    font-size: 1rem;
                }
                
                .bakedbot-heading.text-sm {
                    font-size: 0.875rem;
                }
                
                /* Level variants for custom headings */
                .bakedbot-heading.h1 {
                    font-size: 2em;
                    font-weight: 600;
                    margin-block-start: 0.67em;
                    margin-block-end: 0.67em;
                }
                
                .bakedbot-heading.h2 {
                    font-size: 1.5em;
                    margin-block-start: 0.83em;
                    margin-block-end: 0.83em;
                }
                
                .bakedbot-heading.h3 {
                    font-size: 1.17em;
                    margin-block-start: 1em;
                    margin-block-end: 1em;
                }
                
                .bakedbot-heading.h4 {
                    font-size: 1em;
                    margin-block-start: 1.33em;
                    margin-block-end: 1.33em;
                }
                
                .bakedbot-heading.h5 {
                    font-size: 0.83em;
                    margin-block-start: 1.67em;
                    margin-block-end: 1.67em;
                }
                
                .bakedbot-heading.h6 {
                    font-size: 0.67em;
                    margin-block-start: 2.33em;
                    margin-block-end: 2.33em;
                }
                
                /* Ensure basic elements have proper styling */
                p {
                    display: block;
                    margin: 1em 0;
                }
                
                a {
                    color: <?php echo esc_attr($primary_color); ?>;
                    text-decoration: none;
                }
                
                button {
                    cursor: pointer;
                }

                /* Fix the toggle button background image */
                .toggle-button {
                    background: url("${pluginUrl}dist/assets/images/blunt-smokey-sm.png") no-repeat center center !important;
                    background-size: contain !important;
                }
                
                /* Also fix any other elements that might use this image */
                .bb-sm-loading-icon,
                .bakedbot-toggle-icon {
                    background: url("${pluginUrl}dist/assets/images/blunt-smokey-sm.png") no-repeat center center !important;
                    background-size: contain !important;
                }
                
                /* Add fallback paths in case the image is in a different location */
                .toggle-button:not([style*="background-image"]),
                .bb-sm-loading-icon:not([style*="background-image"]),
                .bakedbot-toggle-icon:not([style*="background-image"]) {
                    background: url("${pluginUrl}dist/images/blunt-smokey-sm.png") no-repeat center center !important;
                    background-size: contain !important;
                }
                
                /* Hide background image when chatbot is opened */
                .toggle-button.open,
                .toggle-button.active,
                .toggle-button.expanded,
                .toggle-button[data-open="true"],
                .toggle-button[aria-expanded="true"] {
                    background-image: none !important;
                }
                
                /* Direct styling for the close toggle button */
                .close-toggle-button,
                .toggle-button.close-toggle-button {
                    background-image: none !important;
                    background: #00a67d !important;
                    border-radius: 50% !important;
                    width: 40px !important;
                    height: 40px !important;
                }
            `;
            shadowRoot.appendChild(styleElement);
            
            // Add the original content to the shadow DOM
            const contentContainer = document.createElement('div');
            contentContainer.classList.add('bakedbot-shadow-content');
            contentContainer.innerHTML = originalContent;
            shadowRoot.appendChild(contentContainer);
            
            console.log('BakedBot Debug: Shadow DOM initialized successfully');
        }
        
        // Initialize Shadow DOM
        initBakedBotShadowDOM();
        
        // Re-initialize on AJAX content updates
        document.addEventListener('bakedbot-content-updated', function() {
            initBakedBotShadowDOM();
        });
    });
    </script>
    <?php
}
